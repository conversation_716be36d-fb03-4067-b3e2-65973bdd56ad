import type { ExpoConfig } from 'expo/config';
import fs from 'fs';

type Variant = 'development' | 'staging' | 'production';

const info: Record<Variant, { name: string; package: string }> = {
  development: {
    name: 'Channels (development)',
    package: 'shapeconstruction.channels.development',
  },
  staging: {
    name: 'Channels (staging)',
    package: 'shapeconstruction.channels.staging',
  },
  production: {
    name: 'Channels',
    package: 'shapeconstruction.channels.production',
  },
};

export default (): ExpoConfig => {
  const ENVIRONMENT = (process.env.EXPO_PUBLIC_APP_ENV || 'development') as Variant;
  const information = info[ENVIRONMENT];

  let VERSION;
  try {
    VERSION = fs.readFileSync('.version-shape', 'utf-8').trim();
  } catch {
    VERSION = '0.0.1';
  }

  return {
    slug: 'channels',
    name: information.name,
    description: 'Channels is a workplace chat platform for the construction industry.',
    owner: 'shapeconstruction',
    version: VERSION,
    githubUrl: 'https://github.com/shape-construction/shape-frontend',
    orientation: 'portrait',
    userInterfaceStyle: 'light',
    icon: './assets/icons/icon.png',
    scheme: 'shape-channels',
    experiments: {
      typedRoutes: true,
    },
    assetBundlePatterns: ['**/*'],
    ios: {
      jsEngine: 'hermes',
      entitlements: {
        'aps-environment': 'development',
      },
      bundleIdentifier: information.package,
      supportsTablet: true,
      config: {
        usesNonExemptEncryption: false,
      },
      infoPlist: {
        LSMinimumSystemVersion: '12.0',
        NSCameraUsageDescription: 'Allow $(PRODUCT_NAME) take and send photos or videos in channels from the app.',
        UIBackgroundModes: ['fetch'],
      },
      googleServicesFile: `./services/${ENVIRONMENT}/GoogleService-Info.plist`,
      privacyManifests: {
        NSPrivacyAccessedAPITypes: [
          {
            NSPrivacyAccessedAPIType: 'NSPrivacyAccessedAPICategoryDiskSpace',
            NSPrivacyAccessedAPITypeReasons: ['E174.1'],
          },
          {
            NSPrivacyAccessedAPIType: 'NSPrivacyAccessedAPICategorySystemBootTime',
            NSPrivacyAccessedAPITypeReasons: ['8FFB.1'],
          },
          {
            NSPrivacyAccessedAPIType: 'NSPrivacyAccessedAPICategoryFileTimestamp',
            NSPrivacyAccessedAPITypeReasons: ['DDA9.1'],
          },
          {
            NSPrivacyAccessedAPIType: 'NSPrivacyAccessedAPICategoryUserDefaults',
            NSPrivacyAccessedAPITypeReasons: ['CA92.1'],
          },
        ],
      },
    },
    android: {
      jsEngine: 'hermes',
      adaptiveIcon: {
        foregroundImage: './assets/icons/android_foreground_icon.png',
        backgroundImage: './assets/icons/android_background_icon.png',
      },
      package: information.package,
      permissions: ['android.permission.CAMERA'],
      blockedPermissions: ['android.permission.USE_FULL_SCREEN_INTENT', 'android.permission.RECORD_AUDIO'],
      googleServicesFile: `./services/${ENVIRONMENT}/google-services.json`,
    },
    notification: {
      icon: './assets/icons/ic_notification.png',
      color: '#4338CA',
    },
    plugins: [
      // https://op-engineering.github.io/op-sqlite/docs/installation/#use_frameworks
      './expo-plugins/op-sqlite-plugin',
      './expo-plugins/withCustomConfig',
      'expo-router',
      'expo-secure-store',
      '@react-native-google-signin/google-signin',
      [
        'expo-updates',
        {
          username: 'shapeconstruction',
        },
      ],
      [
        'expo-build-properties',
        {
          ios: {
            useFrameworks: 'static',
          },
          android: {
            // Need this while Expo is not updated to use the latest version (53)
            // https://shape-construction.atlassian.net/browse/SE-446
            compileSdkVersion: 35,
            targetSdkVersion: 35,
            buildToolsVersion: '35.0.0',
            extraMavenRepos: ['$rootDir/../../../../../node_modules/@notifee/react-native/android/libs'],
          },
        },
      ],
      '@react-native-firebase/app',
      '@react-native-firebase/messaging',
      'expo-video',
      [
        'expo-media-library',
        {
          microphonePermission: 'Allow $(PRODUCT_NAME) to record audio in channels when capturing videos.',
          photosPermission: 'Allow $(PRODUCT_NAME) to select images from your library to send in channels.',
          savePhotosPermission: 'Allow $(PRODUCT_NAME) to save channels media to your library.',
        },
      ],
      [
        '@sentry/react-native/expo',
        {
          organization: 'shapeconstruction',
          project: 'channels',
        },
      ],
      [
        'expo-splash-screen',
        {
          backgroundColor: '#ffffff',
          image: './assets/splashscreen/splashscreen-light.png',
          resizeMode: 'contain',
        },
      ],
      'react-native-compressor',
    ],
    extra: {
      eas: {
        projectId: '3e612fc9-c09d-4fbe-9d59-a457e034522e',
      },
    },
    updates: {
      url: 'https://u.expo.dev/3e612fc9-c09d-4fbe-9d59-a457e034522e',
    },
  };
};
