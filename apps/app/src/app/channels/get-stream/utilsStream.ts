import type { DocumentViewerItem } from '@shape-construction/arch-ui/src/DocumentViewer/DocumentViewer';
import type { Attachment } from 'stream-chat';

/**
 * Extracts the identifier from a Stream asset URL.
 *
 * @param assetUrl Stream asset URL
 * @returns The asset identifier
 */
export const getIdentifierFromAssetUrl = (assetUrl: string) => {
  try {
    const url = new URL(assetUrl);
    const parts = url.pathname.split('/');
    return parts.at(-1) ?? '';
  } catch (error) {
    return '';
  }
};

/**
 * Creates a key from Stream attachment
 *
 * @param attachment Stream attachment
 * @returns The attachment identifier
 */
export const createAttachmentKey = (attachment: Attachment) => {
  const attachmentUrl = attachment?.asset_url ?? attachment?.image_url;
  if (!attachmentUrl) return '';

  return `attachment-${getIdentifierFromAssetUrl(attachmentUrl)}`;
};

/**
 * Converts the Stream attachment containing PDF
 * to a format compatible with the Document Viewer
 *
 * @param attachment Stream attachment
 * @param blobUrl Blob retrieved with Stream asset id
 * @returns PDF Document Viewer Item or null
 */
export const convertPdfAttachment = (
  attachment: Attachment | null,
  blobUrl: string | null
): DocumentViewerItem | null => {
  if (!attachment || !blobUrl) return null;

  return {
    byteSize: Number(attachment?.file_size),
    caption: null,
    contentType: attachment?.mime_type ?? '',
    createdAt: '',
    extension: 'pdf',
    exifMetadata: null,
    filename: attachment?.title ?? '',
    id: blobUrl,
    user: undefined,
    downloadUrl: blobUrl,
    inlineUrl: blobUrl,
    kind: 'file',
    location: '',
    availableActions: {
      edit: false,
      delete: false,
    },
    alt: '',
    src: '',
  };
};

/**
 * Converts the Stream attachment containing image
 * to a format compatible with the Document Viewer
 *
 * @param attachment Stream attachment
 * @returns Image Document Viewer Item
 */
export const convertImageAttachment = (attachment: Attachment): DocumentViewerItem => ({
  byteSize: Number(attachment?.file_size),
  caption: null,
  contentType: attachment?.type ?? '',
  createdAt: '',
  extension: attachment?.mime_type?.split('/')[1] ?? '',
  exifMetadata: null,
  filename: attachment?.fallback ?? '',
  id: createAttachmentKey(attachment),
  user: undefined,
  downloadUrl: attachment?.image_url ?? '',
  inlineUrl: attachment?.image_url ?? '',
  kind: 'image',
  location: '',
  availableActions: {
    edit: false,
    delete: false,
  },
  alt: '',
  src: attachment?.image_url ?? '',
});
