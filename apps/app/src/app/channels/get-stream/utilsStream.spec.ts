import { assetUrlFactory, attachmentFactory } from '@shape-construction/api/channels/factories/attachment';
import type { Factory } from '@shape-construction/api/factories';
import type { DocumentViewerItem } from '@shape-construction/arch-ui/src/DocumentViewer/DocumentViewer';
import type { Attachment } from 'stream-chat';
import {
  convertImageAttachment,
  convertPdfAttachment,
  createAttachmentKey,
  getIdentifierFromAssetUrl,
} from './utilsStream';

const documentViewerItemPdfFactory: Factory<DocumentViewerItem> = (itemProperties) => ({
  alt: '',
  availableActions: { delete: false, edit: false },
  byteSize: 1158848,
  caption: null,
  contentType: 'application/pdf',
  createdAt: '',
  downloadUrl: 'my-pdf-url',
  exifMetadata: null,
  extension: 'pdf',
  filename: '1910.10683.pdf',
  id: 'my-pdf-url',
  inlineUrl: 'my-pdf-url',
  kind: 'file',
  location: '',
  src: '',
  user: undefined,
  ...itemProperties,
});

const documentViewerItemImageFactory: Factory<DocumentViewerItem> = (itemProperties) => ({
  alt: '',
  availableActions: { delete: false, edit: false },
  byteSize: 1024,
  caption: null,
  contentType: 'image',
  createdAt: '',
  downloadUrl: 'https://example.com/image.jpg',
  exifMetadata: null,
  extension: 'jpeg',
  filename: 'test-image.jpg',
  id: 'attachment-image.jpg',
  inlineUrl: 'https://example.com/image.jpg',
  kind: 'image',
  location: '',
  src: 'https://example.com/image.jpg',
  user: undefined,
  ...itemProperties,
});

const assetUrl = assetUrlFactory().url;
const attachment = attachmentFactory();
const pdfDocumentViewer = documentViewerItemPdfFactory();

describe('utilsStream', () => {
  describe('getIdentifierFromAssetUrl', () => {
    describe('when a correct asset URL is provided', () => {
      it('returns the asset identifier', () => {
        const expectedId = 'a8a78991-5ad9-4b9f-b575-6520a47f0a36.1910.10683.pdf';

        expect(getIdentifierFromAssetUrl(assetUrl)).toEqual(expectedId);
      });
    });

    describe('when an invalid asset URL is passed', () => {
      it('returns the empty string', () => {
        expect(getIdentifierFromAssetUrl('10683.pdf?Key-Pair-Id=KP&Policy=PO_&Signature=SIG')).toEqual('');
      });
    });
  });

  describe('createAttachmentKey', () => {
    describe('when a correct attachment with asset_url is provided', () => {
      it('returns the attachment key', () => {
        const expectedKey = 'attachment-a8a78991-5ad9-4b9f-b575-6520a47f0a36.1910.10683.pdf';

        expect(createAttachmentKey(attachment)).toEqual(expectedKey);
      });
    });

    describe('when an attachment with only image_url is provided', () => {
      it('returns the attachment key using image_url', () => {
        const imageAttachment: Attachment = {
          type: 'image',
          image_url:
            'https://dublin.stream-io-cdn.com/1279012/images/abc123.image.jpg?Key-Pair-Id=KP&Policy=PO_&Signature=SIG',
          file_size: 1024,
          mime_type: 'image/jpeg',
        };
        const expectedKey = 'attachment-abc123.image.jpg';

        expect(createAttachmentKey(imageAttachment)).toEqual(expectedKey);
      });
    });

    describe('when an attachment has both asset_url and image_url', () => {
      it('prioritizes asset_url over image_url', () => {
        const attachmentWithBoth: Attachment = {
          type: 'file',
          asset_url:
            'https://dublin.stream-io-cdn.com/1279012/attachments/def456.file.pdf?Key-Pair-Id=KP&Policy=PO_&Signature=SIG',
          image_url:
            'https://dublin.stream-io-cdn.com/1279012/images/ghi789.image.jpg?Key-Pair-Id=KP&Policy=PO_&Signature=SIG',
          file_size: 2048,
          mime_type: 'application/pdf',
        };
        const expectedKey = 'attachment-def456.file.pdf';

        expect(createAttachmentKey(attachmentWithBoth)).toEqual(expectedKey);
      });
    });

    describe('when an attachment has no asset_url or image_url', () => {
      it('returns the empty string', () => {
        const emptyAttachment: Attachment = {
          title: '',
          asset_url: undefined,
          image_url: undefined,
          file_size: undefined,
        };

        expect(createAttachmentKey(emptyAttachment)).toEqual('');
      });
    });

    describe('when an attachment has empty string URLs', () => {
      it('returns the empty string', () => {
        const emptyUrlAttachment: Attachment = {
          title: '',
          asset_url: '',
          image_url: '',
          file_size: undefined,
        };

        expect(createAttachmentKey(emptyUrlAttachment)).toEqual('');
      });
    });
  });

  describe('convertPdfAttachment', () => {
    describe('when a correct asset URL is provided', () => {
      it('returns the document viewer item for PDF', () => {
        expect(convertPdfAttachment(attachment, 'my-pdf-url')).toEqual(pdfDocumentViewer);
      });
    });

    describe('when the asset URL is not passed', () => {
      it('returns the null value', () => {
        expect(convertPdfAttachment(attachment, '')).toEqual(null);
      });
    });
  });

  describe('convertImageAttachment', () => {
    describe('when a valid image attachment is provided', () => {
      it('returns the document viewer item for image', () => {
        const imageAttachment: Attachment = {
          type: 'image',
          image_url: 'https://example.com/image.jpg',
          file_size: 1024,
          mime_type: 'image/jpeg',
          fallback: 'test-image.jpg',
        };

        const expectedResult = documentViewerItemImageFactory();

        expect(convertImageAttachment(imageAttachment)).toEqual(expectedResult);
      });
    });

    describe('when an image attachment with missing optional fields is provided', () => {
      it('returns the document viewer item with default values', () => {
        const minimalImageAttachment: Attachment = {
          type: 'image',
          image_url: 'https://example.com/image.png',
        };

        const result = convertImageAttachment(minimalImageAttachment);

        expect(result.byteSize).toBeNaN();
        expect(result.contentType).toBe('image');
        expect(result.extension).toBe('');
        expect(result.filename).toBe('');
        expect(result.downloadUrl).toBe('https://example.com/image.png');
        expect(result.kind).toBe('image');
        expect(result.availableActions).toEqual({ edit: false, delete: false });
      });
    });
  });
});
