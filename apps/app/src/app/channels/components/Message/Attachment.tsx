import React from 'react';
import { cn } from '@shape-construction/arch-ui';
import { useSetAtom } from 'jotai';
import type { Attachment as StreamAttachment } from 'stream-chat';
import { Attachment as DefaultAttachment, useMessageContext } from 'stream-chat-react';
import { createAttachmentKey } from '../../get-stream/utilsStream';
import { Gallery } from '../Gallery/Gallery';
import { ImageComponent } from '../Gallery/Image';
import { isPdfViewerOpenAtom, selectedPdfAtom } from './MessagePdfPreview';

type StreamAttachments = StreamAttachment[];

type AttachmentProps = {
  attachments: StreamAttachments;
};

export const Attachment = ({ attachments }: AttachmentProps) => {
  const { handleAction } = useMessageContext();
  const setSelectedPdf = useSetAtom(selectedPdfAtom);
  const setIsPdfViewerOpen = useSetAtom(isPdfViewerOpenAtom);

  const handlePdfClick = (attachment: StreamAttachment) => {
    setSelectedPdf(attachment);
    setIsPdfViewerOpen(true);
  };

  const renderPdfAttachments = () => {
    return attachments
      .filter((item) => item.type === 'pdf' || item.mime_type === 'application/pdf')
      .map((item) => (
        <button type="button" key={createAttachmentKey(item)} onClick={() => handlePdfClick(item)} className="w-full">
          <DefaultAttachment actionHandler={handleAction} attachments={[item]} />
        </button>
      ));
  };

  const renderDefaultAttachments = () => {
    const mediaAttachments = attachments.filter(
      (attachment) => !(attachment.type === 'pdf' || attachment.mime_type === 'application/pdf')
    );

    if (mediaAttachments.length === 0) return null;

    return (
      <DefaultAttachment
        Gallery={Gallery}
        Image={ImageComponent}
        actionHandler={handleAction}
        attachments={mediaAttachments}
      />
    );
  };

  if (!attachments?.length) return null;

  return (
    <div className={cn('px-1', { 'max-w-[476px]': attachments?.length })}>
      {renderPdfAttachments()}
      {renderDefaultAttachments()}
    </div>
  );
};
