import React from 'react';
import type { DocumentViewerItem } from '@shape-construction/arch-ui/src/DocumentViewer/DocumentViewer';
import { convertImageAttachment } from 'app/channels/get-stream/utilsStream';
import type { Attachment } from 'stream-chat';
import { render, screen, userEvent } from 'tests/test-utils';
import { ModalGallery } from './ModalGallery';

jest.mock('app/channels/get-stream/utilsStream', () => ({
  ...jest.requireActual('app/channels/get-stream/utilsStream'),
  convertImageAttachment: jest.fn(),
}));

const images: Attachment[] = Array.from({ length: 3 }, () => ({
  original: 'https://placeimg.com/640/480/any',
  originalAlt: 'User uploaded content',
  src: 'https://placeimg.com/640/480/any',
}));

const convertImageAttachmentMocked = jest.mocked(convertImageAttachment);

describe('<ModalGallery />', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    convertImageAttachmentMocked.mockImplementation((image: Attachment) => {
      return {
        ...image,
        availableActions: {
          edit: false,
          delete: false,
        },
        byteSize: 0,
        caption: null,
        contentType: '',
        createdAt: '',
        extension: '',
        exifMetadata: null,
        filename: '',
        id: 'image-id',
        downloadUrl: '',
        inlineUrl: '',
        kind: 'image',
        location: '',
        src: '',
        alt: '',
      } satisfies DocumentViewerItem;
    });
  });

  describe('when isOpen is false', () => {
    it('renders nothing', () => {
      render(<ModalGallery images={images} isOpen={false} onClose={jest.fn()} />);

      expect(screen.queryByTestId('lightbox-container')).not.toBeInTheDocument();
    });
  });

  describe('when isOpen is true', () => {
    it('renders the DocumentViewer', () => {
      render(<ModalGallery images={images} isOpen={true} onClose={jest.fn()} />);

      expect(screen.getByTestId('lightbox-container')).toBeInTheDocument();
    });

    it('converts images to DocumentViewer format', () => {
      render(<ModalGallery images={images} isOpen={true} onClose={jest.fn()} />);

      expect(convertImageAttachmentMocked).toHaveBeenCalledTimes(images.length);
    });

    describe('when close button is clicked', () => {
      it('calls onClose', async () => {
        const onCloseMock = jest.fn();
        render(<ModalGallery images={images} isOpen={true} onClose={onCloseMock} />);

        await userEvent.click(screen.getByLabelText('Close document viewer'));

        expect(onCloseMock).toHaveBeenCalledTimes(1);
      });
    });
  });
});
