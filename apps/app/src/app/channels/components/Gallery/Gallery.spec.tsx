import React from 'react';
import { nanoid } from 'nanoid';
import { Channel } from 'stream-chat-react';
import { render, screen, userEvent } from 'tests/test-utils';
import { Gallery } from './Gallery';

const mockGalleryAssets = [
  {
    original: 'https://placeimg.com/640/480/any',
    originalAlt: 'User uploaded content',
    src: 'https://placeimg.com/640/480/any',
  },
  {
    original: 'https://placeimg.com/640/480/any',
    originalAlt: 'User uploaded content',
    src: 'https://placeimg.com/640/480/any',
  },
  {
    original: 'https://placeimg.com/640/480/any',
    originalAlt: 'User uploaded content',
    src: 'https://placeimg.com/640/480/any',
  },
  {
    original: 'https://placeimg.com/640/480/any',
    originalAlt: 'User uploaded content',
    src: 'https://placeimg.com/640/480/any',
  },
  {
    original: 'https://placeimg.com/640/480/any',
    originalAlt: 'User uploaded content',
    src: 'https://placeimg.com/640/480/any',
  },
];

describe('<Gallery />', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders component with 2 images correctly', () => {
    render(<Gallery images={mockGalleryAssets.slice(0, 2)} />);

    expect(screen.getAllByTestId('gallery-image')).toHaveLength(2);
    expect(screen.queryByText('gallery-image-last')).not.toBeInTheDocument();
    expect(screen.queryByText('{{ imageCount }} more')).not.toBeInTheDocument();
  });

  it('renders component with 3 images correctly', () => {
    render(<Gallery images={mockGalleryAssets.slice(0, 3)} />);

    expect(screen.getAllByTestId('gallery-image')).toHaveLength(3);
    expect(screen.queryByText('gallery-image-last')).not.toBeInTheDocument();
    expect(screen.queryByText('{{ imageCount }} more')).not.toBeInTheDocument();
  });

  it('renders component with 4 images correctly', () => {
    render(<Gallery images={mockGalleryAssets.slice(0, 4)} />);

    expect(screen.getAllByTestId('gallery-image')).toHaveLength(4);
    expect(screen.queryByText('gallery-image-last')).not.toBeInTheDocument();
    expect(screen.queryByText('{{ imageCount }} more')).not.toBeInTheDocument();
  });

  it('renders component with 5 images correctly', () => {
    render(<Gallery images={mockGalleryAssets} />);

    expect(screen.getAllByTestId('gallery-image')).toHaveLength(3);
    expect(screen.getByTestId('gallery-image-last')).toBeInTheDocument();
    expect(screen.getByText('{{ imageCount }} more')).toBeInTheDocument();
  });

  it('opens modal on image click', async () => {
    render(<Gallery images={mockGalleryAssets.slice(0, 1)} />);

    await userEvent.click(screen.getByTestId('gallery-image'));

    expect(await screen.findByTestId('lightbox-container')).toBeInTheDocument();
  });

  it('renders custom ModalGallery component from context', async () => {
    const galleryContent = nanoid();
    const CustomGallery = () => <div>{galleryContent}</div>;
    render(
      <Channel ModalGallery={CustomGallery}>
        <Gallery images={mockGalleryAssets} />
      </Channel>
    );

    await userEvent.click((await screen.findAllByTestId('gallery-image'))[0]);

    expect(await screen.findByText(galleryContent)).toBeInTheDocument();
  });

  it('renders custom BaseImage component from context', async () => {
    const CustomBaseImage = () => <img alt="custom base" data-testid={'custom-base-image'} />;
    render(
      <Channel BaseImage={CustomBaseImage}>
        <Gallery images={mockGalleryAssets} />
      </Channel>
    );

    expect(await screen.findAllByTestId('custom-base-image')).toHaveLength(3);
    expect(screen.getByTestId('gallery-image-last')).toBeInTheDocument();
    expect(screen.getByText('{{ imageCount }} more')).toBeInTheDocument();
  });
});
