import React, { useCallback, useMemo, useState } from 'react';
import 'react-image-gallery/styles/css/image-gallery.css';

import { useMessageGetter } from '@messageformat/react';
import { DocumentViewer } from '@shape-construction/arch-ui/src/DocumentViewer/DocumentViewer';
import { convertImageAttachment } from 'app/channels/get-stream/utilsStream';
import type { Attachment } from 'stream-chat';

export type ModalGalleryProps = {
  images: Attachment[];
  index?: number;
  isOpen: boolean;
  onClose: () => void;
};

export const ModalGallery = (props: ModalGalleryProps) => {
  const actionMessages = useMessageGetter('actions');
  const pdfMessages = useMessageGetter('mediaGallery.pdf');
  const { images, index: initialIndex, isOpen, onClose } = props;
  const documentsConverted = useMemo(() => images.map((image) => convertImageAttachment(image)), [images]);
  const [initialSelectedDocumentId, setInitialSelectedDocumentId] = useState<string | undefined>(undefined);

  const handleNavigateDocument = useCallback(
    (documentIndex: number) => {
      const invalidIndex = documentIndex < 0 || documentIndex >= documentsConverted.length;
      if (invalidIndex) return;

      setInitialSelectedDocumentId(documentsConverted[documentIndex].id);
    },
    [onClose]
  );

  const selectedDocumentId = initialSelectedDocumentId ?? documentsConverted[initialIndex ?? 0]?.id;

  return (
    <DocumentViewer
      isOpen={isOpen}
      documents={documentsConverted}
      onClose={() => {
        // @note: This avoids the initial slide animation
        setInitialSelectedDocumentId(undefined);
        onClose();
      }}
      selectedDocumentId={selectedDocumentId}
      onNavigate={handleNavigateDocument}
      expandCaptionLabel={actionMessages('seeMore')}
      collapseCaptionLabel={actionMessages('seeLess')}
      pdfUploadErrorTitle={pdfMessages('uploadError.title')}
      pdfUploadErrorDescription={pdfMessages('uploadError.description')}
      pdfNotSupportedLabel={pdfMessages('notSupported.title')}
      pdfNotSupportedLink={pdfMessages('notSupported.link')}
    />
  );
};
