import React from 'react';
import { Channel, type ImageProps } from 'stream-chat-react';
import { render, screen, userEvent } from 'tests/test-utils';
import { ImageComponent } from './Image';

const imageMock: ImageProps = {
  image_url: 'https://placeimg.com/640/480/any',
  thumb_url: 'https://placeimg.com/640/480/any',
  previewUrl: 'https://placeimg.com/640/480/any',
};

describe('<Image />', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders custom BaseImage component', async () => {
    const CustomBaseImage = () => <img alt="custom base" data-testid={'custom-base-image'} />;
    render(
      <Channel BaseImage={CustomBaseImage}>
        <ImageComponent {...imageMock} />
      </Channel>
    );

    expect(await screen.findByTestId('custom-base-image')).toBeInTheDocument();
  });

  describe('ImageComponent URL sanitization', () => {
    it('prevents javascript protocol in image src', () => {
      const xssJavascriptUri = 'javascript:alert("p0wn3d")';
      render(<ImageComponent image_url={xssJavascriptUri} />);

      expect(screen.getByTestId('image-test')).not.toHaveAttribute('src', xssJavascriptUri);
    });

    it('prevents javascript protocol in thumbnail src', () => {
      const xssJavascriptUri = 'javascript:alert("p0wn3d")';
      render(<ImageComponent thumb_url={xssJavascriptUri} />);

      expect(screen.getByTestId('image-test')).not.toHaveAttribute('src', xssJavascriptUri);
    });

    it('prevents dataUris in image src', () => {
      const xssDataUri = 'data:image/svg+xml;base64,DANGEROUSENCODEDSVG';
      render(<ImageComponent image_url={xssDataUri} />);

      expect(screen.getByTestId('image-test')).not.toHaveAttribute('src', xssDataUri);
    });

    it('prevents dataUris in thumb src', () => {
      const xssDataUri = 'data:image/svg+xml;base64,DANGEROUSENCODEDSVG';
      render(<ImageComponent thumb_url={xssDataUri} />);

      expect(screen.getByTestId('image-test')).not.toHaveAttribute('src', xssDataUri);
    });
  });

  it('opens modal on image click', async () => {
    render(<ImageComponent {...imageMock} />);

    await userEvent.click(screen.getByTestId('image-test'));

    expect(await screen.findByTestId('lightbox-container')).toBeInTheDocument();
  });
});
