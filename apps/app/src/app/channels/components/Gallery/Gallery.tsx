import type { CSSProperties, MutableRefObject } from 'react';
import React, { useState } from 'react';
import { sanitizeUrl } from '@braintree/sanitize-url';
import clsx from 'clsx';
import type { Attachment } from 'stream-chat';
import { BaseImage as DefaultBaseImage, useComponentContext, useTranslationContext } from 'stream-chat-react';
import { ModalGallery as DefaultModalGallery } from './ModalGallery';

export type GalleryProps = {
  images: ((
    | {
        image_url?: string;
        thumb_url?: string;
      }
    | Attachment
  ) & { previewUrl?: string; style?: CSSProperties })[];
  innerRefs?: MutableRefObject<(HTMLElement | null)[]>;
};

const UnMemoizedGallery = ({ images, innerRefs }: GalleryProps) => {
  const [index, setIndex] = useState(0);
  const [modalOpen, setModalOpen] = useState(false);

  const { BaseImage = DefaultBaseImage, ModalGallery = DefaultModalGallery } = useComponentContext('Gallery');
  const { t } = useTranslationContext('Gallery');

  const imageFallbackTitle = t('User uploaded content');

  const countImagesDisplayedInPreview = 4;
  const lastImageIndexInPreview = countImagesDisplayedInPreview - 1;

  const toggleModal = (selectedIndex: number) => {
    if (modalOpen) {
      setModalOpen(false);
    } else {
      setIndex(selectedIndex);
      setModalOpen(true);
    }
  };

  const renderImages = images.slice(0, countImagesDisplayedInPreview).map((image, i) => {
    const isLastImage = i === lastImageIndexInPreview && images.length > countImagesDisplayedInPreview;

    if (isLastImage) {
      return (
        <button
          className="str-chat__gallery-placeholder"
          data-testid="gallery-image-last"
          key={`gallery-image-${image.image_url}`}
          onClick={() => toggleModal(i)}
          style={{
            backgroundImage: `url(${
              images[lastImageIndexInPreview].previewUrl ??
              images[lastImageIndexInPreview].image_url ??
              images[lastImageIndexInPreview].thumb_url
            })`,
            ...image.style,
          }}
          {...(innerRefs?.current && {
            ref: (r) => {
              innerRefs.current[i] = r;
            },
          })}
        >
          <p>
            {t('{{ imageCount }} more', {
              imageCount: images.length - countImagesDisplayedInPreview,
            })}
          </p>
        </button>
      );
    }

    return (
      <button
        type="button"
        className="str-chat__gallery-image"
        data-testid="gallery-image"
        key={`gallery-image-${image.image_url}`}
        onClick={() => toggleModal(i)}
      >
        <BaseImage
          alt={(image as Attachment)?.fallback ?? imageFallbackTitle}
          src={sanitizeUrl(image.previewUrl ?? image.image_url ?? image.thumb_url)}
          style={image.style}
          title={(image as Attachment)?.fallback ?? imageFallbackTitle}
          {...(innerRefs?.current && {
            ref: (r) => {
              innerRefs.current[i] = r;
            },
          })}
        />
      </button>
    );
  });

  const className = clsx('str-chat__gallery', {
    'str-chat__gallery--square': images.length > lastImageIndexInPreview,
    'str-chat__gallery-two-rows': images.length > 2,
  });

  return (
    <div className={className}>
      {renderImages}
      <ModalGallery
        isOpen={modalOpen}
        images={images}
        index={index}
        onClose={() => setModalOpen((modalOpen) => !modalOpen)}
      />
    </div>
  );
};

/**
 * Displays images in a simple responsive grid with a light box to view the images.
 */
export const Gallery = React.memo(UnMemoizedGallery) as typeof UnMemoizedGallery;
