import React from 'react';
import MessageFormat from '@messageformat/core';
import { sharedCursorPaginationMetaFactory } from '@shape-construction/api/factories/sharedCursorPagination';
import { shiftReportsListItemFactory } from '@shape-construction/api/factories/shiftReports';
import { teamMemberFactory } from '@shape-construction/api/factories/team-member';
import { getApiProjectsProjectIdDashboardsDataHealthRecordsShiftReportsMockHandler } from '@shape-construction/api/handlers-factories/projects/data-book';
import listFormatter from 'libs/i18n/message-format/formatters/listFormatter';
import { server } from 'tests/mock-server';
import { getTableData, render, screen } from 'tests/test-utils';
import { ShiftReportDetailsTable } from './ShiftReportDetailsTable';

describe('<ShiftReportDetailsTable />', () => {
  it('displays a table of shift reports', async () => {
    const teamMember = teamMemberFactory();
    const shiftReports = [
      shiftReportsListItemFactory({ id: '1' }),
      shiftReportsListItemFactory({
        reportTitle: 'My report title',
        reportDate: '2024-12-01',
        shiftType: 'Day shift',
        completionQualityScore: 42,
        documentCount: 12,
      }),
      shiftReportsListItemFactory({ id: '3' }),
    ];
    server.use(
      getApiProjectsProjectIdDashboardsDataHealthRecordsShiftReportsMockHandler(() => ({
        entries: shiftReports,
        meta: sharedCursorPaginationMetaFactory({ total: shiftReports.length }),
      }))
    );

    render(<ShiftReportDetailsTable teamMemberId={teamMember.id} startDate="2024-12-15" endDate="2024-12-22" />);

    const shiftReportRows = (await screen.findAllByRole('row')).filter((row) => row.closest('tbody'));
    expect(shiftReportRows).toHaveLength(3);
    const tableData = getTableData(await screen.findByRole('table'));
    expect(tableData[1]).toMatchObject([
      [expect.stringMatching(/.*\.headers\.document$/), 'My report title'],
      [expect.stringMatching(/.*\.headers\.date$/), '01-Dec-2024'],
      [expect.stringMatching(/.*\.headers\.shift$/), 'Day shift'],
      [expect.stringMatching(/.*\.headers\.attachments$/), '12'],
      [expect.stringMatching(/.*\.headers\.score$/), 'dataBook.page.heatmapDashboard.heatmap.qualityLabel.good'],
    ]);
  });

  describe('when a shift report does not have a title', () => {
    it('displays report with a default title', async () => {
      const teamMember = teamMemberFactory();
      const shiftReports = [shiftReportsListItemFactory({ id: '1', reportTitle: undefined, reportDate: '2024-12-01' })];
      server.use(
        getApiProjectsProjectIdDashboardsDataHealthRecordsShiftReportsMockHandler(() => ({
          entries: shiftReports,
          meta: sharedCursorPaginationMetaFactory({ total: shiftReports.length }),
        }))
      );
      const messageFormat = new MessageFormat('en', { customFormatters: { list: listFormatter } });

      render(<ShiftReportDetailsTable teamMemberId={teamMember.id} startDate="2024-12-15" endDate="2024-12-22" />, {
        messages: {
          dataBook: {
            page: {
              heatmapDashboard: {
                performanceDetails: {
                  shiftReportsTable: { reportTitle: messageFormat.compile('Report {reportDate}') },
                },
              },
            },
          },
        },
      });

      expect(await screen.findByRole('cell', { name: 'Report 2024-12-01' })).toBeInTheDocument();
    });
  });

  describe('when the total number of results is 0', () => {
    it('renders the empty state', async () => {
      const teamMember = teamMemberFactory();
      server.use(
        getApiProjectsProjectIdDashboardsDataHealthRecordsShiftReportsMockHandler(() => ({
          entries: [],
          meta: sharedCursorPaginationMetaFactory({ total: 0 }),
        }))
      );

      render(<ShiftReportDetailsTable teamMemberId={teamMember.id} startDate="2024-12-15" endDate="2024-12-22" />);

      expect(
        await screen.findByText('dataBook.page.heatmapDashboard.performanceDetails.shiftReportsTable.emptyState.title')
      ).toBeInTheDocument();
      expect(
        await screen.findByText('dataBook.page.heatmapDashboard.performanceDetails.shiftReportsTable.emptyState.body')
      ).toBeInTheDocument();
    });
  });

  describe('when there is no next page', () => {
    it('does not render the next button', async () => {
      const teamMember = teamMemberFactory();
      server.use(
        getApiProjectsProjectIdDashboardsDataHealthRecordsShiftReportsMockHandler(() => ({
          entries: [shiftReportsListItemFactory()],
          meta: sharedCursorPaginationMetaFactory({
            total: 1,
            hasPreviousPage: true,
            hasNextPage: false,
          }),
        }))
      );

      render(<ShiftReportDetailsTable teamMemberId={teamMember.id} startDate="2024-12-15" endDate="2024-12-22" />);

      expect(
        await screen.findByRole('button', {
          name: 'dataBook.page.heatmapDashboard.performanceDetails.shiftReportsTable.next',
        })
      ).not.toBeEnabled();
    });
  });

  describe('when there is next page', () => {
    it('renders the next button', async () => {
      const teamMember = teamMemberFactory();
      server.use(
        getApiProjectsProjectIdDashboardsDataHealthRecordsShiftReportsMockHandler(() => ({
          entries: [shiftReportsListItemFactory()],
          meta: sharedCursorPaginationMetaFactory({
            total: 1,
            hasNextPage: true,
            lastEntryCursor: 'last-cursor',
          }),
        }))
      );

      render(<ShiftReportDetailsTable teamMemberId={teamMember.id} startDate="2024-12-15" endDate="2024-12-22" />);

      expect(
        await screen.findByRole('button', {
          name: 'dataBook.page.heatmapDashboard.performanceDetails.shiftReportsTable.next',
        })
      ).toBeEnabled();
    });
  });

  describe('when there is no previous page', () => {
    it('does not render the previous button', async () => {
      const teamMember = teamMemberFactory();
      server.use(
        getApiProjectsProjectIdDashboardsDataHealthRecordsShiftReportsMockHandler(() => ({
          entries: [shiftReportsListItemFactory()],
          meta: sharedCursorPaginationMetaFactory({
            total: 1,
            hasPreviousPage: false,
            hasNextPage: true,
            lastEntryCursor: 'last-cursor',
          }),
        }))
      );

      render(<ShiftReportDetailsTable teamMemberId={teamMember.id} startDate="2024-12-15" endDate="2024-12-22" />);

      expect(
        await screen.findByRole('button', {
          name: 'dataBook.page.heatmapDashboard.performanceDetails.shiftReportsTable.previous',
        })
      ).not.toBeEnabled();
    });
  });

  describe('when there is no pagination', () => {
    it('does not render the footer', () => {
      const teamMember = teamMemberFactory();
      server.use(
        getApiProjectsProjectIdDashboardsDataHealthRecordsShiftReportsMockHandler(() => ({
          entries: [shiftReportsListItemFactory()],
          meta: sharedCursorPaginationMetaFactory({
            total: 1,
            hasNextPage: false,
            hasPreviousPage: false,
          }),
        }))
      );

      render(<ShiftReportDetailsTable teamMemberId={teamMember.id} startDate="2024-12-15" endDate="2024-12-22" />);

      expect(
        screen.queryByRole('button', {
          name: 'dataBook.page.heatmapDashboard.performanceDetails.shiftReportsTable.previous',
        })
      ).not.toBeInTheDocument();
      expect(
        screen.queryByRole('button', {
          name: 'dataBook.page.heatmapDashboard.performanceDetails.shiftReportsTable.next',
        })
      ).not.toBeInTheDocument();
    });
  });

  describe('when there is a previous page', () => {
    it('renders the previous button', async () => {
      const teamMember = teamMemberFactory();
      server.use(
        getApiProjectsProjectIdDashboardsDataHealthRecordsShiftReportsMockHandler(() => ({
          entries: [shiftReportsListItemFactory()],
          meta: sharedCursorPaginationMetaFactory({
            total: 1,
            hasPreviousPage: true,
            firstEntryCursor: 'last-cursor',
          }),
        }))
      );

      render(<ShiftReportDetailsTable teamMemberId={teamMember.id} startDate="2024-12-15" endDate="2024-12-22" />);

      expect(
        await screen.findByRole('button', {
          name: 'dataBook.page.heatmapDashboard.performanceDetails.shiftReportsTable.previous',
        })
      ).toBeEnabled();
    });
  });
});
