import React from 'react';
import { act, render, renderHook, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useContentEditable } from './useContentEditable';

type InputProps = React.ComponentProps<'input'>;

describe('useContentEditable', () => {
  it('exports attributes for editable element', () => {
    const { result } = renderHook(() => useContentEditable());
    const editableProps = result.current({ onSubmit: jest.fn() });

    expect(editableProps).toEqual({
      contentEditable: true,
      role: 'textbox',
      suppressContentEditableWarning: true,
      tabIndex: 0,
      onBlur: expect.any(Function),
      onKeyDown: expect.any(Function),
    });
  });

  it('triggers onSubmit when an element loses focus', () => {
    const { result } = renderHook(() => useContentEditable());
    const onSubmit = jest.fn();
    const editableProps = result.current({ onSubmit });
    const event = {
      currentTarget: { textContent: 'content!', blur: jest.fn() },
    } as unknown as React.FocusEvent<HTMLInputElement, Element>;

    act(() => {
      editableProps!.onBlur!(event);
    });

    expect(onSubmit).toHaveBeenCalledWith('content!');
  });

  it('triggers onSubmit when return key is pressed', () => {
    const { result } = renderHook(() => useContentEditable());
    const onSubmit = jest.fn();
    const editableProps = result.current({ onSubmit });
    const event = {
      currentTarget: { textContent: 'content!', blur: jest.fn() },
      key: 'Enter',
      preventDefault: jest.fn(),
      stopPropagation: jest.fn(),
    } as unknown as React.KeyboardEvent<HTMLInputElement>;

    act(() => {
      editableProps!.onKeyDown!(event);
    });

    expect(onSubmit).toHaveBeenCalledWith('content!');
  });

  it('does not allow to exceed the max length', () => {
    const { result } = renderHook(() => useContentEditable());
    const onSubmit = jest.fn();
    const editableProps = result.current({ onSubmit, maxLength: 4 });
    const event = {
      currentTarget: { textContent: 'this is a very long text', blur: jest.fn() },
      key: 'Enter',
      preventDefault: jest.fn(),
      stopPropagation: jest.fn(),
    } as unknown as React.KeyboardEvent<HTMLInputElement>;

    act(() => {
      editableProps!.onKeyDown!(event);
    });

    expect(onSubmit).toHaveBeenCalledWith('this');
  });

  it('triggers onSubmit once when Enter is pressed', () => {
    const { result } = renderHook(() => useContentEditable());
    const onSubmit = jest.fn();
    const editableProps = result.current({ onSubmit, maxLength: 4 });

    const event = {
      currentTarget: {
        textContent: 'content!',
        blur() {
          editableProps.onBlur?.(event as unknown as React.FocusEvent<HTMLInputElement, Element>);
        },
      },
      key: 'Enter',
      preventDefault: jest.fn(),
      stopPropagation: jest.fn(),
    } as unknown as React.KeyboardEvent<HTMLInputElement>;

    act(() => {
      editableProps!.onKeyDown!(event);
    });

    expect(onSubmit).toHaveBeenCalledTimes(1);
  });

  describe('when Escape key is pressed', () => {
    it('revert the value to the defaultValue', async () => {
      const onSubmit = jest.fn();
      const TestComponent = () => {
        const getContentEditableProps = useContentEditable();
        const inputProps: InputProps = getContentEditableProps({
          value: 'initial content',
          defaultValue: 'initial content',
          onSubmit,
        });
        return <span data-testid="input" {...inputProps} />;
      };

      render(<TestComponent />);

      const input = screen.getByTestId('input');
      userEvent.click(input);
      userEvent.keyboard(' (edited){Escape}');

      await waitFor(() => {
        expect(onSubmit).toHaveBeenCalledWith('initial content');
      });
    });
  });
});
