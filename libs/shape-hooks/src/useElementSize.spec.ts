import { useRef } from 'react';
import { renderHook } from '@testing-library/react';
import { useElementSize } from './useElementSize';

describe('useElementSize', () => {
  it('returns initial size (0, 0) when ref null is passed', () => {
    const { result } = renderHook(() => {
      const ref = useRef(null);
      return useElementSize(ref);
    });

    expect(result.current).toEqual({ width: 0, height: 0 });
  });

  it('retrieves an element size correctly', () => {
    const divElement = document.createElement('div');
    Object.defineProperty(divElement, 'offsetWidth', { configurable: true, value: 100 });
    Object.defineProperty(divElement, 'offsetHeight', { configurable: true, value: 200 });

    const { result } = renderHook(() => {
      const ref = useRef<HTMLElement>(divElement);
      return useElementSize(ref);
    });

    expect(result.current).toEqual({ width: 100, height: 200 });
  });
});
