import React from 'react';
import type { Meta } from '@storybook/react-vite';
import { InputPassword } from './InputPassword';

export default {
  title: 'Input/InputPassword',
  component: InputPassword,
} as Meta<typeof InputPassword>;

export const Standard = {
  args: {
    label: 'Example',
    name: 'example',
    id: 'example',
    required: true,
  },
};

export const WithValue = {
  args: {
    label: 'Example',
    name: 'example',
    id: 'example',
    value: 'This is my value!',
  },
};

export const WithVisibilityToggle = {
  args: {
    label: 'Example',
    name: 'example',
    id: 'example',
    value: 'This is my value!',
    showVisibilityToggle: true,
  },
};

export const WithVisibilityToggleAndError = {
  args: {
    label: 'Example',
    name: 'example',
    id: 'example',
    value: 'This is my value!',
    showVisibilityToggle: true,
    error: 'This field has an error message!',
  },
};

export const WithError = {
  args: {
    label: 'Example',
    name: 'example',
    id: 'example',
    error: 'This field has an error message!',
  },
};

export const Disabled = {
  args: {
    label: 'Example',
    name: 'example',
    id: 'example',
    value: 'This is my value!',
    disabled: true,
  },
};

export const WithCornerAdornment = {
  args: {
    label: 'Example',
    name: 'example',
    id: 'example',
    value: 'This is my value!',
    cornerAdornment: <>Corner Adornment</>,
  },
};

export const WithFullWidth = {
  args: {
    ...Standard.args,
    fullWidth: true,
  },
};
