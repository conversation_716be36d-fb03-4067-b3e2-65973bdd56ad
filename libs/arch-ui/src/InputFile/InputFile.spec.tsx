import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { InputFile } from './InputFile';

describe('InputFile', () => {
  it('renders the label', () => {
    render(<InputFile label="Example" />);
    expect(screen.getByText('Example')).toBeInTheDocument();
  });

  it('renders the start adornment', () => {
    render(<InputFile startAdornment={<>start ardornment</>} />);
    expect(screen.getByLabelText('start ardornment')).toBeInTheDocument();
  });

  it('invoke onChange when upload a file', async () => {
    const onChangeMock = jest.fn();
    const file = new File(['photo'], 'filename.png', { type: 'image/png' });
    render(<InputFile onChange={onChangeMock} aria-label="image upload" startAdornment={<>start ardornment</>} />);

    await userEvent.upload(screen.getByLabelText('image upload'), file);

    expect(onChangeMock).toHaveBeenCalled();
  });
});
