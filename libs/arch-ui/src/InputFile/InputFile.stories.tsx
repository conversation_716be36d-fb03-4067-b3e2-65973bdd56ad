import React from 'react';
import type { Meta } from '@storybook/react-vite';
import { CameraIcon } from '../Icons/solid';
import { InputFile } from './InputFile';

export default {
  title: 'Input/InputFile',
  component: InputFile,
} as Meta<typeof InputFile>;

export const Default = {
  args: {
    label: 'Upload file',
  },
};

export const WithStartAdornment = {
  args: {
    label: 'Upload file',
    startAdornment: <CameraIcon className="h-5 w-5 text-gray-900" />,
  },
};
