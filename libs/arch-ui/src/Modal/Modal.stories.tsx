import React, { useEffect, useState } from 'react';
import type { Meta, StoryObj } from '@storybook/react-vite';
import Button from '../Button';
import Modal from '.';

export default {
  component: Modal.Root,
  subcomponents: {
    Header: Modal.Header,
    Title: Modal.Title,
    Content: Modal.Content,
    Footer: Modal.Footer,
  },
  title: 'Feedback/Modal/Modal',
} as Meta<typeof Modal.Root>;

export const Default: StoryObj<typeof Modal.Root> = {
  render: (args) => {
    const [isOpen, setIsOpen] = useState(Boolean(args.open));

    const handleClose = () => {
      setIsOpen(false);
    };

    useEffect(() => {
      setIsOpen(Boolean(args.open));
    }, [args.open]);

    return (
      <>
        <Button color="primary" variant="contained" size="md" onClick={() => setIsOpen(true)}>
          Open Modal
        </Button>

        <Modal.Root {...args} open={isOpen} onClose={handleClose}>
          <Modal.Header onClose={handleClose}>
            <Modal.Title>Occaecat Lorem commodo</Modal.Title>
          </Modal.Header>
          <Modal.Content>
            Ex adipisicing aute pariatur do elit ea ea ullamco exercitation officia ea anim aute.
          </Modal.Content>
          <Modal.Footer>
            <Button color="secondary" size="md" variant="outlined" onClick={handleClose}>
              Cancel
            </Button>
            <Button color="primary" variant="contained" size="md" onClick={handleClose}>
              Submit
            </Button>
          </Modal.Footer>
        </Modal.Root>
      </>
    );
  },
};
