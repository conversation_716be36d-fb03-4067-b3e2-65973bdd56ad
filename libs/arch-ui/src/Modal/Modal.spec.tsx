import React from 'react';
import { render } from '@testing-library/react';
import ModalBaseModule from '../ModalBase';
import createMatchMedia from '../tests/create-match-media';
import { mediaQueryOptions } from '../utils/breakpoints';
import Modal from '.';

describe('Modal', () => {
  const spyModal = jest.spyOn(ModalBaseModule, 'Root');
  const defaultProps = {
    open: true,
    onClose: jest.fn(),
  };

  describe('when on small screens', () => {
    beforeEach(() => {
      window.matchMedia = createMatchMedia(mediaQueryOptions.md - 1);
    });

    it('renders Modal component in fullscreen', () => {
      render(
        <Modal.Root {...defaultProps}>
          <input />
        </Modal.Root>
      );

      expect(spyModal).toHaveBeenCalledWith(
        expect.objectContaining({ fullScreen: true, outsidePad: false, roundBorders: false }),
        expect.anything()
      );
    });
  });

  describe('when on large screens', () => {
    beforeEach(() => {
      window.matchMedia = createMatchMedia(mediaQueryOptions.md + 1);
    });

    it('renders Modal component', () => {
      render(
        <Modal.Root {...defaultProps}>
          <input />
        </Modal.Root>
      );

      expect(spyModal).toHaveBeenCalledWith(
        expect.objectContaining({ outsidePad: true, roundBorders: true }),
        expect.anything()
      );
    });
  });
});

describe('ModalFooter', () => {
  const spyModalFooter = jest.spyOn(ModalBaseModule, 'Footer');

  it('renders ModalFooter component with topBorder ', () => {
    render(<Modal.Footer />);

    expect(spyModalFooter).toHaveBeenCalledWith(expect.objectContaining({ topBorder: true }), expect.anything());
  });
});

describe('ModalHeader', () => {
  const spyModalHeader = jest.spyOn(ModalBaseModule, 'Header');

  it('renders ModalHeader component with bottomBorder ', () => {
    render(<Modal.Header />);

    expect(spyModalHeader).toHaveBeenCalledWith(expect.objectContaining({ bottomBorder: true }), expect.anything());
  });
});
