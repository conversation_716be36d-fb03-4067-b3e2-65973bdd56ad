import type { Meta } from '@storybook/react-vite';
import { InputToggle } from './InputToggle';

export default {
  title: 'Input/InputToggle',
  component: InputToggle,
} as Meta<typeof InputToggle>;

export const Unchecked = {
  args: {},
};

export const Checked = {
  args: {
    checked: true,
  },
};

export const SmallUnchecked = {
  args: {
    small: true,
  },
};

export const SmallChecked = {
  args: {
    small: true,
    checked: true,
  },
};

export const WithIcon = {
  args: {
    withIcon: true,
  },
};

export const Disabled = {
  args: {
    disabled: true,
  },
};

export const DisabledChecked = {
  args: {
    disabled: true,
    checked: true,
  },
};
