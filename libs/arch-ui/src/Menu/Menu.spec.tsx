import React from 'react';
import { CameraIcon, DocumentIcon } from '@heroicons/react/24/solid';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import createMatchMedia from '../tests/create-match-media';
import { mediaQueryOptions } from '../utils/breakpoints';
import { MenuHeading, MenuItem, MenuItems, MenuRoot, MenuTrigger } from './Menu';

describe('Menu', () => {
  const handleClick = jest.fn();

  const options: React.ReactNode = [
    <MenuHeading key="heading-1">Media</MenuHeading>,
    <MenuItem key="option-1" icon={CameraIcon} onClick={handleClick}>
      Option 1
    </MenuItem>,
    <MenuItem key="option-2" icon={DocumentIcon} onClick={handleClick}>
      Option 2
    </MenuItem>,
  ];

  beforeEach(() => {
    window.matchMedia = createMatchMedia(mediaQueryOptions.xl);
  });

  describe('when menu options are passed', () => {
    it('renders the menu items', async () => {
      render(
        <MenuRoot>
          <MenuTrigger>Create</MenuTrigger>
          <MenuItems>{options}</MenuItems>
        </MenuRoot>
      );

      await userEvent.click(screen.getByRole('button', { name: 'Create' }));

      expect(screen.getByText('Media')).toBeInTheDocument();
      expect(screen.getByText('Option 1')).toBeInTheDocument();
      expect(screen.getByText('Option 2')).toBeInTheDocument();
    });

    it('closes menu when the user clicks in one option', async () => {
      render(
        <MenuRoot>
          <MenuTrigger>Create</MenuTrigger>
          <MenuItems data-testid="menu-items">{options}</MenuItems>
        </MenuRoot>
      );

      await userEvent.click(screen.getByRole('button', { name: 'Create' }));
      await userEvent.click(screen.getByText('Option 1'));

      expect(handleClick).toBeCalled();
      await waitFor(() => expect(screen.queryByTestId('menu-items')).not.toBeInTheDocument());
    });
  });
});
