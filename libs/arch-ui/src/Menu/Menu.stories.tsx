import React, { type ComponentProps, Fragment } from 'react';
import type { Meta, StoryObj } from '@storybook/react-vite';
import Button from '../Button';
import { ChevronDownIcon, Cog8ToothIcon, ShieldExclamationIcon, UserCircleIcon } from '../Icons/solid';
import { MenuHeading, MenuItem, MenuItems, MenuRoot, MenuTrigger } from './Menu';

const meta: Meta<ComponentProps<typeof MenuRoot>> = {
  title: 'Containment/Menu',
  component: MenuRoot,
};

export default meta;
type Story = StoryObj<ComponentProps<typeof MenuRoot>>;

const onItemClick = () => {};

const optionsAlpha: React.ReactNode = [
  <MenuItem key="1" icon={UserCircleIcon} onClick={onItemClick}>
    Gallery
  </MenuItem>,
  <MenuItem key="2" icon={ShieldExclamationIcon} onClick={onItemClick}>
    Camera
  </MenuItem>,
  <MenuItem key="3" icon={Cog8ToothIcon} onClick={onItemClick}>
    Responsible team
  </MenuItem>,
  <MenuItem key="4" onClick={onItemClick}>
    Observer team
  </MenuItem>,
];

const optionsBeta: React.ReactNode = [
  <MenuHeading key="heading-1">Media</MenuHeading>,
  <MenuItem key="1" icon={UserCircleIcon} onClick={onItemClick}>
    Gallery
  </MenuItem>,
  <MenuItem key="2" icon={ShieldExclamationIcon} onClick={onItemClick}>
    Camera
  </MenuItem>,
  <MenuHeading key="heading-1" className="text-red-600">
    Admin panel
  </MenuHeading>,
  <MenuItem key="3" icon={Cog8ToothIcon} onClick={onItemClick}>
    Team settings
  </MenuItem>,
  <MenuItem key="4" icon={Cog8ToothIcon} onClick={onItemClick}>
    Project settings
  </MenuItem>,
];

export const Default: Story = {
  args: { className: '' },
  render: () => {
    return (
      <>
        <div className="flex w-full justify-center">
          <MenuRoot>
            <MenuTrigger as={Fragment}>
              <Button color="primary" size="md" variant="contained">
                Add
                <ChevronDownIcon />
              </Button>
            </MenuTrigger>
            <MenuItems>{optionsAlpha}</MenuItems>
          </MenuRoot>
        </div>
        <div className="flex min-h-[160px]" />
      </>
    );
  },
};

export const WithHeadings: Story = {
  name: 'Menu with section headings',
  render: () => {
    return (
      <>
        <div className="flex w-full justify-center">
          <MenuRoot>
            <MenuTrigger as={Fragment}>
              <Button color="primary" size="md" variant="contained">
                Add
                <ChevronDownIcon />
              </Button>
            </MenuTrigger>
            <MenuItems>{optionsBeta}</MenuItems>
          </MenuRoot>
        </div>
        <div className="flex min-h-[220px]" />
      </>
    );
  },
};
