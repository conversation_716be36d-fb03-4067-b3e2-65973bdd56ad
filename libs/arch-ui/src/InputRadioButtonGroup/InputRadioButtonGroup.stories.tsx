import React, { useState } from 'react';
import type { Meta, StoryFn } from '@storybook/react-vite';
import { InputRadioButtonGroup, type InputRadioButtonGroupProps } from './InputRadioButtonGroup';

export default {
  title: 'Input/InputRadioButtonGroup',
  component: InputRadioButtonGroup,
} as Meta<InputRadioButtonGroupProps>;

const Template: StoryFn<InputRadioButtonGroupProps> = (props) => {
  const [value, setValue] = useState(props.options[0].value);

  const changeValue = (_: any, val: string) => setValue(val);

  return <InputRadioButtonGroup value={value} setFieldValue={changeValue} {...props} />;
};

export const Default = {
  render: Template,

  args: {
    options: [
      { label: 'example', value: 'example', description: 'This is a description' },
      {
        label: 'another example',
        value: 'another example',
        description: 'And this is another one',
      },
    ],
  },
};

export const Simple = {
  render: Template,

  args: {
    ...Default.args,
    type: 'simple',
  },
};

export const WithLabelAndContent = {
  render: Template,

  args: {
    ...Default.args,
    label: 'Group label',
    options: [
      { value: 'example', content: 'This is a description' },
      {
        value: 'another example',
        content: 'And this is another one',
      },
    ],
  },
};

export const WithDescription = {
  render: Template,

  args: {
    ...Default.args,
    label: 'Group label',
    description: 'A very descriptive description',
  },
};

export const DisabledOption = {
  render: Template,

  args: {
    ...Default.args,
    options: [
      ...(Default.args.options || []),
      {
        label: 'Disabled',
        value: 'disabled',
        description: 'You can not choose this option',
        disabled: true,
      },
    ],
  },
};

export const AllDisabled = {
  render: Template,

  args: {
    ...Default.args,
    disabled: true,
  },
};

export const WithRoundedBorder = {
  render: Template,

  args: {
    ...Default.args,
    rounded: true,
    options: [
      ...(Default.args.options || []),
      {
        label: 'another example',
        value: 'another example',
        description: 'And this is another one',
      },
    ],
  },
};
