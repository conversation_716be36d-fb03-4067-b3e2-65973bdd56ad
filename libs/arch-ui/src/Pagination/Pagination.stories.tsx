import React from 'react';
import type { Meta, StoryObj } from '@storybook/react-vite';
import { Pagination, type PaginationProps } from './Pagination';

const meta: Meta<PaginationProps> = {
  title: 'Navigation/Pagination',
  component: Pagination,
  subcomponents: {},
};

export default meta;
type Story = StoryObj<typeof Pagination>;

export const Default: Story = {
  args: {
    hasNextPage: true,
    hasPreviousPage: true,
    lastEntryCursor: 'lastEntryCursor',
    firstEntryCursor: 'firstEntryCursor',
    total: 42,
    count: 10,
    previousButtonLabel: 'Previous',
    nextButtonLabel: 'Next',
  },
  render: (props) => <Pagination {...props} onNext={() => {}} onPrevious={() => {}} />,
};
