import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Pagination } from './Pagination';

describe('Pagination', () => {
  it('renders the pagination', () => {
    const paginationMeta = {
      hasNextPage: true,
      hasPreviousPage: true,
      lastEntryCursor: 'lastEntryCursor',
      firstEntryCursor: 'firstEntryCursor',
      total: 42,
    };

    render(<Pagination {...paginationMeta} count={13} onNext={jest.fn()} onPrevious={jest.fn()} />);

    expect(screen.getByText((_, element) => element?.textContent === 'Showing 13 of 42 results')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Previous' })).toBeEnabled();
    expect(screen.getByRole('button', { name: 'Next' })).toBeEnabled();
  });

  describe('when next button is pressed', () => {
    it('calls onNext with the lastEntryCursor', async () => {
      const paginationMeta = {
        hasNextPage: true,
        hasPreviousPage: true,
        lastEntryCursor: 'lastEntryCursor',
        firstEntryCursor: 'firstEntryCursor',
        total: 42,
      };
      const onNext = jest.fn();
      const onPrevious = jest.fn();

      render(
        <Pagination
          {...paginationMeta}
          count={10}
          onNext={onNext}
          onPrevious={onPrevious}
          previousButtonLabel="< Previous"
          nextButtonLabel="Next >"
        />
      );
      await userEvent.click(screen.getByRole('button', { name: 'Next >' }));

      expect(onPrevious).toHaveBeenCalledTimes(0);
      expect(onNext).toHaveBeenCalledTimes(1);
      expect(onNext).toHaveBeenCalledWith('lastEntryCursor');
    });
  });

  describe('when previous button is pressed', () => {
    it('calls onPrevious with the firstEntryCursor', async () => {
      const paginationMeta = {
        hasNextPage: true,
        hasPreviousPage: true,
        lastEntryCursor: 'lastEntryCursor',
        firstEntryCursor: 'firstEntryCursor',
        total: 42,
      };
      const onNext = jest.fn();
      const onPrevious = jest.fn();

      render(
        <Pagination
          {...paginationMeta}
          count={10}
          onNext={onNext}
          onPrevious={onPrevious}
          previousButtonLabel="< Previous"
          nextButtonLabel="Next >"
        />
      );
      await userEvent.click(screen.getByRole('button', { name: '< Previous' }));

      expect(onNext).toHaveBeenCalledTimes(0);
      expect(onPrevious).toHaveBeenCalledTimes(1);
      expect(onPrevious).toHaveBeenCalledWith('firstEntryCursor');
    });
  });

  describe('when hasNextPage is false', () => {
    it('disables the next button', () => {
      const paginationMeta = {
        hasNextPage: false,
        hasPreviousPage: true,
        lastEntryCursor: 'lastEntryCursor',
        firstEntryCursor: 'firstEntryCursor',
        total: 42,
      };

      render(<Pagination {...paginationMeta} count={10} onNext={jest.fn()} onPrevious={jest.fn()} />);

      expect(screen.getByRole('button', { name: 'Next' })).toBeDisabled();
    });
  });

  describe('when hasPreviousPage is false', () => {
    it('disables the previous button', () => {
      const paginationMeta = {
        hasNextPage: true,
        hasPreviousPage: false,
        lastEntryCursor: 'lastEntryCursor',
        firstEntryCursor: 'firstEntryCursor',
        total: 42,
      };

      render(<Pagination {...paginationMeta} count={10} onNext={jest.fn()} onPrevious={jest.fn()} />);

      expect(screen.getByRole('button', { name: 'Previous' })).toBeDisabled();
    });
  });
});
