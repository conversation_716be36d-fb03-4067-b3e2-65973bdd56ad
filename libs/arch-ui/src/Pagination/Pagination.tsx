import React from 'react';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/solid';
import Button from '../Button';

type PaginationCursor = string | null;

export type PaginationVariant = 'compact' | 'numbered';

export type PaginationProps = {
  variant?: PaginationVariant;
  firstEntryCursor: PaginationCursor;
  lastEntryCursor: PaginationCursor;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  total: number;
  count: number;
  onNext: (cursor: PaginationCursor) => void;
  onPrevious: (cursor: PaginationCursor) => void;
  previousButtonLabel?: string;
  nextButtonLabel?: string;
};

export const Pagination: React.FC<PaginationProps> = ({
  variant: _ = 'compact',
  hasPreviousPage,
  hasNextPage,
  firstEntryCursor,
  lastEntryCursor,
  total,
  count,
  onNext,
  onPrevious,
  previousButtonLabel = 'Previous',
  nextButtonLabel = 'Next',
}) => {
  return (
    <div className="flex items-center justify-between">
      <p>
        Showing <span className="font-medium">{count}</span> of <span className="font-medium">{total}</span> results
      </p>

      <div className="flex items-start space-x-2">
        <Button
          color="secondary"
          variant="outlined"
          aria-label={previousButtonLabel}
          size="sm"
          disabled={!hasPreviousPage}
          onClick={() => onPrevious(firstEntryCursor ?? null)}
          leadingIcon={ChevronLeftIcon}
        >
          {previousButtonLabel}
        </Button>
        <Button
          color="secondary"
          variant="outlined"
          aria-label={nextButtonLabel}
          size="sm"
          disabled={!hasNextPage}
          onClick={() => onNext(lastEntryCursor ?? null)}
        >
          {nextButtonLabel}
          <ChevronRightIcon className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
};
