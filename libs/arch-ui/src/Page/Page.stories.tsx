import React from 'react';
import type { Meta, StoryFn } from '@storybook/react-vite';
import Button from '../Button';
import { EditInPlace } from '../EditInPlace/EditInPlace';
import { PlusIcon } from '../Icons/solid';
import { Page, type PageProps } from './Page';

export default {
  title: 'Screens/Page',
  component: Page,
  subcomponents: {
    Header: Page.Header,
    Body: Page.Body,
  },
  parameters: { layout: 'fullscreen' },
} as Meta<PageProps>;

const TemplatePage: StoryFn<PageProps> = ({ ref, ...props }) => <Page {...props} />;

export const HeaderWithTitle = {
  render: TemplatePage,

  args: {
    children: <Page.Header title="My profile" />,
  },
};

export const HeaderWithRightSection = {
  render: TemplatePage,

  args: {
    children: (
      <Page.Header
        title="Sint labore est commodo ex fugiat pariatur ut et veniam eiusmod aliquip do."
        rightSection={
          <Button color="primary" variant="contained" size="md" leadingIcon={PlusIcon}>
            New project
          </Button>
        }
      />
    ),
  },
};

export const HeaderWithBackNavigation = {
  render: TemplatePage,

  args: {
    children: (
      <Page.Header
        hasBackNavigation
        backNavigationTitle="Back to Organisations"
        onBackNavigation={() => {}}
        title="This is a long title name"
        rightSection={
          <Button color="primary" variant="contained" size="md" leadingIcon={PlusIcon}>
            New project
          </Button>
        }
      />
    ),
  },
};

export const HeaderWithBottomSection = {
  render: TemplatePage,

  args: {
    children: (
      <Page.Header
        hasBackNavigation
        backNavigationTitle="Back to Organisations"
        onBackNavigation={() => {}}
        title="Nulla reprehenderit aliqua est velit cillum sunt ipsum magna duis adipisicing."
        rightSection={
          <Button color="primary" variant="contained" size="md" leadingIcon={PlusIcon}>
            New project
          </Button>
        }
        bottomSection="Reprehenderit sit magna in ea non non id commodo adipisicing magna ipsum enim eu."
      />
    ),
  },
};

export const HeaderWithCustomTitle = {
  render: TemplatePage,

  args: {
    children: (
      <Page.Header
        title="This is a long title name"
        titleAs={({ children: title }) => (
          <EditInPlace label="title" contentEditable onBlur={() => {}}>
            <h1 className="mb-2 text-2xl font-semibold leading-8" title="title" data-testid="res-title-value">
              {title}
            </h1>
          </EditInPlace>
        )}
        rightSection={
          <Button color="primary" variant="contained" size="md" leadingIcon={PlusIcon}>
            New project
          </Button>
        }
        bottomSection="This is the bottom section"
      />
    ),
  },
};

export const WithFooter = {
  render: TemplatePage,

  args: {
    children: <Page.Footer>This is the footer.</Page.Footer>,
  },
};
