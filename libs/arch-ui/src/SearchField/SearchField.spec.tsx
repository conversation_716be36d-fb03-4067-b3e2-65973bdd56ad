import React from 'react';
import { act, render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { SearchField, type SearchFieldProps } from './SearchField';

describe('SearchField', () => {
  it('renders Search as the default placeholder', () => {
    const props: SearchFieldProps = {
      onChange: () => {},
    };

    render(<SearchField {...props} />);

    expect(screen.getByPlaceholderText('Search')).toBeInTheDocument();
  });

  describe('when placeholder is specified', () => {
    it('renders the provided value', () => {
      const props: SearchFieldProps = {
        placeholder: 'Search your favorite reducer...',
        onChange: () => {},
      };

      render(<SearchField {...props} />);

      expect(screen.getByPlaceholderText('Search your favorite reducer...')).toBeInTheDocument();
    });
  });

  describe('when user types something', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    afterEach(() => {
      jest.runOnlyPendingTimers();
      jest.useRealTimers();
    });

    describe('and not enough time has passed', () => {
      it('does not call onChange', async () => {
        const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
        const props: SearchFieldProps = {
          onChange: jest.fn(),
        };
        render(<SearchField {...props} />);

        await user.type(screen.getByPlaceholderText('Search'), 'My new reducer');
        act(() => {
          jest.advanceTimersByTime(249);
        });

        expect(props.onChange).not.toHaveBeenCalled();
      });
    });

    describe('and enough time has passed', () => {
      it('call onChange with the value', async () => {
        const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
        const props: SearchFieldProps = {
          onChange: jest.fn(),
        };
        render(<SearchField {...props} />);

        await user.type(screen.getByPlaceholderText('Search'), 'My new reducer');
        act(() => {
          jest.advanceTimersByTime(251);
        });

        await waitFor(() =>
          expect(props.onChange).toBeCalledWith(
            expect.objectContaining({ target: expect.objectContaining({ value: 'My new reducer' }) })
          )
        );
      });
    });
  });
});
