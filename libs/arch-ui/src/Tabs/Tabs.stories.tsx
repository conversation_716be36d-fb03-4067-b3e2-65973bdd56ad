import React from 'react';
import type { <PERSON>a, StoryFn, StoryObj } from '@storybook/react-vite';
import { UserIcon } from '../Icons/outline';
import { Tabs, type TabsProps } from './Tabs';

export default {
  title: 'Navigation/Tabs/Tabs',
  component: Tabs,
  subcomponents: {
    Tab: Tabs.Tab,
  },
} as Meta<TabsProps>;

export const Default: StoryObj<TabsProps> = {
  render: (args) => (
    <Tabs {...args}>
      <Tabs.Tab>1</Tabs.Tab>
      <Tabs.Tab>2</Tabs.Tab>
      <Tabs.Tab value="tab 3">3</Tabs.Tab>
    </Tabs>
  ),
};

export const Standard: StoryObj<TabsProps> = {
  render: (args) => {
    const [tabIndex, setTabIndex] = React.useState(0);

    return (
      <Tabs {...args} selectedValue={tabIndex} onChange={(_, newValue) => setTabIndex(newValue)}>
        <Tabs.Tab>First</Tabs.Tab>
        <Tabs.Tab>Second</Tabs.Tab>
        <Tabs.Tab badge={2}>Third</Tabs.Tab>
        <Tabs.Tab>Fourth</Tabs.Tab>
        <Tabs.Tab badge={4}>Fifth</Tabs.Tab>
      </Tabs>
    );
  },
};

export const WithDisabledTab: StoryObj<TabsProps> = {
  render: (args) => {
    const [tabIndex, setTabIndex] = React.useState(0);

    return (
      <Tabs {...args} selectedValue={tabIndex} onChange={(_, newValue) => setTabIndex(newValue)}>
        <Tabs.Tab>First</Tabs.Tab>
        <Tabs.Tab>Second</Tabs.Tab>
        <Tabs.Tab disabled badge={2}>
          Third
        </Tabs.Tab>
        <Tabs.Tab disabled>Fourth</Tabs.Tab>
      </Tabs>
    );
  },
};

export const WithOnSelectOnTab: StoryFn<TabsProps> = () => {
  const [tabIndex, setTabIndex] = React.useState(0);

  return (
    <Tabs>
      <Tabs.Tab onSelect={() => setTabIndex(0)} selected={tabIndex === 0}>
        First
      </Tabs.Tab>
      <Tabs.Tab onSelect={() => setTabIndex(1)} selected={tabIndex === 1}>
        Second
      </Tabs.Tab>
      <Tabs.Tab onSelect={() => setTabIndex(2)} selected={tabIndex === 2}>
        Third
      </Tabs.Tab>
    </Tabs>
  );
};

export const WithLeadingIcon: StoryFn<TabsProps> = () => {
  const [tabIndex, setTabIndex] = React.useState(0);

  return (
    <Tabs>
      <Tabs.Tab leadingIcon={UserIcon} onSelect={() => setTabIndex(0)} selected={tabIndex === 0}>
        First
      </Tabs.Tab>
      <Tabs.Tab leadingIcon={UserIcon} onSelect={() => setTabIndex(1)} selected={tabIndex === 1}>
        Second
      </Tabs.Tab>
      <Tabs.Tab leadingIcon={UserIcon} onSelect={() => setTabIndex(2)} selected={tabIndex === 2} badge={2}>
        Third
      </Tabs.Tab>
      <Tabs.Tab leadingIcon={UserIcon} onSelect={() => setTabIndex(3)} selected={tabIndex === 3} disabled badge={2}>
        Fourth
      </Tabs.Tab>
    </Tabs>
  );
};
