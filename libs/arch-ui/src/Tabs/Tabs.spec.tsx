import React from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import { Tabs } from './Tabs';

describe('Tabs', () => {
  it('renders the children', () => {
    render(<Tabs>content</Tabs>);

    expect(screen.getByText('content')).toBeInTheDocument();
  });

  describe('onChange', () => {
    it('trigger on change when clicked', () => {
      const onChange = jest.fn();

      render(
        <Tabs onChange={onChange}>
          <Tabs.Tab>Tab1</Tabs.Tab>
          <Tabs.Tab>Tab2</Tabs.Tab>
        </Tabs>
      );

      fireEvent.click(screen.getByRole('tab', { name: 'Tab2' }));
      expect(onChange).toHaveBeenCalledTimes(1);
      expect(onChange).toHaveBeenCalledWith(expect.any(Object), 1);
    });
  });
});
