import React from 'react';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import createMatchMedia from '../../tests/create-match-media';
import { mediaQueryOptions } from '../../utils/breakpoints';
import { ScrollableTabs as Tabs } from './ScrollableTabs';

jest.mock('../../../../shape-hooks/src/useElementSize', () => ({
  useElementSize: jest
    .fn()
    .mockImplementationOnce(() => ({ width: 320 }))
    .mockImplementationOnce(() => ({ width: 480 }))
    .mockImplementationOnce(() => ({ width: 320 }))
    .mockImplementation(() => ({ width: 640 })),
}));

describe('ScrollableTabs', () => {
  beforeEach(() => {
    window.HTMLElement.prototype.scrollIntoView = jest.fn();
  });

  describe('when tabs overflow', () => {
    beforeEach(() => {
      window.matchMedia = createMatchMedia(mediaQueryOptions.sm);
    });

    it('renders the left & right arrows', () => {
      render(
        <Tabs>
          <Tabs.Tab>Tab1</Tabs.Tab>
          <Tabs.Tab>Tab2</Tabs.Tab>
          <Tabs.Tab>Tab3</Tabs.Tab>
        </Tabs>
      );

      expect(screen.getByRole('tab', { name: 'Tab1' })).toBeInTheDocument();
      expect(screen.getByRole('tab', { name: 'Tab2' })).toBeInTheDocument();
      expect(screen.getByRole('tab', { name: 'Tab3' })).toBeInTheDocument();

      waitFor(() => expect(screen.queryByLabelText('scroll left')).not.toBeInTheDocument());
      waitFor(() => expect(screen.getByLabelText('scroll right')).toBeInTheDocument());
    });
  });

  it('renders the children', () => {
    render(
      <Tabs>
        <Tabs.Tab>Tab1</Tabs.Tab>
        <Tabs.Tab>Tab2</Tabs.Tab>
      </Tabs>
    );

    expect(screen.getByText('Tab1')).toBeInTheDocument();
    expect(screen.getByText('Tab2')).toBeInTheDocument();
  });

  describe('onChange', () => {
    it('trigger on change when clicked', () => {
      const onChange = jest.fn();

      render(
        <Tabs onChange={onChange}>
          <Tabs.Tab>Tab1</Tabs.Tab>
          <Tabs.Tab>Tab2</Tabs.Tab>
        </Tabs>
      );

      fireEvent.click(screen.getByRole('tab', { name: 'Tab2' }));
      expect(onChange).toHaveBeenCalledTimes(1);
      expect(onChange).toHaveBeenCalledWith(expect.any(Object), 1);
      expect(window.HTMLElement.prototype.scrollIntoView).toHaveBeenCalled();
    });
  });
});
