import React from 'react';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { ScrollableRightArrow } from './ScrollableRightArrow';

const mockDiv = document.createElement('div');

Object.defineProperties(mockDiv, {
  scrollWidth: { value: 640 },
  clientWidth: { value: 320 },
  scrollTo: { value: jest.fn() },
});

const mockRef = { current: mockDiv } as React.RefObject<HTMLDivElement>;
jest.spyOn(mockDiv, 'scrollTo');

describe('ScrollableRightArrow', () => {
  describe('when tabs overflow', () => {
    it('renders the arrow', () => {
      render(<ScrollableRightArrow width={400} position={300} parentRef={mockRef} isOverflowing={true} />);

      waitFor(() => expect(screen.getByLabelText('scroll right')).toBeInTheDocument());
      waitFor(() => expect(screen.getByTestId('icon')).toBeInTheDocument());
    });

    it('scrolls on click', () => {
      render(<ScrollableRightArrow width={400} position={300} parentRef={mockRef} isOverflowing={true} />);

      waitFor(() => expect(screen.getByLabelText('scroll right')).toBeInTheDocument());

      fireEvent.click(screen.getByLabelText('scroll right'));

      if (mockRef.current) {
        expect(mockRef.current.scrollTo).toHaveBeenCalled();
      }
    });

    it('does not render when scrolled to the right', () => {
      render(<ScrollableRightArrow width={400} position={400} parentRef={mockRef} isOverflowing={true} />);

      expect(screen.queryByLabelText('scroll right')).not.toBeInTheDocument();
    });
  });

  describe('when tabs dont overflow', () => {
    it('does not render the arrow', () => {
      render(<ScrollableRightArrow width={400} position={300} parentRef={mockRef} isOverflowing={false} />);

      expect(screen.queryByLabelText('scroll right')).not.toBeInTheDocument();
    });
  });
});
