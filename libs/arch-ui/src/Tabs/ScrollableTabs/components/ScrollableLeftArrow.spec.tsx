import React from 'react';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { ScrollableLeftArrow } from './ScrollableLeftArrow';

const mockDiv = document.createElement('div');

Object.defineProperties(mockDiv, {
  scrollWidth: { value: 640 },
  clientWidth: { value: 320 },
  scrollTo: { value: jest.fn() },
});

const mockRef = { current: mockDiv } as React.RefObject<HTMLDivElement>;
jest.spyOn(mockDiv, 'scrollTo');

describe('ScrollableLeftArrow', () => {
  describe('when tabs overflow', () => {
    it('renders the arrow', () => {
      render(<ScrollableLeftArrow width={400} position={300} parentRef={mockRef} isOverflowing={true} />);

      waitFor(() => expect(screen.getByLabelText('scroll left')).toBeInTheDocument());
      waitFor(() => expect(screen.getByTestId('icon')).toBeInTheDocument());
    });

    it('scrolls on click', () => {
      render(<ScrollableLeftArrow width={400} position={300} parentRef={mockRef} isOverflowing={true} />);

      waitFor(() => expect(screen.getByLabelText('scroll left')).toBeInTheDocument());

      fireEvent.click(screen.getByLabelText('scroll left'));

      if (mockRef.current) {
        expect(mockRef.current.scrollTo).toHaveBeenCalled();
      }
    });

    it('does not render when scrolled to the left', () => {
      render(<ScrollableLeftArrow width={400} position={0} parentRef={mockRef} isOverflowing={true} />);

      expect(screen.queryByLabelText('scroll left')).not.toBeInTheDocument();
    });
  });

  describe('when tabs dont overflow', () => {
    it('does not render the arrow', () => {
      render(<ScrollableLeftArrow width={400} position={300} parentRef={mockRef} isOverflowing={false} />);

      expect(screen.queryByLabelText('scroll left')).not.toBeInTheDocument();
    });
  });
});
