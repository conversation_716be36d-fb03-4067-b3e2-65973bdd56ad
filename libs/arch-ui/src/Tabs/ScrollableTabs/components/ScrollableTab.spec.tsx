import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { UserIcon } from '../../../Icons/outline';
import { ScrollableTab as Tab } from './ScrollableTab';

describe('ScrollableTab', () => {
  it('renders the children', () => {
    render(<Tab>content</Tab>);

    expect(screen.getByText('content')).toBeInTheDocument();
  });

  it('renders a leading icon', () => {
    render(<Tab leadingIcon={UserIcon}>content</Tab>);

    expect(screen.getByTestId('leading-icon')).toBeInTheDocument();
  });

  it('triggers the onSelect action if provided', async () => {
    const onSelect = jest.fn();

    render(<Tab onSelect={onSelect}>content</Tab>);

    await userEvent.click(screen.getByRole('tab'));

    expect(onSelect).toHaveBeenCalledTimes(1);
    expect(onSelect).toHaveBeenCalledWith(expect.any(Object));
  });

  describe('when is not selected', () => {
    it('is clickable', async () => {
      const onSelect = jest.fn();

      render(<Tab onSelect={onSelect}>content</Tab>);

      await userEvent.click(screen.getByRole('tab'));

      expect(onSelect).toHaveBeenCalledTimes(1);
    });
  });

  describe('when is selected', () => {
    it('is not clickable', async () => {
      const onSelect = jest.fn();

      render(
        <Tab selected onSelect={onSelect}>
          content
        </Tab>
      );

      await userEvent.click(screen.getByRole('tab'));

      expect(onSelect).not.toHaveBeenCalled();
    });
  });

  describe('when is disabled', () => {
    it('is not clickable', async () => {
      const onSelect = jest.fn();

      render(
        <Tab disabled onSelect={onSelect}>
          content
        </Tab>
      );

      await userEvent.click(screen.getByRole('tab'));

      expect(onSelect).not.toHaveBeenCalled();
    });

    it('renders disabled', () => {
      render(<Tab disabled>content</Tab>);

      expect(screen.getByRole('tab')).toBeDisabled();
    });
  });
});
