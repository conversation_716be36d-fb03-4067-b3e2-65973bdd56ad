import React from 'react';
import type { Meta, StoryFn, StoryObj } from '@storybook/react-vite';
import { UserIcon } from '../../Icons/outline';
import type { TabsProps } from '../Tabs';
import { ScrollableTabs as Tabs } from './ScrollableTabs';

export default {
  title: 'Navigation/Tabs/ScrollableTabs',
  component: Tabs,
  subcomponents: {
    Tab: Tabs.Tab,
  },
} as Meta<TabsProps>;

export const Default: StoryObj<TabsProps> = {
  render: (args) => {
    const [tabIndex, setTabIndex] = React.useState(0);

    return (
      <Tabs {...args} selectedValue={tabIndex} onChange={(_, newValue) => setTabIndex(newValue)}>
        <Tabs.Tab>Alpha</Tabs.Tab>
        <Tabs.Tab>Beta</Tabs.Tab>
        <Tabs.Tab>Gamma</Tabs.Tab>
        <Tabs.Tab>Delta</Tabs.Tab>
        <Tabs.Tab>Epsilon</Tabs.Tab>
        <Tabs.Tab>Zeta</Tabs.Tab>
        <Tabs.Tab>Eta</Tabs.Tab>
        <Tabs.Tab>Theta</Tabs.Tab>
      </Tabs>
    );
  },
};

export const WithLeadingIcon: StoryFn<TabsProps> = () => {
  const [tabIndex, setTabIndex] = React.useState(0);

  return (
    <Tabs>
      <Tabs.Tab leadingIcon={UserIcon} onSelect={() => setTabIndex(0)} selected={tabIndex === 0}>
        First
      </Tabs.Tab>
      <Tabs.Tab leadingIcon={UserIcon} onSelect={() => setTabIndex(1)} selected={tabIndex === 1}>
        Second
      </Tabs.Tab>
      <Tabs.Tab leadingIcon={UserIcon} onSelect={() => setTabIndex(2)} selected={tabIndex === 2} badge={2}>
        Third
      </Tabs.Tab>
      <Tabs.Tab leadingIcon={UserIcon} onSelect={() => setTabIndex(3)} selected={tabIndex === 3} disabled badge={2}>
        Fourth
      </Tabs.Tab>
    </Tabs>
  );
};
