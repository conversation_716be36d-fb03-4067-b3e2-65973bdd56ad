import React, { useState } from 'react';
import type { Meta, StoryObj } from '@storybook/react-vite';
import { Table, type TableProps } from './Table';

type TableStoryProps = TableProps & { striped?: boolean; highlighted?: boolean };
type Story = StoryObj<TableStoryProps>;
type DensityStoryProps = TableProps;
type DensityStory = StoryObj<DensityStoryProps>;

const meta: Meta<TableStoryProps> = {
  component: Table,
  subcomponents: {},
  title: 'Layout and Organisation/Table',
};
export default meta;

const Template = ({ striped = false, highlighted = false }: TableStoryProps) => (
  <Table.Container>
    <Table>
      <Table.Heading>
        <Table.Row>
          <Table.Header>First header</Table.Header>
          <Table.Header>Second header</Table.Header>
        </Table.Row>
      </Table.Heading>
      <Table.Body>
        <Table.Row striped={striped} highlighted={highlighted}>
          <Table.Cell>A table cell</Table.Cell>
          <Table.Cell>Another table cell</Table.Cell>
        </Table.Row>
        <Table.Row striped={striped}>
          <Table.Cell>A table cell</Table.Cell>
          <Table.Cell>Another table cell</Table.Cell>
        </Table.Row>
        <Table.Row striped={striped}>
          <Table.Cell>A table cell</Table.Cell>
          <Table.Cell>Another table cell</Table.Cell>
        </Table.Row>
      </Table.Body>
    </Table>
  </Table.Container>
);

export const Default: Story = {
  render: Template,
};

export const Striped: Story = {
  render: Template,
  args: {
    ...Default.args,
    striped: true,
  },
};

const DensityTemplate = ({ rowDensity }: { rowDensity: 'default' | 'condensed' | 'spacious' }) => (
  <Table.Container>
    <Table rowDensity={rowDensity}>
      <Table.Heading>
        <Table.Row striped>
          <Table.Header>Name</Table.Header>
          <Table.Header>Email</Table.Header>
          <Table.Header>Role</Table.Header>
          <Table.Header>Status</Table.Header>
        </Table.Row>
      </Table.Heading>
      <Table.Body>
        <Table.Row striped>
          <Table.Cell>John Doe</Table.Cell>
          <Table.Cell><EMAIL></Table.Cell>
          <Table.Cell>Developer</Table.Cell>
          <Table.Cell>Active</Table.Cell>
        </Table.Row>
        <Table.Row striped>
          <Table.Cell>Jane Smith</Table.Cell>
          <Table.Cell><EMAIL></Table.Cell>
          <Table.Cell>Designer</Table.Cell>
          <Table.Cell>Active</Table.Cell>
        </Table.Row>
        <Table.Row striped>
          <Table.Cell>Bob Johnson</Table.Cell>
          <Table.Cell><EMAIL></Table.Cell>
          <Table.Cell>Manager</Table.Cell>
          <Table.Cell>Inactive</Table.Cell>
        </Table.Row>
      </Table.Body>
    </Table>
  </Table.Container>
);

export const DefaultDensity: DensityStory = {
  render: () => <DensityTemplate rowDensity="default" />,
  parameters: {
    docs: {
      description: {
        story:
          'Default row density with standard padding (py-2). This is the most commonly used density for general data tables.',
      },
    },
  },
};

export const CondensedDensity: DensityStory = {
  render: () => <DensityTemplate rowDensity="condensed" />,
  parameters: {
    docs: {
      description: {
        story:
          'Condensed row density with minimal padding (py-1). Use this when you need to display more data in a compact space.',
      },
    },
  },
};

export const SpaciousDensity: DensityStory = {
  render: () => <DensityTemplate rowDensity="spacious" />,
  parameters: {
    docs: {
      description: {
        story:
          'Spacious row density with generous padding (py-4). Use this for better readability when space is not a constraint.',
      },
    },
  },
};

export const Highlighted: Story = {
  render: Template,
  args: {
    ...Default.args,
    highlighted: true,
  },
};

export const StripedWithHighlighted: Story = {
  render: Template,
  args: {
    ...Default.args,
    striped: true,
    highlighted: true,
  },
};

const InteractiveHighlightDemo = () => {
  const [highlightedRowId, setHighlightedRowId] = useState<string | null>(null);
  const [highlightTimer, setHighlightTimer] = useState<NodeJS.Timeout | null>(null);

  const handleHighlightRow = (rowId: string) => {
    if (highlightTimer) {
      clearTimeout(highlightTimer);
    }

    setHighlightedRowId(rowId);

    const timer = setTimeout(() => {
      setHighlightedRowId(null);
    }, 3000);

    setHighlightTimer(timer);
  };

  return (
    <div className="space-y-4">
      <div className="space-x-2">
        <button
          type="button"
          onClick={() => handleHighlightRow('row1')}
          className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
        >
          Highlight Row 1
        </button>
        <button
          type="button"
          onClick={() => handleHighlightRow('row2')}
          className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
        >
          Highlight Row 2
        </button>
        <button
          type="button"
          onClick={() => handleHighlightRow('row3')}
          className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
        >
          Highlight Row 3
        </button>
        <button
          type="button"
          onClick={() => {
            setHighlightedRowId(null);
            if (highlightTimer) clearTimeout(highlightTimer);
          }}
          className="px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600"
        >
          Clear Highlight
        </button>
      </div>

      <Table.Container>
        <Table>
          <Table.Heading>
            <Table.Row>
              <Table.Header>Name</Table.Header>
              <Table.Header>Status</Table.Header>
              <Table.Header>Date</Table.Header>
            </Table.Row>
          </Table.Heading>
          <Table.Body>
            <Table.Row striped highlighted={highlightedRowId === 'row1'} className="transition-colors duration-300">
              <Table.Cell>John Doe</Table.Cell>
              <Table.Cell>Active</Table.Cell>
              <Table.Cell>2024-01-15</Table.Cell>
            </Table.Row>
            <Table.Row striped highlighted={highlightedRowId === 'row2'} className="transition-colors duration-300">
              <Table.Cell>Jane Smith</Table.Cell>
              <Table.Cell>Pending</Table.Cell>
              <Table.Cell>2024-01-16</Table.Cell>
            </Table.Row>
            <Table.Row striped highlighted={highlightedRowId === 'row3'} className="transition-colors duration-300">
              <Table.Cell>Bob Johnson</Table.Cell>
              <Table.Cell>Inactive</Table.Cell>
              <Table.Cell>2024-01-17</Table.Cell>
            </Table.Row>
          </Table.Body>
        </Table>
      </Table.Container>

      <p className="text-sm text-gray-600">
        Click buttons to highlight rows. Highlights automatically fade after 3 seconds.
      </p>
    </div>
  );
};

export const InteractiveHighlight: Story = {
  render: InteractiveHighlightDemo,
};

const TableGroups = () => {
  const [collapsedRows, setCollapsedRows] = useState<Record<string, boolean>>({
    row1: false,
    row2: false,
    row2_subgroup: false,
  });

  const onToggleRow = (rowKey: string) => {
    setCollapsedRows((prev) => ({
      ...prev,
      [rowKey]: !prev[rowKey],
    }));
  };

  return (
    <Table.Container>
      <Table>
        <Table.Heading>
          <Table.Row>
            <Table.Header>First header</Table.Header>
            <Table.Header>Second header</Table.Header>
          </Table.Row>
        </Table.Heading>
        <Table.Body>
          <Table.GroupRow
            title="This is a normal group - "
            label={<div className="text-xs font-medium">It has one level</div>}
            isCollapsed={collapsedRows.row1}
            onToggleRow={() => onToggleRow('row1')}
          >
            <Table.Row>
              <Table.Cell>A table cell</Table.Cell>
              <Table.Cell>Another table cell</Table.Cell>
            </Table.Row>
            <Table.Row>
              <Table.Cell>A table cell</Table.Cell>
              <Table.Cell>Another table cell</Table.Cell>
            </Table.Row>
          </Table.GroupRow>
          <Table.GroupRow
            title="This is multi-level group - "
            label={<div className="text-xs font-medium">It has 2 levels</div>}
            isCollapsed={collapsedRows.row2}
            onToggleRow={() => onToggleRow('row2')}
          >
            <Table.GroupRow
              title="This is the second level group - "
              label={<div className="text-xs font-medium">or sub-group</div>}
              isCollapsed={collapsedRows.row2_subgroup}
              onToggleRow={() => onToggleRow('row2_subgroup')}
              indentLevel={2}
            >
              <Table.Row>
                <Table.Cell>A table cell</Table.Cell>
                <Table.Cell>Another table cell</Table.Cell>
              </Table.Row>
              <Table.Row>
                <Table.Cell>A table cell</Table.Cell>
                <Table.Cell>Another table cell</Table.Cell>
              </Table.Row>
            </Table.GroupRow>
          </Table.GroupRow>
        </Table.Body>
      </Table>
    </Table.Container>
  );
};

export const TableWithGroupedRows: Story = {
  render: TableGroups,
  args: {
    ...Default.args,
  },
};
