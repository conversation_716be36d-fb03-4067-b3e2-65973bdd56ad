import React from 'react';
import type { Meta, StoryFn } from '@storybook/react-vite';
import Button from '../Button';
import * as PopoverMenu from './PopoverMenu';
import './index.css';

export default {
  title: 'Factories/PopoverMenu',
  component: PopoverMenu.Root,
  subcomponents: PopoverMenu,
} as Meta<typeof PopoverMenu.Root>;

const PopoverTemplate: StoryFn<typeof PopoverMenu.Root> = (args) => (
  <div className="relative flex flex-col">
    {[...Array(50)].map((item, index) => (
      <div key={`info-modal-item-${item}`}>{`Lorem ipsum dolor sit amet ${index}`}</div>
    ))}
    <div className="absolute top-5 right-5 z-popover">
      <PopoverMenu.Root {...args}>
        <PopoverMenu.Trigger asChild>
          <Button color="primary" variant="contained" size="md" type="submit">
            Open popover
          </Button>
        </PopoverMenu.Trigger>
        <PopoverMenu.Content>
          <div>This is the popover content</div>
        </PopoverMenu.Content>
      </PopoverMenu.Root>
    </div>
  </div>
);

export const OnBiggerScreens = {
  render: PopoverTemplate,
};

export const OnSmallerScreens = {
  render: PopoverTemplate,

  parameters: {
    viewport: {
      defaultViewport: 'iphonese2',
    },
  },
};

export const WithStaticPanel = {
  render: PopoverTemplate,

  args: {
    open: true,
    isStatic: true,
  },
};

export const WithButtonClickActionPanel = {
  render: PopoverTemplate,

  argTypes: {
    onClickButton: { action: 'button clicked!' },
  },
};
