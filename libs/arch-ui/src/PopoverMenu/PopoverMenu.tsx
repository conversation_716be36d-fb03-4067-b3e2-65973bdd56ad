import React from 'react';
import { Popover } from 'radix-ui';
import { cn } from '../utils/classes';

export const Root = Popover.Root;
export const Trigger = Popover.Trigger;
export const Close = Popover.Close;

export const Content = ({ children, ...props }: React.ComponentPropsWithoutRef<typeof Popover.Content>) => {
  return (
    <Popover.Portal>
      <>
        <div className="z-popover fixed inset-0 bg-black/50 z-overlay md:hidden" />
        <Popover.Content
          side="bottom"
          align="end"
          {...props}
          className={cn('z-popover min-w-52 overflow-y-auto', props.className)}
        >
          <div
            className={cn(
              'overflow-y-auto bg-white border border-gray-100 rounded-t-lg rounded-b-none',
              'md:relative md:rounded-lg md:shadow-lg',
              'fixed bottom-0 w-screen md:w-auto md:bottom-auto',
              'animate-in slide-in-from-bottom z-popover md:animate-none'
            )}
          >
            {children}
          </div>
        </Popover.Content>
      </>
    </Popover.Portal>
  );
};
