import React, { type ComponentProps } from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import * as PopoverMenu from './PopoverMenu';

const renderPopOver = (props?: Partial<ComponentProps<typeof PopoverMenu.Root>>) => {
  const renderComponent = render(
    <PopoverMenu.Root {...props}>
      <PopoverMenu.Trigger asChild>
        <button type="button">open popover</button>
      </PopoverMenu.Trigger>
      <PopoverMenu.Content>{props?.children}</PopoverMenu.Content>
    </PopoverMenu.Root>
  );

  return renderComponent;
};
describe('Popover', () => {
  describe.each([
    ['Larger', 'lg', 'larger screen', 1024],
    ['Smaller', 'sm', 'smaller screen', 620],
  ])('%s screens', (_, __, content, innerWidth) => {
    beforeEach(() => {
      global.window = Object.assign(window, { innerWidth });
    });

    it('should show options when user clicks listbox', async () => {
      renderPopOver({ children: <>{content}</> });

      await userEvent.click(screen.getByRole('button', { name: 'open popover' }));

      expect(screen.getByText(content)).toBeInTheDocument();
    });
  });
});
