import React from 'react';
import type { Meta, StoryFn } from '@storybook/react-vite';
import NotificationBadge from './NotificationBadge';

export default {
  title: 'Uncategorised/NotificationBadge',
  component: NotificationBadge,
} as Meta<typeof NotificationBadge>;

const Template: StoryFn<typeof NotificationBadge> = (props) => (
  <NotificationBadge className="bg-green-500 text-green-100" active {...props}>
    content
  </NotificationBadge>
);

export const Inactive = {
  render: Template,

  args: {
    active: false,
  },
};

export const WithoutText = {
  render: Template,
  args: {},
};

export const WithText = {
  render: Template,

  args: {
    text: '10',
  },
};
