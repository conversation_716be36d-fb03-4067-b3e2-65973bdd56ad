import React, { type <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react';
import classnames from 'clsx';
import { Slot } from 'radix-ui';

export interface StackedListItemProps {
  /**
   * The content to be rendered inside the list item
   */
  children: React.ReactNode;

  /**
   * Optional click handler for clicking on the stacked list item
   */
  onClick?: MouseEventHandler;

  /**
   * If true, the component will render the children as the root element
   */
  asChild?: boolean;
}

export const StackedListItem: React.FC<StackedListItemProps> = ({ children, onClick, asChild }) => {
  const elementClassNames = 'flex w-full items-center px-4 py-4 sm:px-6';

  if (asChild) {
    return <Slot.Root>{children}</Slot.Root>;
  }

  return (
    <li
      className={classnames('flex items-center', {
        'cursor-pointer': onClick,
      })}
    >
      {onClick ? (
        <button type="button" tabIndex={0} className={elementClassNames} onClick={onClick}>
          {children}
        </button>
      ) : (
        <div className={elementClassNames}>{children}</div>
      )}
    </li>
  );
};
