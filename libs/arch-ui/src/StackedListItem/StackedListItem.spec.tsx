import React from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import { StackedListItem } from './StackedListItem';

const children: string = 'Item Child';

const renderComponent = () => render(<StackedListItem>{children}</StackedListItem>);

describe('StackedListItem', () => {
  it('renders the children', () => {
    renderComponent();

    expect(screen.getByText(children)).toBeTruthy();
  });

  it('renders the children when clickable', () => {
    const onClick = jest.fn();

    render(<StackedListItem onClick={onClick}>{children}</StackedListItem>);

    expect(screen.getByText(children)).toBeTruthy();
  });

  it('triggers the click when clickable', () => {
    const onClick = jest.fn();

    render(<StackedListItem onClick={onClick}>{children}</StackedListItem>);

    const button = screen.getByRole('button');
    fireEvent.click(button);

    expect(onClick).toHaveBeenCalledTimes(1);
  });

  it('renders children as root element when asChild is passed', () => {
    const CustomComponent = ({ children }: { children: React.ReactNode }) => (
      <div data-testid="custom-element">{children}</div>
    );

    render(
      <StackedListItem asChild>
        <CustomComponent>{children}</CustomComponent>
      </StackedListItem>
    );

    expect(screen.getByTestId('custom-element')).toBeInTheDocument();
    expect(screen.getByText(children)).toBeInTheDocument();
    expect(screen.queryByRole('listitem')).not.toBeInTheDocument();
  });
});
