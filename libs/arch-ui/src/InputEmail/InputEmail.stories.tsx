import React from 'react';
import type { Meta } from '@storybook/react-vite';
import { InputEmail } from './InputEmail';

export default {
  title: 'Input/InputEmail',
  component: InputEmail,
} as Meta<typeof InputEmail>;

export const Standard = {
  args: {
    label: 'Example',
    name: 'example',
    id: 'example',
    required: true,
  },
};

export const WithValue = {
  args: {
    label: 'Example',
    name: 'example',
    id: 'example',
    value: '<EMAIL>',
  },
};

export const WithError = {
  args: {
    label: 'Example',
    name: 'example',
    id: 'example',
    error: 'This field has an error message!',
  },
};

export const Disabled = {
  args: {
    label: 'Example',
    name: 'example',
    id: 'example',
    value: '<EMAIL>',
    disabled: true,
  },
};

export const WithCornerAdornment = {
  args: {
    label: 'Example',
    name: 'example',
    id: 'example',
    value: '<EMAIL>',
    cornerAdornment: <>Corner Adornment</>,
  },
};

export const WithFullWidth = {
  args: {
    ...Standard.args,
    fullWidth: true,
  },
};
