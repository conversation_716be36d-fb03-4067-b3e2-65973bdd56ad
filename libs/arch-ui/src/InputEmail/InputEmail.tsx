import React from 'react';
import classNames from 'clsx';
import { ExclamationCircleIcon } from '../Icons/solid';
import type { InputBaseProps } from '../types/InputBase';

export const InputEmail = React.forwardRef<HTMLInputElement, InputBaseProps>(
  (
    {
      autoFocus = false,
      cornerAdornment,
      description,
      disabled,
      error,
      fullWidth,
      touched = true,
      id,
      label,
      name,
      onBlur,
      onChange,
      placeholder,
      required,
      value,
    },
    ref
  ) => {
    const showError = error && touched;

    const labelClassnames = classNames('block text-sm font-medium text-gray-700', {
      'w-full': fullWidth,
    });

    const inputClassnames = classNames(
      'appearance-none block w-full px-3 py-2 border rounded-md shadow-xs focus:outline-hidden sm:text-sm',
      {
        'pr-10 border-red-300 text-red-800 placeholder-red-300 focus:ring-red-400 focus:border-red-400': showError,
        'border-gray-300 placeholder-gray-400 focus:ring-indigo-500 focus:border-indigo-500': !showError,
        'opacity-50': disabled,
      }
    );

    return (
      <label htmlFor={id} className={labelClassnames}>
        <div className="flex justify-between">
          <div className="flex gap-x-1">
            {label}
            {required && <div className="text-red-600">*</div>}
          </div>
          {cornerAdornment && <div>{cornerAdornment}</div>}
        </div>
        <div className="relative mt-1">
          <input
            ref={ref}
            autoFocus={autoFocus}
            className={inputClassnames}
            disabled={disabled}
            id={id}
            name={name}
            onBlur={onBlur}
            onChange={onChange}
            placeholder={placeholder}
            required={required}
            value={value}
            type="email"
          />
          {showError && (
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
              <ExclamationCircleIcon className="h-5 w-5 text-red-600" aria-hidden="true" />
            </div>
          )}
        </div>
        {showError && <p className="mt-2 text-sm font-normal text-red-700">{error}</p>}
        {description && <p className="mt-2 text-sm font-normal text-gray-500">{description}</p>}
      </label>
    );
  }
);

InputEmail.displayName = 'InputEmail';
