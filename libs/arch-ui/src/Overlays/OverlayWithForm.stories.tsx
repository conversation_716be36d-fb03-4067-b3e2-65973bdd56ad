import React, { useState } from 'react';
import type { Meta, StoryFn } from '@storybook/react-vite';
import { Field, type FieldProps } from 'formik';
import Button from '../Button';
import InputText from '../InputText';
import { OverlayWithForm, type OverlayWithFormProps } from './OverlayWithForm';

export default {
  component: OverlayWithForm,
  title: 'Feedback/Overlays/OverlayWithForm',
  argTypes: { formProps: { onSubmit: { action: 'Form submitted!' } } },
  parameters: {
    docs: {
      inlineStories: false,
      iframeHeight: '500px',
    },
  },
} as Meta<OverlayWithFormProps>;

const Template: StoryFn<OverlayWithFormProps & { fieldsNo: number }> = ({ fieldsNo = 2, ...args }) => {
  const [open, setOpen] = useState(true);

  const closeOverlay = () => {
    setOpen(false);
  };

  return (
    <>
      <Button color="primary" variant="contained" size="md" onClick={() => setOpen(!open)}>
        Open OverlayWithForm
      </Button>
      {/* Actual OverlayWithForm code 👇🏼 */}
      <OverlayWithForm
        {...args}
        open={open}
        closeOverlay={closeOverlay}
        formProps={{ ...args.formProps, onSubmit: closeOverlay }}
      >
        {/* OverlayWithForm content 👇🏼 */}
        {[...Array(fieldsNo).keys()].map((num: number) => (
          <Field key={`example-${num}`} name={`example-${num}`}>
            {({ field, meta }: FieldProps) => <InputText label={`Example ${num}`} {...field} {...meta} />}
          </Field>
        ))}
      </OverlayWithForm>
    </>
  );
};

export const Defaults = {
  render: Template,

  args: {
    title: "I'm on the center..",
    subTitle: '... of the screen!',
    formProps: {
      onSubmit: () => {},
      initialValues: {
        example: 'Value',
      },
    },
    confirmCTA: 'Submit',
    cancelCTA: 'Cancel',
  },
};

export const MultipleFields = {
  render: Template,

  args: {
    fieldsNo: 7,
    title: 'I have lots of fields',
    subTitle: 'and my content overflows..',
    formProps: {
      onSubmit: () => {},
      initialValues: {
        example: 'Value',
      },
    },
    confirmCTA: 'Submit',
    cancelCTA: 'Cancel',
  },
};

export const CustomFooter = {
  render: Template,

  args: {
    title: 'I have a custom footer',
    subTitle: '...below!',
    formProps: {
      onSubmit: () => {},
      initialValues: {
        example: 'Value',
      },
    },
    footer: (
      <>
        <Button color="primary" variant="contained" size="md" type="submit">
          Custom Button
        </Button>
      </>
    ),
    confirmCTA: 'Submit',
    cancelCTA: 'Cancel',
  },
};
