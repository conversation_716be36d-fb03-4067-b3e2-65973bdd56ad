import React, { type ComponentProps } from 'react';
import { useMediaQuery } from '@shape-construction/hooks';
import type { FormikConfig, FormikValues } from 'formik';
import ModalBase from '../ModalBase';
import { breakpoints } from '../utils/breakpoints';
import OverlayForm from './OverlayForm';

export interface OverlayWithFormProps extends Omit<ComponentProps<typeof ModalBase.Root>, 'onClose'> {
  /**
   * Instructs the upper state to update open prop to false
   */
  closeOverlay: () => void;
  /**
   * Custom footer that replaces the default if passed
   */
  footer?: React.ReactNode;
  /**
   * Props to be passed down to OverlayForm
   */
  formProps: FormikConfig<FormikValues>;
  /**
   * Whether the content should be padded or not
   */
  noPadding?: boolean;
  /**
   * Whether the close button should appear
   */
  showCloseIcon?: boolean;
  /**
   * InnerText of paragraph below the title
   */
  subTitle?: string;
  /**
   * InnerText of h2 at the top of the Overlay
   */
  title: string;
  /**
   * Call To Action of the confirm button on OverlayForm
   */
  confirmCTA?: string;
  /**
   * Call To Action of the cancel button on OverlayForm
   */
  cancelCTA?: string;
}

export const OverlayWithForm: React.FC<OverlayWithFormProps> = ({
  children,
  footer,
  closeOverlay,
  formProps,
  noPadding,
  showCloseIcon = true,
  subTitle,
  title,
  confirmCTA,
  cancelCTA,
  ...props
}) => {
  const isLargeScreen = useMediaQuery(breakpoints.up('md'));

  return (
    <ModalBase.Root
      {...props}
      fullScreen={!isLargeScreen}
      fullWidth
      maxWidth={isLargeScreen ? 'lg' : 'none'}
      onClose={closeOverlay}
      roundBorders={isLargeScreen}
      outsidePad={isLargeScreen}
    >
      <ModalBase.Header bottomBorder onClose={showCloseIcon ? closeOverlay : undefined}>
        {title && <ModalBase.Title>{title}</ModalBase.Title>}
        {subTitle && <ModalBase.SubTitle>{subTitle}</ModalBase.SubTitle>}
      </ModalBase.Header>
      <ModalBase.Content className="px-0">
        <OverlayForm
          {...formProps}
          noPadding={noPadding}
          closeOverlay={closeOverlay}
          formClassNames="bg-white"
          footer={footer}
          confirmCTA={confirmCTA}
          cancelCTA={cancelCTA}
        >
          {children}
        </OverlayForm>
      </ModalBase.Content>
    </ModalBase.Root>
  );
};
