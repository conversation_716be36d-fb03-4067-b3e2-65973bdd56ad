import React, { type <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react';
import classNames from 'clsx';
import { Form, Formik, type FormikConfig, type FormikValues } from 'formik';
import Button from '../Button';
export interface OverlayFormProps extends FormikConfig<FormikValues> {
  children?: React.ReactNode;
  /**
   * Instructs the upper state to update open prop to false
   */
  closeOverlay: () => void;
  /**
   * Call To Action of the confirm button
   */
  confirmCTA?: string;
  /**
   * Call To Action of the cancel button
   */
  cancelCTA?: string;
  /**
   * Classnames to be applied in form element
   * @default false
   */
  formClassNames: string;
  /**
   * Called when confirm button is clicked
   */
  onConfirm?: MouseEventHandler;
  /**
   * Whether the content should be padded or not
   */
  noPadding?: boolean;
  /**
   * A custom footer that replaces the default if passed
   */
  footer?: React.ReactNode;
}

const OverlayForm: React.FC<OverlayFormProps> = React.forwardRef(
  ({ children, closeOverlay, confirmCTA, cancelCTA, formClassNames, noPadding, footer, ...formProps }, ref) => (
    <Formik {...formProps}>
      {({ isSubmitting }) => (
        <Form className={formClassNames} ref={ref as React.Ref<any>}>
          <div
            className={classNames('mb-[15px] flex h-full flex-col gap-y-3 overflow-y-auto', {
              'px-4 py-6': !noPadding,
            })}
            data-testid="field-wrapper"
          >
            {children}
          </div>
          <footer className="flex w-full items-center justify-end gap-3 border-t bg-white px-4 pt-3 pb-6 sm:pb-3">
            {footer || (
              <>
                <Button color="secondary" variant="outlined" size="md" onClick={closeOverlay} disabled={isSubmitting}>
                  {cancelCTA}
                </Button>
                <Button color="primary" variant="contained" size="md" type="submit" disabled={isSubmitting}>
                  {confirmCTA}
                </Button>
              </>
            )}
          </footer>
        </Form>
      )}
    </Formik>
  )
);

OverlayForm.displayName = 'OverlayForm';

export default OverlayForm;
