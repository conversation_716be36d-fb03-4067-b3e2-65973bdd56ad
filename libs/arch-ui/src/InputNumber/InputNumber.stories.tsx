import React from 'react';
import type { Meta } from '@storybook/react-vite';
import { InputNumber } from './InputNumber';

export default {
  title: 'Input/InputNumber',
  component: InputNumber,
} as Meta<typeof InputNumber>;

export const Standard = {
  args: {
    label: 'Example',
    name: 'example',
    id: 'example',
    required: true,
  },
};

export const WithValue = {
  args: {
    ...Standard.args,
    value: 1,
  },
};

export const WithError = {
  args: {
    ...Standard.args,
    error: 'This field has an error message!',
  },
};

export const Disabled = {
  args: {
    ...Standard.args,
    value: 1,
    disabled: true,
  },
};

export const WithCornerAdornment = {
  args: {
    ...Standard.args,
    value: 1,
    cornerAdornment: <>Corner Adornment</>,
  },
};

export const WithCornerAdornmentAndNoLabel = {
  args: {
    ...Standard.args,
    value: 1,
    cornerAdornment: <>Corner Adornment</>,
  },
};

export const WithFullWidth = {
  args: {
    ...Standard.args,
    fullWidth: true,
  },
};
