import React from 'react';
import { render, screen } from '@testing-library/react';
import { InputNumber } from './InputNumber';

describe('InputNumber', () => {
  it('renders the input with label', () => {
    render(<InputNumber name="example" label="Example" />);
    expect(screen.getByLabelText('Example')).toBeTruthy();
  });

  it('renders the input with required option', () => {
    render(<InputNumber name="example" label="Example" required />);
    expect(screen.getByLabelText('Example*').hasAttribute('required')).toBeTruthy();
  });

  it('renders the input with value', () => {
    render(<InputNumber name="example" label="Example" value={1} onChange={() => {}} />);
    expect(screen.getByLabelText('Example')).toHaveValue(1);
  });

  it('does not render input with invalid value', () => {
    render(<InputNumber name="example" label="Example" value={'abc'} onChange={() => {}} />);
    expect(screen.getByLabelText('Example')).toHaveValue(null);
  });

  it('renders the input with an error message', () => {
    render(<InputNumber name="example" label="Example" error="This is not a valid value" />);

    expect(screen.getByText('This is not a valid value')).toBeTruthy();
  });

  it('does not render the input with an error message if untouched', () => {
    render(
      <InputNumber
        name="example"
        label="Example"
        value="abc"
        error="This is not a valid value"
        onChange={() => {}}
        touched={false}
      />
    );

    expect(screen.queryByText('This is not a valid value')).not.toBeInTheDocument();
  });

  it('renders the input disabled', () => {
    render(<InputNumber name="example" label="Example" value={1} disabled onChange={() => {}} />);
    expect(screen.getByLabelText('Example')).toBeDisabled();
  });

  it('renders the input with corner adornment', () => {
    render(
      <InputNumber
        name="example"
        label="Example"
        value={1}
        cornerAdornment={<>Corner Adornment</>}
        onChange={() => {}}
      />
    );
    expect(screen.getByText('Corner Adornment')).toBeInTheDocument();
  });

  it('sets the input value using ref', async () => {
    const ref = React.createRef<HTMLInputElement>();

    render(<InputNumber ref={ref} name="example" label="Example" />);
    if (ref.current) {
      ref.current.value = '1';
    }
    expect(screen.getByLabelText('Example')).toHaveValue(1);
  });
});
