import React from 'react';
import type { Meta, StoryObj } from '@storybook/react-vite';
import { Pulse, type PulseProps } from './Pulse';

const meta: Meta<typeof Pulse> = {
  title: 'Uncategorised/Pulse',
  component: Pulse,
};
export default meta;
type Story = StoryObj<PulseProps>;

export const Default: Story = {
  args: {
    onClick: () => alert('Pulse clicked'),
    ariaLabel: 'Pulse',
  },
  render: (args) => <Pulse {...args} />,
};
