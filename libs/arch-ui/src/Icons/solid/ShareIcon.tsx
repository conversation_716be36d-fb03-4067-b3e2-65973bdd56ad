import type { SVGProps } from 'react';
import * as React from 'react';

const ShareIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/share">
      <path
        id="Icon"
        d="M15.6 5.40039C15.6 3.74354 16.9432 2.40039 18.6 2.40039C20.2569 2.40039 21.6 3.74354 21.6 5.40039C21.6 7.05725 20.2569 8.40039 18.6 8.40039C17.7532 8.40039 16.9883 8.04953 16.4429 7.48525L8.36263 11.5254C8.38724 11.6801 8.40003 11.8387 8.40003 12.0004C8.40003 12.1621 8.38724 12.3208 8.36261 12.4755L16.4428 16.5156C16.9883 15.9513 17.7532 15.6004 18.6 15.6004C20.2569 15.6004 21.6 16.9435 21.6 18.6004C21.6 20.2572 20.2569 21.6004 18.6 21.6004C16.9432 21.6004 15.6 20.2572 15.6 18.6004C15.6 18.4387 15.6128 18.2801 15.6374 18.1254L7.55719 14.0853C7.01171 14.6495 6.24684 15.0004 5.40002 15.0004C3.74317 15.0004 2.40002 13.6572 2.40002 12.0004C2.40002 10.3435 3.74317 9.00039 5.40002 9.00039C6.24688 9.00039 7.01177 9.35128 7.55726 9.9156L15.6374 5.87551C15.6128 5.72076 15.6 5.56207 15.6 5.40039Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default ShareIcon;
