import type { SVGProps } from 'react';
import * as React from 'react';

const ShapeProjectGalleryIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/shape-project-gallery">
      <path
        id="Icon"
        d="M21.8893 10.4659C21.5607 9.86783 21.0141 9.51063 20.4272 9.51063H8.3451C7.69286 9.51063 7.09563 9.95184 6.7867 10.6618L4.47293 15.9825V5.94087C4.47293 5.54473 4.68707 5.26876 4.87862 5.26876H8.84606C8.94184 5.26876 9.08083 5.44291 9.08083 5.71554C9.08083 6.55512 9.64564 7.23836 10.3391 7.23836H17.2819C17.3467 7.23836 17.4661 7.38246 17.4661 7.61781V8.67605H18.9395V7.61781C18.9395 6.48835 18.1959 5.56921 17.2819 5.56921H10.5503C10.4894 4.47035 9.74829 3.59961 8.84606 3.59961H4.87911C3.8428 3.59961 3 4.65006 3 5.94087V17.1882C3 18.913 4.081 20.3245 5.43115 20.3963C5.46701 20.3979 5.50286 20.3996 5.5392 20.3996H17.6046C18.2568 20.3996 18.8541 19.9584 19.163 19.2484L21.9861 12.7566C22.3029 12.0283 22.2655 11.1503 21.8893 10.4659ZM20.6634 12.0188L17.8403 18.5107C17.7824 18.6442 17.6896 18.7305 17.6036 18.7305H5.52201C5.52201 18.7305 5.51759 18.7299 5.51514 18.7293C5.41544 18.7249 5.34471 18.6236 5.31082 18.5624C5.22929 18.4139 5.1733 18.1485 5.28528 17.8909L8.10788 11.3995C8.16583 11.266 8.25866 11.1798 8.34461 11.1798H20.4267C20.5249 11.1798 20.5956 11.271 20.6379 11.3478C20.7194 11.4964 20.7754 11.7612 20.6634 12.0188Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default ShapeProjectGalleryIcon;
