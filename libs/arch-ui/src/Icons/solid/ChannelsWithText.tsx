import type { SVGProps } from 'react';
import * as React from 'react';

const ChannelsWithText = (props: SVGProps<SVGSVGElement>) => (
  <svg width={638} height={133} viewBox="0 0 638 133" fill="none" aria-hidden="true" {...props}>
    <g id="Color=Primary, Type=Icon + Text, Size=Lg">
      <g id="logo / simple / shape-logo-default">
        <g id="CHANNELS">
          <path
            id="Vector"
            d="M575.668 95.8151C570.117 95.8151 565.447 94.5875 561.657 92.1322C557.867 89.6236 555.199 86.2342 553.651 81.9642L563.098 76.4399C565.287 82.1511 569.583 85.0066 575.988 85.0066C579.084 85.0066 581.353 84.4462 582.794 83.3253C584.235 82.2044 584.955 80.79 584.955 79.082C584.955 77.1071 584.075 75.5859 582.313 74.5184C580.552 73.3975 577.403 72.1966 572.866 70.9156C570.357 70.1683 568.222 69.4211 566.461 68.6738C564.753 67.9266 563.018 66.9391 561.257 65.7115C559.549 64.4305 558.241 62.8292 557.334 60.9077C556.426 58.9862 555.973 56.7445 555.973 54.1825C555.973 49.1118 557.761 45.082 561.337 42.093C564.966 39.0506 569.316 37.5294 574.387 37.5294C578.924 37.5294 582.9 38.6503 586.316 40.892C589.786 43.0804 592.481 46.1495 594.403 50.0993L585.115 55.4635C582.874 50.6597 579.298 48.2578 574.387 48.2578C572.092 48.2578 570.277 48.7916 568.943 49.8591C567.662 50.8732 567.021 52.2076 567.021 53.8622C567.021 55.6236 567.742 57.0647 569.183 58.1856C570.677 59.2531 573.506 60.4274 577.67 61.7084C579.378 62.2421 580.659 62.6691 581.513 62.9894C582.42 63.2562 583.621 63.7099 585.115 64.3504C586.663 64.9376 587.838 65.498 588.638 66.0318C589.492 66.5655 590.453 67.2861 591.52 68.1934C592.588 69.1008 593.389 70.0349 593.922 70.9956C594.509 71.9564 594.99 73.1306 595.363 74.5184C595.79 75.8528 596.004 77.3206 596.004 78.9219C596.004 84.0992 594.109 88.2091 590.32 91.2515C586.583 94.2939 581.699 95.8151 575.668 95.8151Z"
            fill="#1F2937"
          />
          <path id="Vector_2" d="M525.092 84.1259H546.469V94.6942H514.043V38.6503H525.092V84.1259Z" fill="#1F2937" />
          <path
            id="Vector_3"
            d="M477.833 84.1259H501.452V94.6942H466.785V38.6503H501.051V49.2186H477.833V61.1479H499.05V71.5561H477.833V84.1259Z"
            fill="#1F2937"
          />
          <path
            id="Vector_4"
            d="M440.536 38.6503H451.585V94.6942H443.178L419.159 60.4274V94.6942H408.11V38.6503H416.517L440.536 72.8371V38.6503Z"
            fill="#1F2937"
          />
          <path
            id="Vector_5"
            d="M381.862 38.6503H392.91V94.6942H384.504L360.485 60.4274V94.6942H349.436V38.6503H357.843L381.862 72.8371V38.6503Z"
            fill="#1F2937"
          />
          <path
            id="Vector_6"
            d="M327.181 94.6942L323.819 84.6063H301.481L298.119 94.6942H286.189L305.805 38.6503H319.495L339.191 94.6942H327.181ZM305.004 74.2782H320.376L312.69 51.3803L305.004 74.2782Z"
            fill="#1F2937"
          />
          <path
            id="Vector_7"
            d="M264.97 38.6503H275.938V94.6942H264.97V71.476H244.153V94.6942H233.105V38.6503H244.153V60.9077H264.97V38.6503Z"
            fill="#1F2937"
          />
          <path
            id="Vector_8"
            d="M197.415 95.8151C188.981 95.8151 182.016 93.0129 176.518 87.4085C171.021 81.8041 168.272 74.892 168.272 66.6723C168.272 58.3991 171.021 51.487 176.518 45.936C182.016 40.3316 188.981 37.5294 197.415 37.5294C202.485 37.5294 207.156 38.7304 211.426 41.1322C215.749 43.4807 219.112 46.6833 221.514 50.7398L211.986 56.2641C210.598 53.7555 208.624 51.8073 206.062 50.4195C203.5 48.9784 200.617 48.2578 197.415 48.2578C191.97 48.2578 187.567 49.9658 184.204 53.3818C180.895 56.7978 179.24 61.228 179.24 66.6723C179.24 72.0631 180.895 76.4666 184.204 79.8826C187.567 83.2986 191.97 85.0066 197.415 85.0066C200.617 85.0066 203.5 84.3127 206.062 82.925C208.677 81.4839 210.652 79.5357 211.986 77.0804L221.514 82.6047C219.112 86.6613 215.776 89.8904 211.506 92.2923C207.236 94.6408 202.539 95.8151 197.415 95.8151Z"
            fill="#1F2937"
          />
        </g>
      </g>
      <path
        id="Union"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M83.9032 111.613H83.9217C109.09 111.613 129.573 91.1358 129.584 65.9656C129.588 53.7686 124.843 42.2996 116.222 33.6713C107.601 25.0433 96.1362 20.2889 83.9399 20.2841C58.7528 20.2841 38.2687 40.7603 38.2587 65.9287C38.2553 74.5538 40.6693 82.9541 45.24 90.222L45.3888 90.4585C45.9859 91.4082 46.1572 92.5649 45.8609 93.6468L43.5447 102.105C42.7307 105.077 45.44 107.816 48.4212 107.034L57.376 104.686C58.4119 104.414 59.5135 104.569 60.4345 105.115L60.6626 105.251C67.6732 109.41 75.7096 111.61 83.9032 111.613ZM83.9212 11.0086C98.6175 11.0144 112.411 16.7341 122.784 27.1151C133.156 37.4959 138.865 51.2944 138.86 65.9691C138.847 96.2505 114.2 120.889 83.9217 120.889H83.8993C75.2376 120.886 66.7183 118.839 59.0517 114.945C58.1419 114.482 57.0961 114.349 56.109 114.607L41.9385 118.323C35.9763 119.887 30.5578 114.409 32.1858 108.464L35.8749 94.9934C36.1573 93.9621 36.0091 92.864 35.5031 91.9221C31.2239 83.9572 28.9794 75.0437 28.9831 65.9253C28.9952 35.6442 53.6403 11.0086 83.9212 11.0086ZM67.3132 57.2086C72.3529 48.3694 74.8728 43.9498 77.956 42.1249C82.1556 39.6394 87.3522 39.6394 91.5518 42.1249C94.635 43.9498 97.1549 48.3694 102.195 57.2086C107.234 66.0479 109.754 70.4675 109.773 74.0838C109.799 79.0094 107.201 83.5666 102.975 86.0066C99.8732 87.7981 94.8334 87.7981 84.7539 87.7981C74.6744 87.7981 69.6347 87.7981 66.5324 86.0066C62.3069 83.5666 59.7086 79.0094 59.7345 74.0838C59.7535 70.4675 62.2734 66.0479 67.3132 57.2086ZM77.1799 62.0666C80.5912 56.0835 82.2969 53.0919 84.8554 53.0919C87.4139 53.0919 89.1195 56.0835 92.5309 62.0666L93.1709 63.1892C96.5822 69.1723 98.2879 72.1639 97.0086 74.4076C95.7294 76.6513 92.3181 76.6513 85.4954 76.6513H84.2153C77.3927 76.6513 73.9814 76.6513 72.7021 74.4076C71.4229 72.1639 73.1285 69.1723 76.5399 63.1892L77.1799 62.0666Z"
        fill="#6366F1"
      />
    </g>
  </svg>
);
export default ChannelsWithText;
