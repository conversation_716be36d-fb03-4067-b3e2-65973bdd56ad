import type { SVGProps } from 'react';
import * as React from 'react';

const RssIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/rss">
      <g id="Icon">
        <path
          d="M4.49998 3.59961C4.00292 3.59961 3.59998 4.00255 3.59998 4.49961V5.09961C3.59998 5.59667 4.00292 5.99961 4.49998 5.99961H4.79998C12.0901 5.99961 18 11.9094 18 19.1996V19.4996C18 19.9967 18.4029 20.3996 18.9 20.3996H19.5C19.997 20.3996 20.4 19.9967 20.4 19.4996V19.1996C20.4 10.584 13.4156 3.59961 4.79998 3.59961H4.49998Z"
          fill="currentColor"
        />
        <path
          d="M3.59998 10.4996C3.59998 10.0026 4.00292 9.59961 4.49998 9.59961H4.79998C10.1019 9.59961 14.4 13.8977 14.4 19.1996V19.4996C14.4 19.9967 13.997 20.3996 13.5 20.3996H12.9C12.4029 20.3996 12 19.9967 12 19.4996V19.1996C12 15.2232 8.77642 11.9996 4.79998 11.9996H4.49998C4.00292 11.9996 3.59998 11.5967 3.59998 11.0996V10.4996Z"
          fill="currentColor"
        />
        <path
          d="M8.39997 17.9996C8.39997 19.3251 7.32546 20.3996 5.99998 20.3996C4.67449 20.3996 3.59998 19.3251 3.59998 17.9996C3.59998 16.6741 4.67449 15.5996 5.99998 15.5996C7.32546 15.5996 8.39997 16.6741 8.39997 17.9996Z"
          fill="currentColor"
        />
      </g>
    </g>
  </svg>
);
export default RssIcon;
