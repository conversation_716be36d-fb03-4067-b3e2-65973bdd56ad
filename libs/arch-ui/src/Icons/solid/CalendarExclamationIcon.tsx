import type { SVGProps } from 'react';
import * as React from 'react';

const CalendarExclamationIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/calendar-exclamation">
      <path
        id="Icon"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.20002 2.3999C6.53728 2.3999 6.00002 2.93716 6.00002 3.5999V4.7999H4.80002C3.47454 4.7999 2.40002 5.87442 2.40002 7.1999V19.1999C2.40002 20.5254 3.47454 21.5999 4.80002 21.5999H19.2C20.5255 21.5999 21.6 20.5254 21.6 19.1999V7.1999C21.6 5.87442 20.5255 4.7999 19.2 4.7999H18V3.5999C18 2.93716 17.4628 2.3999 16.8 2.3999C16.1373 2.3999 15.6 2.93716 15.6 3.5999V4.7999H8.40002V3.5999C8.40002 2.93716 7.86277 2.3999 7.20002 2.3999ZM12.9 8.69991C12.9 8.20285 12.497 7.79991 12 7.79991C11.5029 7.79991 11.1 8.20285 11.1 8.69991V12.8999C11.1 13.397 11.5029 13.7999 12 13.7999C12.497 13.7999 12.9 13.397 12.9 12.8999V8.69991ZM13.2 17.3999C13.2 18.0626 12.6627 18.5999 12 18.5999C11.3372 18.5999 10.8 18.0626 10.8 17.3999C10.8 16.7372 11.3372 16.1999 12 16.1999C12.6627 16.1999 13.2 16.7372 13.2 17.3999Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default CalendarExclamationIcon;
