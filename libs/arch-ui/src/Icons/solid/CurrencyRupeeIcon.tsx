import type { SVGProps } from 'react';
import * as React from 'react';

const CurrencyRupeeIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/currency-rupee">
      <path
        id="Icon"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12 21.6004C17.302 21.6004 21.6 17.3023 21.6 12.0004C21.6 6.69846 17.302 2.40039 12 2.40039C6.69809 2.40039 2.40002 6.69846 2.40002 12.0004C2.40002 17.3023 6.69809 21.6004 12 21.6004ZM7.20005 6.90039C7.20005 6.40334 7.603 6.00039 8.10005 6.00039H15.9001C16.3971 6.00039 16.8001 6.40334 16.8001 6.90039C16.8001 7.39745 16.3971 7.80039 15.9001 7.80039H13.3473C13.8275 8.39946 14.1671 9.11616 14.3158 9.90039H15.9C16.3971 9.90039 16.8 10.3033 16.8 10.8004C16.8 11.2974 16.3971 11.7004 15.9 11.7004H14.3158C13.933 13.7183 12.2872 15.289 10.2317 15.5592L12.3364 17.664C12.6879 18.0155 12.6879 18.5853 12.3364 18.9368C11.985 19.2883 11.4151 19.2883 11.0637 18.9368L7.46366 15.3368C7.20626 15.0794 7.12926 14.6923 7.26856 14.356C7.40786 14.0197 7.73604 13.8004 8.10005 13.8004H9.60005C10.9433 13.8004 12.0804 12.9175 12.4627 11.7004H8.10003C7.60297 11.7004 7.20003 11.2974 7.20003 10.8004C7.20003 10.3033 7.60297 9.90039 8.10003 9.90039H12.4627C12.0804 8.68326 10.9433 7.80039 9.60005 7.80039H8.10005C7.603 7.80039 7.20005 7.39745 7.20005 6.90039Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default CurrencyRupeeIcon;
