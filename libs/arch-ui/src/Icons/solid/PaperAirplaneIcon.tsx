import type { SVGProps } from 'react';
import * as React from 'react';

const PaperAirplaneIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/paper-airplane">
      <path
        id="Icon"
        d="M3.72626 2.74622C3.4243 2.70342 3.1212 2.81646 2.92101 3.04654C2.72081 3.27662 2.65075 3.59243 2.73488 3.88558L4.43127 9.79663C4.65274 10.5684 5.35855 11.1001 6.16143 11.1001H13.5C13.997 11.1001 14.4 11.503 14.4 12.0001C14.4 12.4972 13.997 12.9001 13.5 12.9001H6.16144C5.35856 12.9001 4.65275 13.4318 4.43128 14.2036L2.73488 20.1146C2.65075 20.4078 2.72081 20.7236 2.92101 20.9537C3.1212 21.1838 3.4243 21.2968 3.72626 21.254C10.7316 20.2611 17.0651 17.1826 22.0779 12.6689C22.2675 12.4983 22.3757 12.2552 22.3757 12.0001C22.3757 11.745 22.2675 11.502 22.0779 11.3313C17.0651 6.81765 10.7316 3.73912 3.72626 2.74622Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default PaperAirplaneIcon;
