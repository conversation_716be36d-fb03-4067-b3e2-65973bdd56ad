import type { SVGProps } from 'react';
import * as React from 'react';

const BookmarkSquareIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/bookmark-square">
      <path
        id="Icon"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.10002 2.40039C3.60886 2.40039 2.40002 3.60922 2.40002 5.10039V18.9004C2.40002 20.3916 3.60886 21.6004 5.10002 21.6004H18.9C20.3912 21.6004 21.6 20.3916 21.6 18.9004V5.10039C21.6 3.60922 20.3912 2.40039 18.9 2.40039H5.10002ZM7.20002 15.9004V4.20039H16.8V15.9004C16.8 16.2073 16.6436 16.4931 16.3851 16.6585C16.1266 16.8239 15.8015 16.8462 15.5229 16.7176L12 15.0916L8.47718 16.7176C8.1985 16.8462 7.8735 16.8239 7.61497 16.6585C7.35643 16.4931 7.20002 16.2073 7.20002 15.9004Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default BookmarkSquareIcon;
