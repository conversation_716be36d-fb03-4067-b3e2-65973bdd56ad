import type { SVGProps } from 'react';
import * as React from 'react';

const RectangleGroupIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/rectangle-group">
      <g id="Icon">
        <path
          d="M2.99995 3.59961C2.00584 3.59961 1.19995 4.4055 1.19995 5.39961V10.1996C1.19995 11.1937 2.00584 11.9996 2.99995 11.9996H10.2C11.1941 11.9996 12 11.1937 12 10.1996V5.39961C12 4.4055 11.1941 3.59961 10.2 3.59961H2.99995Z"
          fill="currentColor"
        />
        <path
          d="M16.2 5.99961C15.2058 5.99961 14.4 6.8055 14.4 7.79961V16.1996C14.4 17.1937 15.2058 17.9996 16.2 17.9996H21C21.9941 17.9996 22.8 17.1937 22.8 16.1996V7.79961C22.8 6.8055 21.9941 5.99961 21 5.99961H16.2Z"
          fill="currentColor"
        />
        <path
          d="M4.19995 14.3996C3.20584 14.3996 2.39995 15.2055 2.39995 16.1996V18.5996C2.39995 19.5937 3.20584 20.3996 4.19995 20.3996H11.4C12.3941 20.3996 13.2 19.5937 13.2 18.5996V16.1996C13.2 15.2055 12.3941 14.3996 11.4 14.3996H4.19995Z"
          fill="currentColor"
        />
      </g>
    </g>
  </svg>
);
export default RectangleGroupIcon;
