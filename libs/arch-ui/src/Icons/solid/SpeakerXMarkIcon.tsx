import type { SVGProps } from 'react';
import * as React from 'react';

const SpeakerXMarkIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/speaker-x-mark">
      <g id="Icon">
        <path
          d="M11.457 3.67345C11.7866 3.81587 12 4.14057 12 4.49963V19.4996C12 19.8587 11.7866 20.1834 11.457 20.3258C11.1274 20.4682 10.7447 20.4011 10.4832 20.155L5.64311 15.5996H3.80072C3.42872 15.5996 3.09506 15.3708 2.9611 15.0237C2.59844 14.0842 2.40002 13.064 2.40002 11.9996C2.40002 10.9352 2.59844 9.91505 2.9611 8.97553C3.09506 8.62849 3.42872 8.39963 3.80072 8.39963H5.64311L10.4832 3.84425C10.7447 3.59816 11.1274 3.53104 11.457 3.67345Z"
          fill="currentColor"
        />
        <path
          d="M15.9364 8.66325C15.585 8.31178 15.0151 8.31178 14.6636 8.66325C14.3122 9.01472 14.3122 9.58457 14.6636 9.93604L16.7272 11.9996L14.6636 14.0632C14.3122 14.4147 14.3122 14.9846 14.6636 15.336C15.0151 15.6875 15.585 15.6875 15.9364 15.336L18 13.2724L20.0636 15.336C20.4151 15.6875 20.985 15.6875 21.3364 15.336C21.6879 14.9846 21.6879 14.4147 21.3364 14.0632L19.2728 11.9996L21.3364 9.93604C21.6879 9.58457 21.6879 9.01472 21.3364 8.66325C20.985 8.31178 20.4151 8.31178 20.0636 8.66325L18 10.7269L15.9364 8.66325Z"
          fill="currentColor"
        />
      </g>
    </g>
  </svg>
);
export default SpeakerXMarkIcon;
