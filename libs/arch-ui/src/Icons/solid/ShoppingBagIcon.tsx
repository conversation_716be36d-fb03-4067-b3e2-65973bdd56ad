import type { SVGProps } from 'react';
import * as React from 'react';

const ShoppingBagIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/shopping-bag">
      <path
        id="Icon"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.20005 5V6H5.6005C4.51121 6 3.60275 6.69407 3.50839 7.5984L2.51709 17.0984C2.41039 18.1209 3.3776 19 4.60919 19H19.3914C20.623 19 21.5902 18.1209 21.4835 17.0984L20.4922 7.5984C20.3979 6.69407 19.4894 6 18.4001 6H16.8001V5C16.8001 2.79086 14.651 1 12 1C9.34908 1 7.20005 2.79086 7.20005 5ZM12 2.5C10.3432 2.5 9.00005 3.61929 9.00005 5V6H15.0001V5C15.0001 3.61929 13.6569 2.5 12 2.5ZM8.85005 10C8.85005 11.2585 10.1265 12.5 12 12.5C13.8736 12.5 15.15 11.2585 15.15 10V8.75C15.15 8.33579 15.4858 8 15.9 8C16.3143 8 16.65 8.33579 16.65 8.75V10C16.65 12.3314 14.4343 14 12 14C9.56577 14 7.35005 12.3314 7.35005 10V8.75C7.35005 8.33579 7.68583 8 8.10005 8C8.51426 8 8.85005 8.33579 8.85005 8.75V10Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default ShoppingBagIcon;
