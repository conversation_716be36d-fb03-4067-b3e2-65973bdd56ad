import type { SVGProps } from 'react';
import * as React from 'react';

const PhoneArrowDownLeftIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/phone-arrow-down-left">
      <g id="Icon">
        <path
          d="M4.20002 2.40039C3.20591 2.40039 2.40002 3.20628 2.40002 4.20039V6.00039C2.40002 7.37866 2.57898 8.71649 2.91541 9.9913C4.34292 15.4004 8.6 19.6575 14.0091 21.085C15.2839 21.4214 16.6218 21.6004 18 21.6004H19.8C20.7941 21.6004 21.6 20.7945 21.6 19.8004V18.4223C21.6 17.5787 21.0141 16.8482 20.1905 16.6652L16.3229 15.8058C15.3932 15.5991 14.4634 16.1526 14.2017 17.0684L13.8819 18.1877C13.7411 18.6807 13.216 18.9591 12.7421 18.7636C9.34954 17.3644 6.63604 14.6509 5.23678 11.2583C5.04131 10.7844 5.31974 10.2593 5.81268 10.1185L6.93203 9.79869C7.84783 9.53703 8.40128 8.60724 8.19466 7.67747L7.33521 3.80992C7.15219 2.98635 6.42173 2.40039 5.57807 2.40039H4.20002Z"
          fill="currentColor"
        />
        <path
          d="M20.0636 2.664C20.4151 2.31252 20.985 2.31252 21.3364 2.664C21.6879 3.01547 21.6879 3.58532 21.3364 3.93679L17.4728 7.80039L20.7 7.80039C21.1971 7.80039 21.6 8.20334 21.6 8.70039C21.6 9.19745 21.1971 9.60039 20.7 9.60039L15.3 9.60039C14.803 9.60039 14.4 9.19745 14.4 8.70039V3.30039C14.4 2.80333 14.803 2.40039 15.3 2.40039C15.7971 2.40039 16.2 2.80333 16.2 3.30039V6.5276L20.0636 2.664Z"
          fill="currentColor"
        />
      </g>
    </g>
  </svg>
);
export default PhoneArrowDownLeftIcon;
