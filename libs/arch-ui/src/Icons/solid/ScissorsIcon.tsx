import type { SVGProps } from 'react';
import * as React from 'react';

const ScissorsIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/scissors">
      <g id="Icon">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M1.76249 4.50012C0.602693 6.50894 1.29097 9.07762 3.2998 10.2374C5.04109 11.2428 7.20305 10.8595 8.50341 9.4312L9.56337 10.0432C9.59315 10.1543 9.73916 10.1834 9.81409 10.0961C9.99558 9.88463 10.1967 9.69002 10.4149 9.51508C10.7585 9.23971 10.7748 8.66411 10.3935 8.44396L9.4034 7.87235C9.99015 6.03207 9.24109 3.96814 7.4998 2.96281C5.49097 1.80301 2.92229 2.49129 1.76249 4.50012ZM4.1998 8.67858C3.0519 8.01584 2.6586 6.54802 3.32134 5.40012C3.98408 4.25221 5.4519 3.85891 6.5998 4.52165C7.7477 5.1844 8.141 6.65221 7.47826 7.80012C6.81552 8.94802 5.3477 9.34132 4.1998 8.67858Z"
          fill="currentColor"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M11.9466 9.98615C11.0084 10.382 10.3054 11.1889 10.0419 12.1725L9.5637 13.9571L8.50351 14.5692C7.20316 13.1408 5.04114 12.7575 3.2998 13.7629C1.29097 14.9227 0.602693 17.4913 1.76249 19.5002C2.92229 21.509 5.49097 22.1973 7.4998 21.0375C9.24104 20.0322 9.99011 17.9683 9.40345 16.1281L22.2181 8.72954C22.5333 8.54753 22.7079 8.19354 22.6604 7.83264C22.6129 7.47174 22.3526 7.17499 22.001 7.08078L21.1572 6.85467C20.4484 6.66474 19.6964 6.7165 19.0203 7.00176L11.9466 9.98615ZM3.32134 18.6002C2.6586 17.4523 3.0519 15.9845 4.1998 15.3217C5.3477 14.659 6.81552 15.0523 7.47826 16.2002C8.141 17.3481 7.7477 18.8159 6.5998 19.4786C5.4519 20.1414 3.98408 19.7481 3.32134 18.6002Z"
          fill="currentColor"
        />
        <path
          d="M15.0241 14.2684C14.5949 14.5162 14.6342 15.1481 15.0909 15.3408L19.02 16.9985C19.6961 17.2838 20.448 17.3355 21.1569 17.1456L22.0007 16.9195C22.3523 16.8253 22.6125 16.5285 22.6601 16.1676C22.7076 15.8067 22.533 15.4527 22.2178 15.2707L18.0529 12.8661C17.8673 12.759 17.6385 12.759 17.4529 12.8661L15.0241 14.2684Z"
          fill="currentColor"
        />
      </g>
    </g>
  </svg>
);
export default ScissorsIcon;
