import type { SVGProps } from 'react';
import * as React from 'react';

const ShieldExclamationIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/shield-exclamation">
      <path
        id="Icon"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.338 2.62688C12.143 2.46292 11.857 2.46292 11.662 2.62688C9.30491 4.60884 6.30323 5.8489 3.01664 5.98735C2.77523 5.99752 2.56937 6.17205 2.5363 6.41141C2.44645 7.06161 2.40002 7.72568 2.40002 8.40058C2.40002 14.6081 6.32785 19.8983 11.8338 21.9213C11.9411 21.9607 12.0594 21.9607 12.1667 21.9213C17.6724 19.8982 21.6 14.6081 21.6 8.40073C21.6 7.72577 21.5536 7.06165 21.4637 6.41141C21.4307 6.17205 21.2248 5.99752 20.9834 5.98735C17.6968 5.84889 14.6951 4.60884 12.338 2.62688ZM12 7.35032C12.4142 7.35032 12.75 7.6861 12.75 8.10032V12.3003C12.75 12.7145 12.4142 13.0503 12 13.0503C11.5858 13.0503 11.25 12.7145 11.25 12.3003V8.10032C11.25 7.6861 11.5858 7.35032 12 7.35032ZM12 18.0003C12.6628 18.0003 13.2 17.4631 13.2 16.8003C13.2 16.1376 12.6628 15.6003 12 15.6003C11.3373 15.6003 10.8 16.1376 10.8 16.8003C10.8 17.4631 11.3373 18.0003 12 18.0003Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default ShieldExclamationIcon;
