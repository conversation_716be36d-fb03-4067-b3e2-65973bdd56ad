import type { SVGProps } from 'react';
import * as React from 'react';

const PresentationChartBarIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/presentation-chart-bar">
      <path
        id="Icon"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.19995 3.30039C1.19995 2.80333 1.60289 2.40039 2.09995 2.40039H21.9C22.397 2.40039 22.8 2.80333 22.8 3.30039C22.8 3.79745 22.397 4.20039 21.9 4.20039H21.6V14.7004C21.6 16.5229 20.1225 18.0004 18.3 18.0004H17.0129L17.971 21.6731C18.0965 22.1541 17.8083 22.6457 17.3274 22.7712C16.8464 22.8966 16.3548 22.6085 16.2293 22.1275L16.0918 21.6003H7.90855L7.77103 22.1275C7.64556 22.6085 7.15395 22.8966 6.67299 22.7712C6.19203 22.6457 5.90385 22.1541 6.02932 21.6731L6.98742 18.0004H5.69995C3.87741 18.0004 2.39995 16.5229 2.39995 14.7004V4.20039H2.09995C1.60289 4.20039 1.19995 3.79745 1.19995 3.30039ZM8.84766 18.0004L8.37812 19.8003H15.6222L15.1527 18.0004H8.84766ZM15.9 6.15039C16.3142 6.15039 16.65 6.48618 16.65 6.90039V13.5004C16.65 13.9146 16.3142 14.2504 15.9 14.2504C15.4857 14.2504 15.15 13.9146 15.15 13.5004V6.90039C15.15 6.48618 15.4857 6.15039 15.9 6.15039ZM8.09995 10.9504C8.51416 10.9504 8.84995 11.2862 8.84995 11.7004V13.5004C8.84995 13.9146 8.51416 14.2504 8.09995 14.2504C7.68574 14.2504 7.34995 13.9146 7.34995 13.5004V11.7004C7.34995 11.2862 7.68574 10.9504 8.09995 10.9504ZM12.75 9.30039C12.75 8.88618 12.4142 8.55039 12 8.55039C11.5857 8.55039 11.25 8.88618 11.25 9.30039V13.5004C11.25 13.9146 11.5857 14.2504 12 14.2504C12.4142 14.2504 12.75 13.9146 12.75 13.5004V9.30039Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default PresentationChartBarIcon;
