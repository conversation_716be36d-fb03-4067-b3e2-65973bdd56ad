import type { SVGProps } from 'react';
import * as React from 'react';

const PhoneIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/phone">
      <path
        id="Icon"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2.40002 4.20039C2.40002 3.20628 3.20591 2.40039 4.20002 2.40039H5.57807C6.42173 2.40039 7.15219 2.98635 7.33521 3.80992L8.19466 7.67747C8.40128 8.60724 7.84783 9.53703 6.93203 9.79869L5.81268 10.1185C5.31974 10.2593 5.04131 10.7844 5.23678 11.2583C6.63604 14.6509 9.34954 17.3644 12.7421 18.7636C13.216 18.9591 13.7411 18.6807 13.8819 18.1877L14.2017 17.0684C14.4634 16.1526 15.3932 15.5991 16.3229 15.8058L20.1905 16.6652C21.0141 16.8482 21.6 17.5787 21.6 18.4223V19.8004C21.6 20.7945 20.7941 21.6004 19.8 21.6004H18C16.6218 21.6004 15.2839 21.4214 14.0091 21.085C8.6 19.6575 4.34292 15.4004 2.91541 9.9913C2.57898 8.71649 2.40002 7.37866 2.40002 6.00039V4.20039Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default PhoneIcon;
