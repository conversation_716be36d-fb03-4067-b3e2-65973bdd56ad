import type { SVGProps } from 'react';
import * as React from 'react';

const CameraPlusIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/camera-plus">
      <g id="Icon">
        <path
          d="M10.8292 7.92728C10.9408 7.46983 11 6.99182 11 6.5C11 5.44817 10.7293 4.45956 10.2539 3.60001H14.3156C15.118 3.60001 15.8674 4.00105 16.3125 4.66873L17.2875 6.13129C17.7327 6.79896 18.482 7.20001 19.2845 7.20001H20.4C21.7255 7.20001 22.8 8.27452 22.8 9.60001V18C22.8 19.3255 21.7255 20.4 20.4 20.4H3.60001C2.27453 20.4 1.20001 19.3255 1.20001 18V11.1435C2.23474 11.9913 3.55796 12.5 5 12.5C5.58366 12.5 6.14785 12.4167 6.68133 12.2612C6.62788 12.5661 6.60001 12.8798 6.60001 13.2C6.60001 16.1823 9.01768 18.6 12 18.6C14.9824 18.6 17.4 16.1823 17.4 13.2C17.4 10.2177 14.9824 7.80001 12 7.80001C11.598 7.80001 11.2062 7.84394 10.8292 7.92728Z"
          fill="currentColor"
        />
        <path
          d="M15.6 13.2C15.6 15.1882 13.9882 16.8 12 16.8C10.0118 16.8 8.40001 15.1882 8.40001 13.2C8.40001 11.2118 10.0118 9.60001 12 9.60001C13.9882 9.60001 15.6 11.2118 15.6 13.2Z"
          fill="currentColor"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M5 11C7.48528 11 9.5 8.98528 9.5 6.5C9.5 4.01472 7.48528 2 5 2C2.51472 2 0.5 4.01472 0.5 6.5C0.5 8.98528 2.51472 11 5 11ZM5.74999 4.25002C5.74999 3.83581 5.41424 3.5 5.00003 3.5C4.58581 3.5 4.25001 3.83581 4.25001 4.25002V5.75001H2.75002C2.33581 5.75001 2 6.08581 2 6.50003C2 6.91424 2.33581 7.24999 2.75002 7.24999H4.25001V8.74998C4.25001 9.16419 4.58581 9.5 5.00003 9.5C5.41424 9.5 5.74999 9.16419 5.74999 8.74998V7.24999H7.24998C7.66419 7.24999 8 6.91424 8 6.50003C8 6.08581 7.66419 5.75001 7.24998 5.75001H5.74999V4.25002Z"
          fill="currentColor"
        />
      </g>
    </g>
  </svg>
);
export default CameraPlusIcon;
