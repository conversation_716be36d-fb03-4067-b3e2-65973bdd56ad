import type { SVGProps } from 'react';
import * as React from 'react';

const ClipboardIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/clipboard">
      <path
        id="Icon"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16.6645 3.81822C17.1396 3.86291 17.6128 3.91436 18.0839 3.97247C19.433 4.13888 20.4 5.29641 20.4 6.61986V20.0999C20.4 21.5911 19.1911 22.7999 17.7 22.7999H6.29998C4.80881 22.7999 3.59998 21.5911 3.59998 20.0999V6.61985C3.59998 5.29641 4.56692 4.13888 5.91604 3.97247C6.38717 3.91436 6.86034 3.86291 7.33546 3.81821C7.76281 2.30715 9.15209 1.19995 10.8 1.19995H13.2C14.8479 1.19995 16.2371 2.30715 16.6645 3.81822ZM8.99997 4.79995C8.99997 3.80584 9.80586 2.99995 10.8 2.99995H13.2C14.1941 2.99995 15 3.80584 15 4.79995V5.39995H8.99997V4.79995Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default ClipboardIcon;
