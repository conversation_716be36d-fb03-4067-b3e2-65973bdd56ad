import type { SVGProps } from 'react';
import * as React from 'react';

const ShapeIssueTrackerIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/shape-issue-tracker">
      <g id="Icon">
        <path
          d="M19.9611 4H4.0389C3.13533 4 2.40002 4.73876 2.40002 5.64656V11.9534C2.40002 12.6532 2.83706 13.252 3.45109 13.4902C3.59695 13.561 3.7592 13.6 3.92746 13.6H20.0376C20.3097 13.6 20.5626 13.5193 20.7757 13.3816C21.2679 13.0973 21.6 12.5638 21.6 11.9534V5.64656C21.6 4.73876 20.8647 4 19.9611 4ZM10.8905 11.9534L15.0795 5.64656H19.051L14.8621 11.9534H10.8905ZM7.16752 5.64656L4.0389 10.3563V5.64656H7.16752ZM4.94848 11.9534L9.13745 5.64656H13.109L8.92003 11.9534H4.94848ZM16.8325 11.9534L19.7639 7.54011V11.9534H16.8325Z"
          fill="currentColor"
        />
        <path
          d="M8.57792 19.1577H7.10071V14.9012H5.3594V19.1577H3.61055C3.12995 19.1577 2.7399 19.5443 2.7399 20.0206C2.7399 20.4969 3.12995 20.8835 3.61055 20.8835H8.57792C9.05852 20.8835 9.44858 20.4969 9.44858 20.0206C9.44858 19.5443 9.05852 19.1577 8.57792 19.1577Z"
          fill="currentColor"
        />
        <path
          d="M20.521 19.1577H18.7721V14.9012H17.0308V19.1577H15.282C14.8014 19.1577 14.4113 19.5443 14.4113 20.0206C14.4113 20.4969 14.8014 20.8835 15.282 20.8835H20.521C21.0016 20.8835 21.3916 20.4969 21.3916 20.0206C21.3916 19.5443 21.0016 19.1577 20.521 19.1577Z"
          fill="currentColor"
        />
      </g>
    </g>
  </svg>
);
export default ShapeIssueTrackerIcon;
