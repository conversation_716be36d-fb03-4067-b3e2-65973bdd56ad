import type { SVGProps } from 'react';
import * as React from 'react';

const XMarkIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/x-mark">
      <path
        id="Icon"
        d="M5.5364 4.2636C5.18492 3.91213 4.61508 3.91213 4.2636 4.2636C3.91213 4.61508 3.91213 5.18492 4.2636 5.5364L10.7272 12L4.2636 18.4636C3.91213 18.8151 3.91213 19.3849 4.2636 19.7364C4.61508 20.0879 5.18492 20.0879 5.5364 19.7364L12 13.2728L18.4636 19.7364C18.8151 20.0879 19.3849 20.0879 19.7364 19.7364C20.0879 19.3849 20.0879 18.8151 19.7364 18.4636L13.2728 12L19.7364 5.5364C20.0879 5.18492 20.0879 4.61508 19.7364 4.2636C19.3849 3.91213 18.8151 3.91213 18.4636 4.2636L12 10.7272L5.5364 4.2636Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default XMarkIcon;
