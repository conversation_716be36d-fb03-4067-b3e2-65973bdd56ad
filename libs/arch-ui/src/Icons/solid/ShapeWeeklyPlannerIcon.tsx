import type { SVGProps } from 'react';
import * as React from 'react';

const <PERSON><PERSON><PERSON><PERSON>eeklyPlannerIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/shape-weekly-planner">
      <g id="Icon">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M15.5847 4.79872H17.0727C18.2784 4.79872 19.2589 5.76391 19.2589 6.95025V9.82075H17.7605V9.1691H15.5842V10.0119C15.5842 10.4188 15.2485 10.7491 14.8349 10.7491C14.4214 10.7491 14.0857 10.4188 14.0857 10.0119V9.1691H9.11628V10.0119C9.11628 10.4188 8.78062 10.7491 8.36704 10.7491C7.95346 10.7491 7.61781 10.4188 7.61781 10.0119V9.1691H5.49797V17.6607C5.49797 18.0342 5.80666 18.3379 6.18627 18.3379H12.6047C12.7671 18.8578 12.9518 19.3532 13.25 19.8122H6.18677C4.981 19.8122 4 18.847 4 17.6607V6.95025C4 5.76391 4.981 4.79872 6.18677 4.79872H7.61831V4.33677C7.61831 3.92986 7.95396 3.59961 8.36754 3.59961C8.78112 3.59961 9.11678 3.92986 9.11678 4.33677V4.79872H14.0862V4.33677C14.0862 3.92986 14.4219 3.59961 14.8354 3.59961C15.249 3.59961 15.5847 3.92986 15.5847 4.33677V4.79872ZM17.761 6.95025C17.761 6.57675 17.4523 6.27304 17.0727 6.27304H15.5847V7.69478H17.761V6.95025ZM14.0862 7.69478V6.27304H9.11678V7.69478H14.0862ZM7.61781 6.27304H6.18627C5.80666 6.27304 5.49797 6.57675 5.49797 6.95025V7.69478H7.61781V6.27304Z"
          fill="currentColor"
        />
        <path
          d="M19.2501 19.6598C18.9665 19.6598 18.6901 19.5114 18.5385 19.246L17.6516 17.698C17.5793 17.5722 17.5416 17.4293 17.5416 17.2842V14.7652C17.5416 14.3084 17.9094 13.9377 18.3627 13.9377C18.816 13.9377 19.1839 14.3084 19.1839 14.7652V17.0625L19.9607 18.4185C20.1873 18.8141 20.0527 19.3205 19.6602 19.5489C19.531 19.6239 19.3897 19.6598 19.2501 19.6598Z"
          fill="currentColor"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M23.738 16.6436C23.7005 15.2656 23.1362 13.978 22.1498 13.0174C21.7273 12.6056 21.2466 12.2741 20.7278 12.0289C20.9629 11.8994 21.1225 11.6491 21.1225 11.3612C21.1225 10.9408 20.782 10.5996 20.3626 10.5996H16.7151C16.2956 10.5996 15.9552 10.9408 15.9552 11.3612C15.9552 11.6328 16.0975 11.871 16.3113 12.0055C15.7075 12.2827 15.1563 12.6762 14.6862 13.1738C13.7231 14.1933 13.214 15.5281 13.252 16.9315C13.2895 18.3095 13.8538 19.5976 14.8402 20.5577C15.8245 21.5163 17.1219 22.0438 18.4932 22.0438C18.5414 22.0438 18.5905 22.0433 18.6391 22.0418C20.0394 22.0032 21.3413 21.4203 22.3044 20.4008C23.2674 19.3813 23.776 18.047 23.738 16.6436ZM18.5971 20.5196C18.5621 20.5206 18.5277 20.5211 18.4932 20.5211C16.4831 20.5211 14.827 18.9162 14.7713 16.8898C14.715 14.8285 16.3361 13.1118 18.3929 13.0555C18.4279 13.0545 18.4623 13.054 18.4968 13.054C20.507 13.054 22.163 14.6589 22.2187 16.6852C22.275 18.7466 20.6539 20.4632 18.5971 20.5196Z"
          fill="currentColor"
        />
      </g>
    </g>
  </svg>
);
export default ShapeWeeklyPlannerIcon;
