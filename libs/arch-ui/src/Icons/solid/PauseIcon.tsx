import type { SVGProps } from 'react';
import * as React from 'react';

const PauseIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/pause">
      <g id="Icon">
        <path
          d="M6.9 3.59961C6.40294 3.59961 6 4.00255 6 4.49961V19.4996C6 19.9967 6.40294 20.3996 6.9 20.3996H8.7C9.19706 20.3996 9.6 19.9967 9.6 19.4996V4.49961C9.6 4.00255 9.19706 3.59961 8.7 3.59961H6.9Z"
          fill="currentColor"
        />
        <path
          d="M15.3 3.59961C14.8029 3.59961 14.4 4.00255 14.4 4.49961V19.4996C14.4 19.9967 14.8029 20.3996 15.3 20.3996H17.1C17.5971 20.3996 18 19.9967 18 19.4996V4.49961C18 4.00255 17.5971 3.59961 17.1 3.59961H15.3Z"
          fill="currentColor"
        />
      </g>
    </g>
  </svg>
);
export default PauseIcon;
