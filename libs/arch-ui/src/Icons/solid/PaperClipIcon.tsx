import type { SVGProps } from 'react';
import * as React from 'react';

const PaperClipIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/paper-clip">
      <path
        id="Icon"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M18.7456 5.25481C17.3397 3.84892 15.0603 3.84892 13.6544 5.25481L5.25444 13.6548C3.84855 15.0607 3.84855 17.3401 5.25444 18.746C6.65997 20.1515 8.93856 20.1519 10.3445 18.7471L10.3456 18.746L10.9415 18.1461C11.2918 17.7935 11.8617 17.7916 12.2143 18.1419C12.5669 18.4922 12.5688 19.0621 12.2185 19.4147L11.6205 20.0167L11.6184 20.0188C9.50957 22.1276 6.09048 22.1276 3.98165 20.0188C1.87282 17.9099 1.87282 14.4908 3.98165 12.382L12.3816 3.98201C14.4905 1.87318 17.9096 1.87318 20.0184 3.98201C22.1257 6.08928 22.1272 9.50485 20.0231 11.6141L15.8775 15.8779C14.6473 17.108 12.6528 17.108 11.4226 15.8778C10.1925 14.6477 10.1925 12.6532 11.4226 11.423L15.5636 7.28204C15.9151 6.93057 16.485 6.93057 16.8364 7.28204C17.1879 7.63352 17.1879 8.20336 16.8364 8.55484L12.6954 12.6958C12.1682 13.223 12.1682 14.0778 12.6954 14.605C13.2213 15.1309 14.0732 15.1322 14.6007 14.6089L18.7455 10.3459C20.1514 8.94003 20.1515 6.66069 18.7456 5.25481Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default PaperClipIcon;
