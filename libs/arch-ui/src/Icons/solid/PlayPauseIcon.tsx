import type { SVGProps } from 'react';
import * as React from 'react';

const PlayPauseIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/play-pause">
      <g id="Icon">
        <path
          d="M15.3 4.7998C14.8029 4.7998 14.4 5.20275 14.4 5.6998V18.2998C14.4 18.7969 14.8029 19.1998 15.3 19.1998H15.9C16.397 19.1998 16.8 18.7969 16.8 18.2998V5.6998C16.8 5.20275 16.397 4.7998 15.9 4.7998H15.3Z"
          fill="currentColor"
        />
        <path
          d="M21.3 4.7998C20.8029 4.7998 20.4 5.20275 20.4 5.6998V18.2998C20.4 18.7969 20.8029 19.1998 21.3 19.1998H21.9C22.397 19.1998 22.8 18.7969 22.8 18.2998V5.6998C22.8 5.20275 22.397 4.7998 21.9 4.7998H21.3Z"
          fill="currentColor"
        />
        <path
          d="M3.94584 5.78211C2.74664 5.04142 1.19995 5.90404 1.19995 7.31354V16.6861C1.19995 18.0956 2.74664 18.9582 3.94584 18.2176L11.5332 13.5313C12.672 12.8279 12.672 11.1718 11.5332 10.4684L3.94584 5.78211Z"
          fill="currentColor"
        />
      </g>
    </g>
  </svg>
);
export default PlayPauseIcon;
