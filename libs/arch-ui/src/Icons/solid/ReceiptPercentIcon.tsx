import type { SVGProps } from 'react';
import * as React from 'react';

const ReceiptPercentIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/receipt-percent">
      <path
        id="Icon"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.91604 2.77294C7.9099 2.527 9.94035 2.40039 12 2.40039C14.0596 2.40039 16.0901 2.527 18.0839 2.77294C19.433 2.93935 20.4 4.09688 20.4 5.42032V20.7004C20.4 21.0097 20.2411 21.2974 19.9793 21.4621C19.7175 21.6269 19.3894 21.6456 19.1105 21.5118L15.75 19.8987L12.3894 21.5118C12.1432 21.6299 11.8567 21.6299 11.6105 21.5118L8.24997 19.8987L4.88943 21.5118C4.61056 21.6456 4.28248 21.6269 4.02066 21.4621C3.75884 21.2974 3.59998 21.0097 3.59998 20.7004V5.42032C3.59998 4.09687 4.56692 2.93935 5.91604 2.77294ZM16.5364 8.73679C16.8878 8.38531 16.8878 7.81547 16.5364 7.46399C16.1849 7.11252 15.6151 7.11252 15.2636 7.46399L7.46358 15.264C7.11211 15.6155 7.11211 16.1853 7.46358 16.5368C7.81505 16.8883 8.3849 16.8883 8.73637 16.5368L16.5364 8.73679ZM10.8 9.60039C10.8 10.2631 10.2627 10.8004 9.59997 10.8004C8.93723 10.8004 8.39997 10.2631 8.39997 9.60039C8.39997 8.93765 8.93723 8.40039 9.59997 8.40039C10.2627 8.40039 10.8 8.93765 10.8 9.60039ZM14.4 15.6004C15.0627 15.6004 15.6 15.0631 15.6 14.4004C15.6 13.7376 15.0627 13.2004 14.4 13.2004C13.7372 13.2004 13.2 13.7376 13.2 14.4004C13.2 15.0631 13.7372 15.6004 14.4 15.6004Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default ReceiptPercentIcon;
