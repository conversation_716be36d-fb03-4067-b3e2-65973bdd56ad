import type { SVGProps } from 'react';
import * as React from 'react';

const PauseCircleIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/pause-circle">
      <path
        id="Icon"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2.40002 12.0004C2.40002 6.69846 6.69809 2.40039 12 2.40039C17.302 2.40039 21.6 6.69846 21.6 12.0004C21.6 17.3023 17.302 21.6004 12 21.6004C6.69809 21.6004 2.40002 17.3023 2.40002 12.0004ZM8.40003 9.30039C8.40003 8.80334 8.80297 8.40039 9.30003 8.40039H9.90003C10.3971 8.40039 10.8 8.80334 10.8 9.30039V14.7004C10.8 15.1974 10.3971 15.6004 9.90003 15.6004H9.30003C8.80297 15.6004 8.40003 15.1974 8.40003 14.7004V9.30039ZM13.2 9.30039C13.2 8.80334 13.603 8.40039 14.1 8.40039H14.7C15.1971 8.40039 15.6 8.80334 15.6 9.30039V14.7004C15.6 15.1974 15.1971 15.6004 14.7 15.6004H14.1C13.603 15.6004 13.2 15.1974 13.2 14.7004V9.30039Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default PauseCircleIcon;
