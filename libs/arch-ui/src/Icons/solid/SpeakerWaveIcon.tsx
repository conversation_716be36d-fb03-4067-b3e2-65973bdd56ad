import type { SVGProps } from 'react';
import * as React from 'react';

const SpeakerWaveIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/speaker-wave">
      <g id="Icon">
        <path
          d="M12 4.49963C12 4.14057 11.7866 3.81587 11.457 3.67345C11.1274 3.53104 10.7447 3.59816 10.4832 3.84425L5.6431 8.39963H3.80072C3.42872 8.39963 3.09506 8.62849 2.9611 8.97553C2.59843 9.91505 2.40002 10.9352 2.40002 11.9996C2.40002 13.064 2.59843 14.0842 2.9611 15.0237C3.09506 15.3708 3.42872 15.5996 3.80072 15.5996H5.6431L10.4832 20.155C10.7447 20.4011 11.1274 20.4682 11.457 20.3258C11.7866 20.1834 12 19.8587 12 19.4996V4.49963Z"
          fill="currentColor"
        />
        <path
          d="M19.1397 6.05996C18.7883 5.70848 18.2184 5.70848 17.8669 6.05996C17.5155 6.41143 17.5155 6.98128 17.8669 7.33275C20.4444 9.91021 20.4444 14.0891 17.8669 16.6666C17.5155 17.018 17.5155 17.5879 17.8669 17.9394C18.2184 18.2908 18.7883 18.2908 19.1397 17.9394C22.4201 14.6589 22.4201 9.34036 19.1397 6.05996Z"
          fill="currentColor"
        />
        <path
          d="M16.5944 8.60553C16.243 8.25405 15.6731 8.25405 15.3216 8.60553C14.9702 8.957 14.9702 9.52684 15.3216 9.87832C16.4932 11.0499 16.4932 12.9494 15.3216 14.121C14.9702 14.4724 14.9702 15.0423 15.3216 15.3938C15.6731 15.7452 16.243 15.7452 16.5944 15.3938C18.4689 13.5192 18.4689 10.48 16.5944 8.60553Z"
          fill="currentColor"
        />
      </g>
    </g>
  </svg>
);
export default SpeakerWaveIcon;
