import type { SVGProps } from 'react';
import * as React from 'react';

const ServerStackIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/server-stack">
      <g id="Icon">
        <path
          d="M5.35638 3.79465C5.74867 2.94469 6.59937 2.40039 7.53549 2.40039H16.4643C17.4004 2.40039 18.2511 2.94469 18.6434 3.79465L20.028 6.79465C20.1081 6.96822 20.1656 7.14447 20.2023 7.32076C19.8811 7.2421 19.5454 7.20039 19.1999 7.20039H4.79989C4.45443 7.20039 4.1187 7.2421 3.79749 7.32076C3.83413 7.14447 3.89166 6.96822 3.97177 6.79465L5.35638 3.79465Z"
          fill="currentColor"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M2.40002 11.4004C2.40002 10.0749 3.47454 9.00039 4.80002 9.00039H19.2C20.5255 9.00039 21.6 10.0749 21.6 11.4004C21.6 12.7259 20.5255 13.8004 19.2 13.8004H4.80002C3.47454 13.8004 2.40002 12.7259 2.40002 11.4004ZM18.2883 11.4004C18.2883 10.9033 18.6913 10.5004 19.1883 10.5004H19.2003C19.6974 10.5004 20.1003 10.9033 20.1003 11.4004V11.4124C20.1003 11.9094 19.6974 12.3124 19.2003 12.3124H19.1883C18.6913 12.3124 18.2883 11.9094 18.2883 11.4124V11.4004ZM15.5883 10.5004C15.0913 10.5004 14.6883 10.9033 14.6883 11.4004V11.4124C14.6883 11.9094 15.0913 12.3124 15.5883 12.3124H15.6003C16.0974 12.3124 16.5003 11.9094 16.5003 11.4124V11.4004C16.5003 10.9033 16.0974 10.5004 15.6003 10.5004H15.5883Z"
          fill="currentColor"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M2.40002 18.0004C2.40002 16.6749 3.47454 15.6004 4.80002 15.6004H19.2C20.5255 15.6004 21.6 16.6749 21.6 18.0004C21.6 19.3259 20.5255 20.4004 19.2 20.4004H4.80002C3.47454 20.4004 2.40002 19.3259 2.40002 18.0004ZM18.2883 18.0004C18.2883 17.5033 18.6913 17.1004 19.1883 17.1004H19.2003C19.6974 17.1004 20.1003 17.5033 20.1003 18.0004V18.0124C20.1003 18.5094 19.6974 18.9124 19.2003 18.9124H19.1883C18.6913 18.9124 18.2883 18.5094 18.2883 18.0124V18.0004ZM15.5883 17.1004C15.0913 17.1004 14.6883 17.5033 14.6883 18.0004V18.0124C14.6883 18.5094 15.0913 18.9124 15.5883 18.9124H15.6003C16.0974 18.9124 16.5003 18.5094 16.5003 18.0124V18.0004C16.5003 17.5033 16.0974 17.1004 15.6003 17.1004H15.5883Z"
          fill="currentColor"
        />
      </g>
    </g>
  </svg>
);
export default ServerStackIcon;
