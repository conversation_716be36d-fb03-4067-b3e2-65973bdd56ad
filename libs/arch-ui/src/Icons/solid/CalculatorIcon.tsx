import type { SVGProps } from 'react';
import * as React from 'react';

const CalculatorIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/calculator">
      <path
        id="Icon"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12 1.2002C9.94035 1.2002 7.9099 1.3268 5.91604 1.57274C4.56692 1.73916 3.59998 2.89668 3.59998 4.22013V20.1002C3.59998 21.5914 4.80881 22.8002 6.29998 22.8002H17.7C19.1911 22.8002 20.4 21.5914 20.4 20.1002V4.22013C20.4 2.89668 19.433 1.73916 18.0839 1.57274C16.0901 1.3268 14.0596 1.2002 12 1.2002ZM7.18826 10.5002C7.18826 10.0031 7.5912 9.6002 8.08826 9.6002H8.10026C8.59731 9.6002 9.00026 10.0031 9.00026 10.5002V10.5122C9.00026 11.0093 8.59731 11.4122 8.10026 11.4122H8.08826C7.5912 11.4122 7.18826 11.0093 7.18826 10.5122V10.5002ZM8.08826 12.2001C7.5912 12.2001 7.18826 12.6031 7.18826 13.1001V13.1121C7.18826 13.6092 7.5912 14.0121 8.08826 14.0121H8.10026C8.59731 14.0121 9.00026 13.6092 9.00026 13.1121V13.1001C9.00026 12.6031 8.59731 12.2001 8.10026 12.2001H8.08826ZM7.18826 15.7001C7.18826 15.203 7.5912 14.8001 8.08826 14.8001H8.10026C8.59731 14.8001 9.00026 15.203 9.00026 15.7001V15.7121C9.00026 16.2092 8.59731 16.6121 8.10026 16.6121H8.08826C7.5912 16.6121 7.18826 16.2092 7.18826 15.7121V15.7001ZM8.08826 17.4C7.5912 17.4 7.18826 17.803 7.18826 18.3V18.312C7.18826 18.8091 7.5912 19.212 8.08826 19.212H8.10026C8.59731 19.212 9.00026 18.8091 9.00026 18.312V18.3C9.00026 17.803 8.59731 17.4 8.10026 17.4H8.08826ZM9.78826 10.5002C9.78826 10.0031 10.1912 9.6002 10.6883 9.6002H10.7003C11.1973 9.6002 11.6003 10.0031 11.6003 10.5002V10.5122C11.6003 11.0093 11.1973 11.4122 10.7003 11.4122H10.6883C10.1912 11.4122 9.78826 11.0093 9.78826 10.5122V10.5002ZM10.6883 12.2001C10.1912 12.2001 9.78826 12.6031 9.78826 13.1001V13.1121C9.78826 13.6092 10.1912 14.0121 10.6883 14.0121H10.7003C11.1973 14.0121 11.6003 13.6092 11.6003 13.1121V13.1001C11.6003 12.6031 11.1973 12.2001 10.7003 12.2001H10.6883ZM9.78826 15.7001C9.78826 15.203 10.1912 14.8001 10.6883 14.8001H10.7003C11.1973 14.8001 11.6003 15.203 11.6003 15.7001V15.7121C11.6003 16.2092 11.1973 16.6121 10.7003 16.6121H10.6883C10.1912 16.6121 9.78826 16.2092 9.78826 15.7121V15.7001ZM10.6883 17.4C10.1912 17.4 9.78826 17.803 9.78826 18.3V18.312C9.78826 18.8091 10.1912 19.212 10.6883 19.212H10.7003C11.1973 19.212 11.6003 18.8091 11.6003 18.312V18.3C11.6003 17.803 11.1973 17.4 10.7003 17.4H10.6883ZM12.3923 10.5002C12.3923 10.0031 12.7952 9.6002 13.2923 9.6002H13.3043C13.8013 9.6002 14.2043 10.0031 14.2043 10.5002V10.5122C14.2043 11.0093 13.8013 11.4122 13.3043 11.4122H13.2923C12.7952 11.4122 12.3923 11.0093 12.3923 10.5122V10.5002ZM13.2923 12.2001C12.7952 12.2001 12.3923 12.6031 12.3923 13.1001V13.1121C12.3923 13.6092 12.7952 14.0121 13.2923 14.0121H13.3043C13.8013 14.0121 14.2043 13.6092 14.2043 13.1121V13.1001C14.2043 12.6031 13.8013 12.2001 13.3043 12.2001H13.2923ZM12.3923 15.7001C12.3923 15.203 12.7952 14.8001 13.2923 14.8001H13.3043C13.8013 14.8001 14.2043 15.203 14.2043 15.7001V15.7121C14.2043 16.2092 13.8013 16.6121 13.3043 16.6121H13.2923C12.7952 16.6121 12.3923 16.2092 12.3923 15.7121V15.7001ZM13.2923 17.4C12.7952 17.4 12.3923 17.803 12.3923 18.3V18.312C12.3923 18.8091 12.7952 19.212 13.2923 19.212H13.3043C13.8013 19.212 14.2043 18.8091 14.2043 18.312V18.3C14.2043 17.803 13.8013 17.4 13.3043 17.4H13.2923ZM15.0003 10.5002C15.0003 10.0031 15.4032 9.6002 15.9003 9.6002H15.9123C16.4093 9.6002 16.8123 10.0031 16.8123 10.5002V10.5122C16.8123 11.0093 16.4093 11.4122 15.9123 11.4122H15.9003C15.4032 11.4122 15.0003 11.0093 15.0003 10.5122V10.5002ZM15.9003 12.2001C15.4032 12.2001 15.0003 12.6031 15.0003 13.1001V13.1121C15.0003 13.6092 15.4032 14.0121 15.9003 14.0121H15.9123C16.4093 14.0121 16.8123 13.6092 16.8123 13.1121V13.1001C16.8123 12.6031 16.4093 12.2001 15.9123 12.2001H15.9003ZM15.9003 14.8002C16.3973 14.8002 16.8003 15.2032 16.8003 15.7002V18.3002C16.8003 18.7973 16.3973 19.2002 15.9003 19.2002C15.4032 19.2002 15.0003 18.7973 15.0003 18.3002V15.7002C15.0003 15.2032 15.4032 14.8002 15.9003 14.8002ZM8.09997 4.8002C7.60292 4.8002 7.19998 5.20314 7.19998 5.7002V6.3002C7.19998 6.79725 7.60292 7.2002 8.09997 7.2002H15.9C16.397 7.2002 16.8 6.79725 16.8 6.3002V5.7002C16.8 5.20314 16.397 4.8002 15.9 4.8002H8.09997Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default CalculatorIcon;
