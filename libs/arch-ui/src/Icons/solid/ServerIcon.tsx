import type { SVGProps } from 'react';
import * as React from 'react';

const ServerIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/server">
      <g id="Icon">
        <path
          d="M5.55838 4.2403C5.81736 3.16121 6.78238 2.40039 7.89211 2.40039H16.1078C17.2175 2.40039 18.1826 3.16121 18.4415 4.2403L20.813 14.1213C20.3163 13.9145 19.7715 13.8004 19.2 13.8004H4.79996C4.22844 13.8004 3.68358 13.9145 3.18694 14.1213L5.55838 4.2403Z"
          fill="currentColor"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M4.80002 15.6004C3.47454 15.6004 2.40002 16.6749 2.40002 18.0004C2.40002 19.3259 3.47454 20.4004 4.80002 20.4004H19.2C20.5255 20.4004 21.6 19.3259 21.6 18.0004C21.6 16.6749 20.5255 15.6004 19.2 15.6004H4.80002ZM18.2883 18.0004C18.2883 17.5033 18.6913 17.1004 19.1883 17.1004H19.2003C19.6974 17.1004 20.1003 17.5033 20.1003 18.0004V18.0124C20.1003 18.5094 19.6974 18.9124 19.2003 18.9124H19.1883C18.6913 18.9124 18.2883 18.5094 18.2883 18.0124V18.0004ZM15.5883 17.1004C15.0913 17.1004 14.6883 17.5033 14.6883 18.0004V18.0124C14.6883 18.5094 15.0913 18.9124 15.5883 18.9124H15.6003C16.0974 18.9124 16.5003 18.5094 16.5003 18.0124V18.0004C16.5003 17.5033 16.0974 17.1004 15.6003 17.1004H15.5883Z"
          fill="currentColor"
        />
      </g>
    </g>
  </svg>
);
export default ServerIcon;
