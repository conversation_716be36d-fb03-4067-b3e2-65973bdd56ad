import type { SVGProps } from 'react';
import * as React from 'react';

const PlusIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/plus">
      <path
        id="Icon"
        d="M12.9 4.6998C12.9 4.20275 12.4971 3.7998 12 3.7998C11.503 3.7998 11.1 4.20275 11.1 4.6998V11.0998H4.70005C4.20299 11.0998 3.80005 11.5027 3.80005 11.9998C3.80005 12.4969 4.20299 12.8998 4.70005 12.8998L11.1 12.8998V19.2998C11.1 19.7969 11.503 20.1998 12 20.1998C12.4971 20.1998 12.9 19.7969 12.9 19.2998V12.8998H19.3C19.7971 12.8998 20.2 12.4969 20.2 11.9998C20.2 11.5027 19.7971 11.0998 19.3 11.0998H12.9V4.6998Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default PlusIcon;
