import type { SVGProps } from 'react';
import * as React from 'react';

const ChartBarSquareIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/chart-bar-square">
      <path
        id="Icon"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.10002 2.3999C3.60886 2.3999 2.40002 3.60873 2.40002 5.0999V18.8999C2.40002 20.3911 3.60886 21.5999 5.10002 21.5999H18.9C20.3912 21.5999 21.6 20.3911 21.6 18.8999V5.0999C21.6 3.60873 20.3912 2.3999 18.9 2.3999H5.10002ZM17.85 6.8999C17.85 6.48569 17.5142 6.1499 17.1 6.1499C16.6858 6.1499 16.35 6.48569 16.35 6.8999V17.0999C16.35 17.5141 16.6858 17.8499 17.1 17.8499C17.5142 17.8499 17.85 17.5141 17.85 17.0999V6.8999ZM7.65002 14.0999C7.65002 13.6857 7.31424 13.3499 6.90002 13.3499C6.48581 13.3499 6.15002 13.6857 6.15002 14.0999V17.0999C6.15002 17.5141 6.48581 17.8499 6.90002 17.8499C7.31424 17.8499 7.65002 17.5141 7.65002 17.0999V14.0999ZM10.3002 10.9499C10.7144 10.9499 11.0502 11.2857 11.0502 11.6999V17.0999C11.0502 17.5141 10.7144 17.8499 10.3002 17.8499C9.88601 17.8499 9.55022 17.5141 9.55022 17.0999V11.6999C9.55022 11.2857 9.88601 10.9499 10.3002 10.9499ZM14.4463 9.2999C14.4463 8.88569 14.1105 8.5499 13.6963 8.5499C13.2821 8.5499 12.9463 8.88569 12.9463 9.2999V17.0999C12.9463 17.5141 13.2821 17.8499 13.6963 17.8499C14.1105 17.8499 14.4463 17.5141 14.4463 17.0999V9.2999Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default ChartBarSquareIcon;
