import type { SVGProps } from 'react';
import * as React from 'react';

const CloudArrowDownIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/cloud-arrow-down">
      <path
        id="Icon"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.59995 20.4001C3.61761 20.4001 1.19995 17.9824 1.19995 15.0001C1.19995 12.6222 2.73696 10.6032 4.8717 9.88258C4.8245 9.59541 4.79995 9.30062 4.79995 9.0001C4.79995 6.01776 7.21761 3.6001 10.2 3.6001C12.1684 3.6001 13.8909 4.65335 14.8345 6.22705C15.2626 6.07995 15.7219 6.0001 16.2 6.0001C18.5195 6.0001 20.4 7.8805 20.4 10.2001C20.4 10.6001 20.344 10.987 20.2396 11.3535C21.7623 12.1583 22.8 13.758 22.8 15.6001C22.8 18.2511 20.6509 20.4001 18 20.4001H6.59995ZM12.9 9.3001C12.9 8.80304 12.497 8.4001 12 8.4001C11.5029 8.4001 11.1 8.80304 11.1 9.3001V14.8082L8.75947 12.2877C8.42124 11.9235 7.85179 11.9024 7.48755 12.2406C7.12331 12.5788 7.10221 13.1483 7.44044 13.5125L11.3404 17.7125C11.5107 17.8959 11.7497 18.0001 12 18.0001C12.2502 18.0001 12.4892 17.8959 12.6595 17.7125L16.5595 13.5125C16.8977 13.1483 16.8766 12.5788 16.5124 12.2406C16.1481 11.9024 15.5787 11.9235 15.2404 12.2877L12.9 14.8082V9.3001Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default CloudArrowDownIcon;
