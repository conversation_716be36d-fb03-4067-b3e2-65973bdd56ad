import type { SVGProps } from 'react';
import * as React from 'react';

const CloudIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/cloud">
      <path
        id="Icon"
        d="M1.19995 15.0001C1.19995 17.9824 3.61761 20.4001 6.59995 20.4001H18C20.6509 20.4001 22.8 18.2511 22.8 15.6001C22.8 13.758 21.7623 12.1583 20.2396 11.3535C20.344 10.987 20.4 10.6001 20.4 10.2001C20.4 7.8805 18.5195 6.0001 16.2 6.0001C15.7219 6.0001 15.2626 6.07995 14.8345 6.22705C13.8909 4.65335 12.1684 3.6001 10.2 3.6001C7.21761 3.6001 4.79995 6.01776 4.79995 9.0001C4.79995 9.30062 4.8245 9.59541 4.8717 9.88258C2.73696 10.6032 1.19995 12.6222 1.19995 15.0001Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default CloudIcon;
