import type { SVGProps } from 'react';
import * as React from 'react';

const PowerIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/power">
      <path
        id="Icon"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12 2.40039C12.4971 2.40039 12.9 2.80333 12.9 3.30039V12.3004C12.9 12.7974 12.4971 13.2004 12 13.2004C11.503 13.2004 11.1 12.7974 11.1 12.3004V3.30039C11.1 2.80333 11.503 2.40039 12 2.40039ZM6.48459 5.21217C6.83606 5.56364 6.83606 6.13349 6.48459 6.48496C3.4385 9.53105 3.4385 14.4697 6.48459 17.5158C9.53068 20.5619 14.4694 20.5619 17.5155 17.5158C20.5615 14.4697 20.5615 9.53105 17.5155 6.48496C17.164 6.13349 17.164 5.56364 17.5155 5.21217C17.8669 4.86069 18.4368 4.86069 18.7883 5.21217C22.5373 8.9612 22.5373 15.0396 18.7883 18.7886C15.0392 22.5377 8.96083 22.5377 5.2118 18.7886C1.46277 15.0396 1.46277 8.9612 5.2118 5.21217C5.56327 4.86069 6.13312 4.86069 6.48459 5.21217Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default PowerIcon;
