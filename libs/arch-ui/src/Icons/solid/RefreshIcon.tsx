import type { SVGProps } from 'react';
import * as React from 'react';

const RefreshIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/refresh">
      <path
        id="Icon"
        d="M5.375 12C5.375 8.34111 8.34111 5.375 12 5.375C13.8333 5.375 15.4913 6.11852 16.6917 7.32258C17.0588 7.69076 17.3826 8.10164 17.6552 8.54694L14.8392 7.52199C14.3851 7.35671 13.883 7.59085 13.7177 8.04495C13.5524 8.49906 13.7865 9.00117 14.2406 9.16645L18.9391 10.8766C19.3932 11.0418 19.8953 10.8077 20.0606 10.3536L21.7707 5.65513C21.936 5.20102 21.7018 4.69891 21.2477 4.53363C20.7936 4.36835 20.2915 4.60249 20.1262 5.05659L19.1732 7.6751C18.8234 7.09602 18.4056 6.563 17.9311 6.08702C16.4157 4.5671 14.3171 3.625 12 3.625C7.37462 3.625 3.625 7.37462 3.625 12C3.625 16.6254 7.37462 20.375 12 20.375C15.3797 20.375 18.2899 18.3732 19.6132 15.4943C19.725 15.251 19.8256 15.0014 19.9141 14.7462C20.0725 14.2896 19.8308 13.7911 19.3743 13.6327C18.9177 13.4743 18.4192 13.716 18.2608 14.1726C18.1909 14.3741 18.1115 14.5712 18.0231 14.7634C16.9748 17.0442 14.6711 18.625 12 18.625C8.34111 18.625 5.375 15.6589 5.375 12Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default RefreshIcon;
