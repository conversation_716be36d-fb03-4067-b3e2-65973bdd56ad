import type { SVGProps } from 'react';
import * as React from 'react';

const SignalIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/signal">
      <g id="Icon">
        <path
          d="M19.6367 4.36321C19.2852 4.01174 18.7154 4.01174 18.3639 4.36321C18.0124 4.71469 18.0124 5.28453 18.3639 5.63601C21.8786 9.15072 21.8786 14.8492 18.3639 18.3639C18.0124 18.7154 18.0124 19.2852 18.3639 19.6367C18.7154 19.9882 19.2852 19.9882 19.6367 19.6367C23.8544 15.4191 23.8544 8.58088 19.6367 4.36321Z"
          fill="currentColor"
        />
        <path
          d="M5.63599 5.63601C5.98746 5.28453 5.98746 4.71469 5.63599 4.36321C5.28452 4.01174 4.71467 4.01174 4.3632 4.36321C0.145535 8.58088 0.145535 15.4191 4.3632 19.6367C4.71467 19.9882 5.28452 19.9882 5.63599 19.6367C5.98746 19.2852 5.98746 18.7154 5.63599 18.3639C2.12127 14.8492 2.12127 9.15072 5.63599 5.63601Z"
          fill="currentColor"
        />
        <path
          d="M14.9698 7.75733C15.3213 7.40586 15.8911 7.40586 16.2426 7.75733C18.5857 10.1005 18.5857 13.8995 16.2426 16.2426C15.8911 16.5941 15.3213 16.5941 14.9698 16.2426C14.6183 15.8911 14.6183 15.3213 14.9698 14.9698C16.61 13.3296 16.61 10.6703 14.9698 9.03013C14.6183 8.67865 14.6183 8.10881 14.9698 7.75733Z"
          fill="currentColor"
        />
        <path
          d="M9.0301 7.75733C9.38157 8.10881 9.38157 8.67865 9.0301 9.03013C7.3899 10.6703 7.3899 13.3296 9.0301 14.9698C9.38157 15.3213 9.38157 15.8911 9.0301 16.2426C8.67863 16.5941 8.10878 16.5941 7.75731 16.2426C5.41417 13.8995 5.41417 10.1005 7.75731 7.75733C8.10878 7.40586 8.67863 7.40586 9.0301 7.75733Z"
          fill="currentColor"
        />
        <path
          d="M13.2 11.9999C13.2 12.6627 12.6627 13.1999 12 13.1999C11.3372 13.1999 10.8 12.6627 10.8 11.9999C10.8 11.3372 11.3372 10.7999 12 10.7999C12.6627 10.7999 13.2 11.3372 13.2 11.9999Z"
          fill="currentColor"
        />
      </g>
    </g>
  </svg>
);
export default SignalIcon;
