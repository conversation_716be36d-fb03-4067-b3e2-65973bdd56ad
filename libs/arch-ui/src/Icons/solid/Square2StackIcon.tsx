import type { SVGProps } from 'react';
import * as React from 'react';

const Square2StackIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/square-2-stack">
      <g id="Icon">
        <path
          d="M2.40002 5.10039C2.40002 3.60922 3.60886 2.40039 5.10002 2.40039H12.9C14.3912 2.40039 15.6 3.60922 15.6 5.10039V6.60039H11.1C8.61474 6.60039 6.60003 8.61511 6.60003 11.1004V15.6004H5.10002C3.60886 15.6004 2.40002 14.3916 2.40002 12.9004V5.10039Z"
          fill="currentColor"
        />
        <path
          d="M11.1 8.40039C9.60886 8.40039 8.40003 9.60922 8.40003 11.1004V18.9004C8.40003 20.3916 9.60886 21.6004 11.1 21.6004H18.9C20.3912 21.6004 21.6 20.3916 21.6 18.9004V11.1004C21.6 9.60922 20.3912 8.40039 18.9 8.40039H11.1Z"
          fill="currentColor"
        />
      </g>
    </g>
  </svg>
);
export default Square2StackIcon;
