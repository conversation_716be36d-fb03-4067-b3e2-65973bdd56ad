import type { SVGProps } from 'react';
import * as React from 'react';

const PencilSquareIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/pencil-square">
      <g id="Icon">
        <path
          d="M5.70002 5.40002C4.8716 5.40002 4.20002 6.07159 4.20002 6.90002V18.3C4.20002 19.1285 4.8716 19.8 5.70002 19.8H17.1C17.9285 19.8 18.6 19.1285 18.6 18.3V12C18.6 11.503 19.003 11.1 19.5 11.1C19.9971 11.1 20.4 11.503 20.4 12V18.3C20.4 20.1226 18.9226 21.6 17.1 21.6H5.70002C3.87749 21.6 2.40002 20.1226 2.40002 18.3V6.90002C2.40002 5.07748 3.87748 3.60002 5.70002 3.60002H12C12.4971 3.60002 12.9 4.00297 12.9 4.50002C12.9 4.99708 12.4971 5.40002 12 5.40002H5.70002Z"
          fill="currentColor"
        />
        <path
          d="M7.75229 12.3586L15.8279 4.28307L19.717 8.17215L11.6414 16.2477C11.559 16.3301 11.4587 16.3921 11.3482 16.4289L7.10558 17.8431C6.83608 17.933 6.53895 17.8628 6.33808 17.6619C6.13721 17.4611 6.06706 17.1639 6.1569 16.8944L7.57111 12.6518C7.60793 12.5413 7.66996 12.441 7.75229 12.3586Z"
          fill="currentColor"
        />
        <path
          d="M22.3693 3.04494C23.0527 3.72836 23.0527 4.8364 22.3693 5.51981L20.7776 7.1115L16.8885 3.22241L18.4802 1.63073C19.1636 0.94731 20.2717 0.947309 20.9551 1.63073L22.3693 3.04494Z"
          fill="currentColor"
        />
      </g>
    </g>
  </svg>
);
export default PencilSquareIcon;
