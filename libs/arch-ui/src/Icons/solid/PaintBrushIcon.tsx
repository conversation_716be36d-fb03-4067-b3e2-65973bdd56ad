import type { SVGProps } from 'react';
import * as React from 'react';

const PaintBrushIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/paint-brush">
      <g id="Icon">
        <path
          d="M19.1918 1.66181C19.5834 1.36241 20.0625 1.2002 20.5554 1.2002C21.7952 1.2002 22.8002 2.20521 22.8002 3.44495C22.8002 3.93783 22.638 4.417 22.3386 4.80853L17.5027 11.1323C16.1356 12.9201 14.3973 14.3761 12.4187 15.407C11.9228 13.5444 10.456 12.0776 8.59336 11.5816C9.62427 9.60303 11.0803 7.86475 12.8681 6.49763L19.1918 1.66181Z"
          fill="currentColor"
        />
        <path
          d="M7.19973 13.2002C5.2115 13.2002 3.59973 14.812 3.59973 16.8002C3.59972 17.1316 3.33109 17.4002 2.99972 17.4002C2.90317 17.4002 2.81436 17.378 2.73576 17.3394C2.40554 17.177 2.0097 17.2319 1.73624 17.4782C1.46278 17.7245 1.3668 18.1124 1.49388 18.4578C2.1683 20.2906 3.92994 21.6002 5.99972 21.6002C8.6489 21.6002 10.7968 19.4538 10.7997 16.8053L10.7997 16.8002C10.7997 16.2241 10.6638 15.6773 10.4214 15.1923C10.0728 14.4948 9.50507 13.9272 8.80763 13.5786C8.32265 13.3362 7.77577 13.2002 7.19973 13.2002Z"
          fill="currentColor"
        />
      </g>
    </g>
  </svg>
);
export default PaintBrushIcon;
