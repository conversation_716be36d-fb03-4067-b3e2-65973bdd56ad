import type { SVGProps } from 'react';
import * as React from 'react';

const BookOpenIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/book-open">
      <g id="Icon">
        <path
          d="M12.9 20.183C14.3495 19.1841 16.1054 18.5996 18 18.5996C18.8524 18.5996 19.6757 18.7179 20.4551 18.9383C20.7264 19.015 21.0181 18.9602 21.243 18.7901C21.4679 18.6199 21.6 18.3543 21.6 18.0723V4.87227C21.6 4.46955 21.3325 4.11585 20.945 4.00624C20.0078 3.74116 19.0197 3.59961 18 3.59961C16.1564 3.59961 14.4193 4.0622 12.9 4.87745V20.183Z"
          fill="currentColor"
        />
        <path
          d="M11.1 4.87745C9.58078 4.0622 7.8437 3.59961 6.00002 3.59961C4.98037 3.59961 3.99229 3.74116 3.05508 4.00624C2.66756 4.11585 2.40002 4.46955 2.40002 4.87227V18.0723C2.40002 18.3543 2.53219 18.6199 2.75709 18.7901C2.98199 18.9602 3.27362 19.015 3.54497 18.9383C4.32435 18.7179 5.14763 18.5996 6.00002 18.5996C7.89463 18.5996 9.65052 19.1841 11.1 20.183V4.87745Z"
          fill="currentColor"
        />
      </g>
    </g>
  </svg>
);
export default BookOpenIcon;
