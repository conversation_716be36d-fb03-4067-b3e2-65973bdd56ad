import type { SVGProps } from 'react';
import * as React from 'react';

const PanelRightOpenIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/panel-right-open">
      <g id="Icon">
        <path
          d="M9.86328 8.36328C10.2148 8.01181 10.7852 8.01181 11.1367 8.36328C11.4882 8.71475 11.4882 9.28525 11.1367 9.63672L8.77344 12L11.1367 14.3633L11.1982 14.4316C11.4867 14.7851 11.4663 15.3072 11.1367 15.6367C10.8072 15.9663 10.2851 15.9867 9.93164 15.6982L9.86328 15.6367L6.86328 12.6367C6.51181 12.2852 6.51181 11.7148 6.86328 11.3633L9.86328 8.36328Z"
          fill="currentColor"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M19 2.09961C20.6016 2.09961 21.9004 3.39837 21.9004 5V19C21.9004 20.6016 20.6016 21.9004 19 21.9004H5C3.39837 21.9004 2.09961 20.6016 2.09961 19V5C2.09961 3.39837 3.39837 2.09961 5 2.09961H19ZM5 3.90039C4.39249 3.90039 3.90039 4.39249 3.90039 5V19C3.90039 19.6075 4.39249 20.0996 5 20.0996H14.0996V3.90039H5Z"
          fill="currentColor"
        />
      </g>
    </g>
  </svg>
);
export default PanelRightOpenIcon;
