import type { SVGProps } from 'react';
import * as React from 'react';

const PresentationChartLineIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/presentation-chart-line">
      <path
        id="Icon"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.19995 3.30039C1.19995 2.80333 1.60289 2.40039 2.09995 2.40039H21.9C22.397 2.40039 22.8 2.80333 22.8 3.30039C22.8 3.79745 22.397 4.20039 21.9 4.20039H21.6V14.7004C21.6 16.5229 20.1225 18.0004 18.3 18.0004H17.0129L17.971 21.6731C18.0965 22.1541 17.8083 22.6457 17.3274 22.7712C16.8464 22.8966 16.3548 22.6085 16.2293 22.1275L16.0918 21.6003H7.90855L7.77103 22.1275C7.64556 22.6085 7.15395 22.8966 6.67299 22.7712C6.19203 22.6457 5.90385 22.1541 6.02931 21.6731L6.98742 18.0004H5.69995C3.87741 18.0004 2.39995 16.5229 2.39995 14.7004V4.20039H2.09995C1.60289 4.20039 1.19995 3.79745 1.19995 3.30039ZM8.84766 18.0004L8.37812 19.8003H15.6222L15.1527 18.0004H8.84766ZM17.835 7.28295C18.1236 7.68757 18.0297 8.24962 17.625 8.53831C16.113 9.61707 14.7475 10.8689 13.5485 12.2565C13.385 12.4457 13.1502 12.5584 12.9003 12.5675C12.6504 12.5765 12.408 12.4813 12.2311 12.3044L10.2 10.2733L7.53635 12.9369C7.18488 13.2883 6.61503 13.2883 6.26356 12.9369C5.91208 12.5854 5.91208 12.0155 6.26356 11.6641L9.56356 8.36407C9.91503 8.0126 10.4849 8.0126 10.8363 8.36407L12.8326 10.3603C13.9616 9.15056 15.2147 8.04679 16.5796 7.07302C16.9842 6.78433 17.5463 6.87832 17.835 7.28295Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default PresentationChartLineIcon;
