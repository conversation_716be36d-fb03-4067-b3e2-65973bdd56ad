import type { SVGProps } from 'react';
import * as React from 'react';

const ReadCheckIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/read-check">
      <g id="Icon">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M2.29932 11.2929C2.69094 10.9024 3.32586 10.9024 3.71748 11.2929L7.72862 15.2929C8.12023 15.6834 8.12023 16.3166 7.72862 16.7071C7.337 17.0976 6.70208 17.0976 6.31046 16.7071L2.29932 12.7071C1.90771 12.3166 1.90771 11.6834 2.29932 11.2929Z"
          fill="currentColor"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M9.31873 12.2929C9.71035 11.9024 10.3453 11.9024 10.7369 12.2929L13.7452 15.2929C14.1368 15.6834 14.1368 16.3166 13.7452 16.7071C13.3536 17.0976 12.7187 17.0976 12.3271 16.7071L9.31873 13.7071C8.92712 13.3166 8.92712 12.6834 9.31873 12.2929Z"
          fill="currentColor"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M15.7509 7.29289C16.1425 7.68342 16.1425 8.31658 15.7509 8.70711L7.72862 16.7071C7.337 17.0976 6.70208 17.0976 6.31046 16.7071C5.91885 16.3166 5.91882 15.6834 6.31043 15.2929L14.3327 7.29289C14.7243 6.90237 15.3593 6.90237 15.7509 7.29289Z"
          fill="currentColor"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M21.7676 7.29289C22.1592 7.68342 22.1592 8.31658 21.7676 8.70711L13.7452 16.7071C13.3536 17.0976 12.7187 17.0976 12.3271 16.7071C11.9355 16.3166 11.9355 15.6834 12.3272 15.2929L20.3494 7.29289C20.741 6.90237 21.376 6.90237 21.7676 7.29289Z"
          fill="currentColor"
        />
      </g>
    </g>
  </svg>
);
export default ReadCheckIcon;
