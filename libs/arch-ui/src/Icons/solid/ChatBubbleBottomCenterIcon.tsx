import type { SVGProps } from 'react';
import * as React from 'react';

const ChatBubbleBottomCenterIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/chat-bubble-bottom-center">
      <path
        id="Icon"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.11566 3.02839C6.68327 2.61476 9.31684 2.3999 12 2.3999C14.6831 2.3999 17.3166 2.61476 19.8842 3.02839C21.6079 3.30606 22.8 4.8158 22.8 6.51096V12.6888C22.8 14.384 21.6079 15.8937 19.8842 16.1714C18.4828 16.3972 17.0616 16.5637 15.6235 16.6685C15.2878 16.6929 14.9985 16.8842 14.853 17.1641L12.7984 21.1151C12.6435 21.413 12.3357 21.5999 12 21.5999C11.6642 21.5999 11.3564 21.413 11.2015 21.1151L9.14692 17.1641C9.00139 16.8842 8.71208 16.6929 8.37643 16.6685C6.93827 16.5637 5.51712 16.3972 4.11566 16.1714C2.39201 15.8937 1.19995 14.384 1.19995 12.6888V6.51096C1.19995 4.8158 2.39202 3.30606 4.11566 3.02839Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default ChatBubbleBottomCenterIcon;
