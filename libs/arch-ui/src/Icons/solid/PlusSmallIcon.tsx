import type { SVGProps } from 'react';
import * as React from 'react';

const PlusSmallIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/plus-small">
      <path
        id="Icon"
        d="M12.9 8.1002C12.9 7.60314 12.497 7.2002 12 7.2002C11.5029 7.2002 11.1 7.60314 11.1 8.1002V11.1002H8.09995C7.60289 11.1002 7.19995 11.5031 7.19995 12.0002C7.19995 12.4973 7.60289 12.9002 8.09995 12.9002L11.1 12.9002V15.9002C11.1 16.3973 11.5029 16.8002 12 16.8002C12.497 16.8002 12.9 16.3973 12.9 15.9002V12.9002L15.9 12.9002C16.397 12.9002 16.8 12.4973 16.8 12.0002C16.8 11.5031 16.397 11.1002 15.9 11.1002H12.9V8.1002Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default PlusSmallIcon;
