import type { SVGProps } from 'react';
import * as React from 'react';

const ShoppingCartIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/shopping-cart">
      <g id="Icon">
        <path
          d="M1.19995 2.1002C1.19995 1.60314 1.60289 1.2002 2.09995 1.2002H4.05411C5.10303 1.2002 5.99109 1.97418 6.13441 3.01326L6.21541 3.60049C11.7109 3.61535 17.0737 4.19866 22.2477 5.29503C22.7273 5.39667 23.0374 5.86355 22.945 6.34507C22.4573 8.88677 21.806 11.3704 21.0024 13.7845C20.88 14.1521 20.536 14.4002 20.1485 14.4002H7.19995C7.06275 14.4002 6.92816 14.4093 6.79666 14.427C5.74249 14.5682 4.86106 15.2585 4.4495 16.2002H20.6999C21.197 16.2002 21.5999 16.6031 21.5999 17.1002C21.5999 17.5973 21.197 18.0002 20.6999 18.0002H3.31132C3.06076 18.0002 2.82155 17.8957 2.65123 17.712C2.48091 17.5282 2.39491 17.2818 2.41391 17.0319C2.56274 15.0756 3.88226 13.4484 5.67389 12.8481L4.35129 3.2592C4.33082 3.11076 4.20395 3.0002 4.05411 3.0002H2.09995C1.60289 3.0002 1.19995 2.59725 1.19995 2.1002Z"
          fill="currentColor"
        />
        <path
          d="M7.19995 21.0002C7.19995 21.9943 6.39406 22.8002 5.39995 22.8002C4.40584 22.8002 3.59995 21.9943 3.59995 21.0002C3.59995 20.0061 4.40584 19.2002 5.39995 19.2002C6.39406 19.2002 7.19995 20.0061 7.19995 21.0002Z"
          fill="currentColor"
        />
        <path
          d="M18.5999 22.8002C19.5941 22.8002 20.3999 21.9943 20.3999 21.0002C20.3999 20.0061 19.5941 19.2002 18.5999 19.2002C17.6058 19.2002 16.7999 20.0061 16.7999 21.0002C16.7999 21.9943 17.6058 22.8002 18.5999 22.8002Z"
          fill="currentColor"
        />
      </g>
    </g>
  </svg>
);
export default ShoppingCartIcon;
