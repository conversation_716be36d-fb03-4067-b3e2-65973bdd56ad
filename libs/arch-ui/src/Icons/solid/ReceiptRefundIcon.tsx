import type { SVGProps } from 'react';
import * as React from 'react';

const ReceiptRefundIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/receipt-refund">
      <path
        id="Icon"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.91604 2.77294C7.9099 2.527 9.94035 2.40039 12 2.40039C14.0596 2.40039 16.0901 2.527 18.0839 2.77294C19.433 2.93935 20.4 4.09688 20.4 5.42032V20.7004C20.4 21.0097 20.2411 21.2974 19.9793 21.4621C19.7175 21.6269 19.3894 21.6456 19.1105 21.5118L15.75 19.8987L12.3894 21.5118C12.1432 21.6299 11.8567 21.6299 11.6105 21.5118L8.24997 19.8987L4.88943 21.5118C4.61056 21.6456 4.28248 21.6269 4.02066 21.4621C3.75884 21.2974 3.59998 21.0097 3.59998 20.7004V5.42032C3.59998 4.09687 4.56692 2.93935 5.91604 2.77294ZM11.702 7.56936C12.0715 7.23684 12.1015 6.66778 11.7689 6.29832C11.4364 5.92886 10.8674 5.89891 10.4979 6.23143L7.49791 8.93143C7.30826 9.1021 7.19998 9.34525 7.19998 9.60039C7.19998 9.85553 7.30826 10.0987 7.49791 10.2694L10.4979 12.9694C10.8674 13.3019 11.4364 13.2719 11.7689 12.9025C12.1015 12.533 12.0715 11.9639 11.702 11.6314L10.4453 10.5004H12.75C13.9926 10.5004 15 11.5078 15 12.7504C15 13.993 13.9926 15.0004 12.75 15.0004C12.2529 15.0004 11.85 15.4033 11.85 15.9004C11.85 16.3974 12.2529 16.8004 12.75 16.8004C14.9867 16.8004 16.8 14.9871 16.8 12.7504C16.8 10.5136 14.9867 8.70039 12.75 8.70039H10.4453L11.702 7.56936Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default ReceiptRefundIcon;
