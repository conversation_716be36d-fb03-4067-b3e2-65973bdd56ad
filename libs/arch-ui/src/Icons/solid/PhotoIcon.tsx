import type { SVGProps } from 'react';
import * as React from 'react';

const PhotoIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/photo">
      <g id="Icon">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M2.5 5.49977C2.5 4.25713 3.50736 3.24976 4.75 3.25C6.07246 3.25026 7.39491 3.25069 8.71737 3.25113C12.2275 3.25229 15.7375 3.25344 19.2476 3.25128C20.4912 3.25052 21.5 4.25822 21.5 5.5018V14.9998C21.5 15.0016 21.5 15.0035 21.5 15.0054V18.5C21.5 19.7426 20.4926 20.75 19.25 20.75H4.75C3.50736 20.75 2.5 19.7426 2.5 18.5V14H2.50002L2.5 5.49977ZM4 15.7745V18.5C4 18.9142 4.33579 19.25 4.75 19.25H19.25C19.6642 19.25 20 18.9142 20 18.5V15.3104L16.9268 12.2372C16.8292 12.1396 16.6709 12.1396 16.5732 12.2372L14.2804 14.5301C14.1397 14.6707 13.9489 14.7498 13.75 14.7498C13.5511 14.7498 13.3603 14.6707 13.2197 14.5301L8.98817 10.2986C8.88475 10.1952 8.71503 10.2022 8.62055 10.3139L4 15.7745ZM13 7.90038C13 7.26525 13.5149 6.75038 14.15 6.75038C14.7851 6.75038 15.3 7.26525 15.3 7.90038C15.3 8.53551 14.7851 9.05038 14.15 9.05038C13.5149 9.05038 13 8.53551 13 7.90038Z"
          fill="currentColor"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M2.5 5.49977C2.5 4.25713 3.50736 3.24976 4.75 3.25C6.07246 3.25026 7.39491 3.25069 8.71737 3.25113C12.2275 3.25229 15.7375 3.25344 19.2476 3.25128C20.4912 3.25052 21.5 4.25822 21.5 5.5018V14.9998C21.5 15.0016 21.5 15.0035 21.5 15.0054V18.5C21.5 19.7426 20.4926 20.75 19.25 20.75H4.75C3.50736 20.75 2.5 19.7426 2.5 18.5V14H2.50002L2.5 5.49977ZM4 15.7745V18.5C4 18.9142 4.33579 19.25 4.75 19.25H19.25C19.6642 19.25 20 18.9142 20 18.5V15.3104L16.9268 12.2372C16.8292 12.1396 16.6709 12.1396 16.5732 12.2372L14.2804 14.5301C14.1397 14.6707 13.9489 14.7498 13.75 14.7498C13.5511 14.7498 13.3603 14.6707 13.2197 14.5301L8.98817 10.2986C8.88475 10.1952 8.71503 10.2022 8.62055 10.3139L4 15.7745ZM13 7.90038C13 7.26525 13.5149 6.75038 14.15 6.75038C14.7851 6.75038 15.3 7.26525 15.3 7.90038C15.3 8.53551 14.7851 9.05038 14.15 9.05038C13.5149 9.05038 13 8.53551 13 7.90038Z"
          fill="currentColor"
        />
      </g>
    </g>
  </svg>
);
export default PhotoIcon;
