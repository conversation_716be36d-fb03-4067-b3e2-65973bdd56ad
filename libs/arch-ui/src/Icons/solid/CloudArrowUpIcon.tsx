import type { SVGProps } from 'react';
import * as React from 'react';

const CloudArrowUpIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/cloud-arrow-up">
      <path
        id="Icon"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.59995 20.4001C3.61761 20.4001 1.19995 17.9824 1.19995 15.0001C1.19995 12.6222 2.73696 10.6032 4.8717 9.88258C4.8245 9.59541 4.79995 9.30062 4.79995 9.0001C4.79995 6.01776 7.21761 3.6001 10.2 3.6001C12.1684 3.6001 13.8909 4.65335 14.8345 6.22705C15.2626 6.07995 15.7219 6.0001 16.1999 6.0001C18.5195 6.0001 20.4 7.8805 20.4 10.2001C20.4 10.6001 20.344 10.987 20.2396 11.3535C21.7623 12.1583 22.7999 13.758 22.7999 15.6001C22.7999 18.2511 20.6509 20.4001 18 20.4001H6.59995ZM10.875 17.1001C10.875 17.7214 11.3786 18.2251 12 18.2251C12.6213 18.2251 13.125 17.7214 13.125 17.1001L13.125 12.1649L15.0756 14.2656C15.4983 14.7209 16.2102 14.7473 16.6655 14.3245C17.1208 13.9017 17.1471 13.1899 16.7243 12.7346L12.8243 8.53459C12.6115 8.30535 12.3128 8.1751 12 8.1751C11.6871 8.1751 11.3884 8.30535 11.1756 8.53459L7.27556 12.7346C6.85278 13.1899 6.87915 13.9017 7.33444 14.3245C7.78974 14.7473 8.50157 14.7209 8.92435 14.2656L10.875 12.1649L10.875 17.1001Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default CloudArrowUpIcon;
