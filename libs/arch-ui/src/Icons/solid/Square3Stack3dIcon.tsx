import type { SVGProps } from 'react';
import * as React from 'react';

const Square3Stack3dIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/square-3-stack-3d">
      <g id="Icon">
        <path
          d="M3.83486 15.4434L2.84488 16.0238C2.56932 16.1853 2.40002 16.4808 2.40002 16.8002C2.40002 17.1196 2.56932 17.4151 2.84488 17.5766L11.5449 22.6766C11.8259 22.8414 12.1741 22.8414 12.4552 22.6766L21.1552 17.5766C21.4307 17.4151 21.6 17.1196 21.6 16.8002C21.6 16.4808 21.4307 16.1853 21.1552 16.0238L20.1652 15.4434L13.3655 19.4295C12.5223 19.9238 11.4778 19.9238 10.6346 19.4295L3.83486 15.4434Z"
          fill="currentColor"
        />
        <path
          d="M3.83486 10.6434L2.84488 11.2238C2.56932 11.3853 2.40002 11.6808 2.40002 12.0002C2.40002 12.3196 2.56932 12.6151 2.84488 12.7766L11.5449 17.8766C11.8259 18.0414 12.1741 18.0414 12.4552 17.8766L21.1552 12.7766C21.4307 12.6151 21.6 12.3196 21.6 12.0002C21.6 11.6808 21.4307 11.3853 21.1552 11.2238L20.1652 10.6434L13.3655 14.6295C12.5223 15.1238 11.4778 15.1238 10.6346 14.6295L3.83486 10.6434Z"
          fill="currentColor"
        />
        <path
          d="M12.4552 1.32377C12.1741 1.159 11.8259 1.159 11.5449 1.32377L2.84488 6.42377C2.56932 6.5853 2.40002 6.88078 2.40002 7.2002C2.40002 7.51961 2.56932 7.81509 2.84488 7.97662L11.5449 13.0766C11.8259 13.2414 12.1741 13.2414 12.4552 13.0766L21.1552 7.97662C21.4307 7.81509 21.6 7.51961 21.6 7.2002C21.6 6.88078 21.4307 6.5853 21.1552 6.42377L12.4552 1.32377Z"
          fill="currentColor"
        />
      </g>
    </g>
  </svg>
);
export default Square3Stack3dIcon;
