import type { SVGProps } from 'react';
import * as React from 'react';

const RocketLaunchIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/rocket-launch">
      <g id="Icon">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M5.52711 15.5632C5.8313 15.9563 5.75921 16.5215 5.3661 16.8257C4.65505 17.3759 4.19995 18.2347 4.19995 19.2002C4.19995 19.3885 4.21719 19.5722 4.25007 19.7501C4.42791 19.783 4.61163 19.8002 4.79995 19.8002C5.76544 19.8002 6.62423 19.3451 7.17443 18.6341C7.47862 18.2409 8.04389 18.1688 8.437 18.473C8.83011 18.7772 8.9022 19.3425 8.59801 19.7356C7.72159 20.8682 6.34604 21.6002 4.79995 21.6002C4.24799 21.6002 3.71596 21.5067 3.22003 21.3339C2.96061 21.2435 2.75669 21.0395 2.6663 20.7801C2.49348 20.2842 2.39995 19.7522 2.39995 19.2002C2.39995 17.6541 3.13192 16.2786 4.26455 15.4021C4.65766 15.098 5.22293 15.17 5.52711 15.5632Z"
          fill="currentColor"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M6.90278 14.4002C7.68852 15.4053 8.5948 16.3116 9.59995 17.0974V21.9002C9.59995 22.3973 10.0029 22.8002 10.5 22.8002C13.8137 22.8002 16.5 20.1139 16.5 16.8002C16.5 16.2119 16.415 15.6422 16.2565 15.1035C20.216 12.2758 22.8 7.64 22.8 2.4002C22.8 2.295 22.7989 2.19003 22.7968 2.0853C22.7873 1.60222 22.3979 1.21289 21.9148 1.20331C21.8101 1.20124 21.7052 1.2002 21.6 1.2002C16.3601 1.2002 11.7243 3.78417 8.8966 7.74369C8.3579 7.58513 7.78828 7.5002 7.19995 7.5002C3.88624 7.5002 1.19995 10.1865 1.19995 13.5002C1.19995 13.9973 1.60289 14.4002 2.09995 14.4002H6.90278ZM15.6 10.8002C16.9254 10.8002 18 9.72568 18 8.4002C18 7.07471 16.9254 6.0002 15.6 6.0002C14.2745 6.0002 13.2 7.07471 13.2 8.4002C13.2 9.72568 14.2745 10.8002 15.6 10.8002Z"
          fill="currentColor"
        />
      </g>
    </g>
  </svg>
);
export default RocketLaunchIcon;
