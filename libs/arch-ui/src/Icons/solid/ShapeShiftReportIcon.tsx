import type { SVGProps } from 'react';
import * as React from 'react';

const <PERSON>ha<PERSON><PERSON>hiftReportIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/shape-shift-report">
      <g id="Icon">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M20.997 7.87174C21.013 7.6248 20.9227 7.37786 20.7386 7.19688L15.712 2.25628C15.5278 2.07531 15.276 1.98657 15.0254 2.00234C15.019 2.0021 15.0127 2.00169 15.0063 2.00128C14.9966 2.00064 14.9867 2 14.9766 2H6.14571C5.51433 2 4.99996 2.50498 4.99996 3.12613V5.3848H6.78183V3.75195H14.1903V7.81745C14.1903 8.30083 14.5894 8.69313 15.0812 8.69313H19.2175V20.2486H4.99996V20.8739C4.99996 21.4944 5.51374 22 6.14571 22H19.8542C20.4856 22 21 21.495 21 20.8739V7.91903C21 7.9097 20.9993 7.90037 20.9987 7.89092C20.9983 7.8844 20.9972 7.87841 20.997 7.87174ZM15.9721 6.94002V4.989L17.9571 6.94002H15.9721Z"
          fill="currentColor"
        />
        <path
          d="M6.54531 15.6458C6.70431 15.924 6.9942 16.0796 7.29154 16.0796C7.43792 16.0796 7.58602 16.042 7.72149 15.9634C8.13306 15.7239 8.27428 15.1928 8.03663 14.7781L7.22209 13.3561V10.9474C7.22209 10.4684 6.83634 10.0796 6.36105 10.0796C5.88575 10.0796 5.50001 10.4684 5.50001 10.9474V13.5887C5.50001 13.7408 5.53961 13.8907 5.61539 14.0226L6.54531 15.6458Z"
          fill="currentColor"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M11.9974 13.3375C11.9581 11.8926 11.3663 10.5425 10.3321 9.53521C9.88906 9.10345 9.38495 8.7558 8.841 8.49866C9.08747 8.3629 9.2548 8.10043 9.2548 7.79857C9.2548 7.35775 8.89784 6.99999 8.458 6.99999H4.63336C4.19353 6.99999 3.83656 7.35775 3.83656 7.79857C3.83656 8.08339 3.98583 8.33308 4.21 8.47417C3.57681 8.76485 2.99886 9.17745 2.50591 9.69919C1.4961 10.7682 0.962239 12.1679 1.00208 13.6394C1.04139 15.0843 1.63314 16.4349 2.66739 17.4417C3.69951 18.4468 5.05992 19 6.49787 19C6.54834 19 6.59986 18.9995 6.65086 18.9979C8.1191 18.9574 9.48428 18.3462 10.4941 17.2772C11.5039 16.2081 12.0372 14.809 11.9974 13.3375ZM6.60677 17.4023C6.57012 17.4034 6.534 17.4039 6.49787 17.4039C4.39007 17.4039 2.65358 15.721 2.59515 13.5963C2.53618 11.4348 4.23603 9.63477 6.3927 9.57567C6.42935 9.57461 6.46547 9.57408 6.50159 9.57408C8.60939 9.57408 10.3459 11.257 10.4043 13.3817C10.4633 15.5432 8.76344 17.3432 6.60677 17.4023Z"
          fill="currentColor"
        />
      </g>
    </g>
  </svg>
);
export default ShapeShiftReportIcon;
