import type { SVGProps } from 'react';
import * as React from 'react';

const PencilIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/pencil">
      <g id="Icon">
        <path
          d="M4.75226 15.3586L14.8278 5.28302L18.7169 9.17211L8.64135 19.2477C8.55902 19.33 8.45865 19.3921 8.34819 19.4289L4.10555 20.8431C3.83605 20.9329 3.53892 20.8628 3.33805 20.6619C3.13717 20.461 3.06703 20.1639 3.15687 19.8944L4.57108 15.6518C4.6079 15.5413 4.66993 15.4409 4.75226 15.3586Z"
          fill="currentColor"
        />
        <path
          d="M21.3693 4.04489C22.0527 4.72831 22.0527 5.83635 21.3693 6.51977L19.7776 8.11145L15.8885 4.22236L17.4802 2.63068C18.1636 1.94726 19.2716 1.94726 19.9551 2.63068L21.3693 4.04489Z"
          fill="currentColor"
        />
      </g>
    </g>
  </svg>
);
export default PencilIcon;
