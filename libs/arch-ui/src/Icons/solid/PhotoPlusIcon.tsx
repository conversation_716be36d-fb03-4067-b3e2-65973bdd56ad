import type { SVGProps } from 'react';
import * as React from 'react';

const PhotoPlusIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/photo-plus">
      <g id="Icon">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M4.75 3.25C3.50736 3.24976 2.5 4.25713 2.5 5.49977V18.5C2.5 19.7426 3.50736 20.75 4.75 20.75H12.6659C12.4244 20.2825 12.2425 19.7791 12.1304 19.25H4.75C4.33579 19.25 4 18.9142 4 18.5V15.7745L8.62055 10.3139C8.71503 10.2022 8.88475 10.1952 8.98817 10.2986L13.1528 14.4632C14.2442 12.97 16.0088 12 18 12C19.3062 12 20.5149 12.4174 21.5 13.1261V5.5018C21.5 4.25822 20.4912 3.25052 19.2476 3.25128C15.7375 3.25345 12.2275 3.25229 8.71737 3.25113C7.39491 3.25069 6.07246 3.25026 4.75 3.25ZM14.15 6.75038C13.5149 6.75038 13 7.26525 13 7.90038C13 8.53551 13.5149 9.05038 14.15 9.05038C14.7851 9.05038 15.3 8.53551 15.3 7.90038C15.3 7.26525 14.7851 6.75038 14.15 6.75038Z"
          fill="currentColor"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M18 22.5C20.4853 22.5 22.5 20.4853 22.5 18C22.5 15.5147 20.4853 13.5 18 13.5C15.5147 13.5 13.5 15.5147 13.5 18C13.5 20.4853 15.5147 22.5 18 22.5ZM18.75 15.75C18.75 15.3358 18.4142 15 18 15C17.5858 15 17.25 15.3358 17.25 15.75V17.25H15.75C15.3358 17.25 15 17.5858 15 18C15 18.4142 15.3358 18.75 15.75 18.75H17.25V20.25C17.25 20.6642 17.5858 21 18 21C18.4142 21 18.75 20.6642 18.75 20.25V18.75H20.25C20.6642 18.75 21 18.4142 21 18C21 17.5858 20.6642 17.25 20.25 17.25H18.75V15.75Z"
          fill="currentColor"
        />
      </g>
    </g>
  </svg>
);
export default PhotoPlusIcon;
