import type { SVGProps } from 'react';
import * as React from 'react';

const BugAntIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/bug-ant">
      <path
        id="Icon"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.87276 1.36773C8.27787 1.65575 8.37279 2.21763 8.08477 2.62274C7.859 2.9403 7.67111 3.28616 7.52741 3.65384C7.74991 3.859 7.9865 4.04902 8.23551 4.22228C9.11398 3.11334 10.4732 2.40036 12 2.40036C13.5268 2.40036 14.8861 3.11334 15.7645 4.22228C16.0135 4.04902 16.2501 3.85899 16.4726 3.65383C16.3289 3.28616 16.141 2.9403 15.9153 2.62274C15.6273 2.21763 15.7222 1.65575 16.1273 1.36773C16.5324 1.07972 17.0943 1.17464 17.3823 1.57975C17.8247 2.20202 18.1623 2.90501 18.3687 3.66271C18.4503 3.96256 18.3715 4.28332 18.1602 4.51117C17.6969 5.0107 17.1734 5.45389 16.6011 5.8292C16.7306 6.26425 16.8 6.72468 16.8 7.20036C16.8 7.82454 16.4386 8.35615 15.9324 8.62473C15.4912 8.85889 15.0266 9.05485 14.5432 9.20825C14.6995 9.4575 14.8203 9.73138 14.8983 10.0227C16.5938 9.81337 18.2328 9.42268 19.7954 8.87075C19.7985 8.71434 19.8 8.55754 19.8 8.40036C19.8 7.61987 19.7618 6.84856 19.6873 6.08818C19.6388 5.59349 20.0005 5.15316 20.4952 5.10466C20.9899 5.05616 21.4302 5.41786 21.4787 5.91255C21.559 6.73121 21.6 7.56113 21.6 8.40036C21.6 8.78435 21.5914 9.1664 21.5744 9.54631C21.5584 9.90434 21.3315 10.2187 20.9967 10.3467C19.0405 11.0946 16.9674 11.6057 14.8129 11.8451C14.7752 11.9466 14.7321 12.0456 14.684 12.1417C15.3765 12.215 16.0608 12.3165 16.7357 12.4448C18.2018 12.7236 19.623 13.1295 20.9874 13.6504C21.3522 13.7897 21.5855 14.1483 21.5651 14.5383C21.454 16.6606 21.0802 18.7141 20.4753 20.6667C20.3282 21.1415 19.8241 21.4071 19.3493 21.2601C18.8745 21.113 18.6089 20.6088 18.7559 20.134C19.2543 18.5254 19.5838 16.8422 19.7237 15.1048C19.1293 14.897 18.5239 14.7126 17.9087 14.5525C17.9687 14.893 18 15.2432 18 15.6004C18 17.7768 17.38 19.5747 16.2886 20.8435C15.191 22.1195 13.6648 22.8004 12 22.8004C10.3352 22.8004 8.80904 22.1195 7.71148 20.8435C6.62009 19.5747 6.00002 17.7768 6.00002 15.6004C6.00002 15.2432 6.03132 14.893 6.09136 14.5525C5.47613 14.7126 4.87077 14.897 4.27636 15.1048C4.41622 16.8422 4.74578 18.5255 5.2441 20.134C5.39119 20.6088 5.12553 21.113 4.65073 21.2601C4.17594 21.4071 3.6718 21.1415 3.52472 20.6667C2.91984 18.7141 2.54604 16.6606 2.43496 14.5383C2.41455 14.1484 2.6479 13.7897 3.01267 13.6505C4.37703 13.1295 5.79825 12.7236 7.26436 12.4448C7.93924 12.3165 8.62352 12.215 9.31604 12.1417C9.26796 12.0456 9.22488 11.9466 9.18714 11.8451C7.03268 11.6057 4.9596 11.0946 3.00334 10.3467C2.66859 10.2187 2.44167 9.90435 2.42563 9.54632C2.40862 9.16641 2.40002 8.78436 2.40002 8.40036C2.40002 7.56113 2.44108 6.73121 2.52134 5.91255C2.56984 5.41786 3.01018 5.05616 3.50486 5.10466C3.99955 5.15316 4.36125 5.59349 4.31275 6.08818C4.23821 6.84857 4.20002 7.61987 4.20002 8.40036C4.20002 8.55754 4.20157 8.71435 4.20465 8.87076C5.76727 9.42269 7.40623 9.81338 9.10179 10.0227C9.17977 9.73138 9.30051 9.4575 9.45689 9.20825C8.97349 9.05485 8.50888 8.85889 8.0676 8.62473C7.56147 8.35615 7.20003 7.82454 7.20003 7.20036C7.20003 6.72468 7.26947 6.26425 7.39897 5.8292C6.82665 5.45388 6.30318 5.0107 5.8399 4.51117C5.62857 4.28332 5.54975 3.96256 5.6314 3.66271C5.83771 2.90501 6.17533 2.20202 6.61775 1.57975C6.90577 1.17464 7.46765 1.07972 7.87276 1.36773Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default BugAntIcon;
