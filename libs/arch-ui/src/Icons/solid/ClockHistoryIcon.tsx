import type { SVGProps } from 'react';
import * as React from 'react';

const ClockHistoryIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/clock-history">
      <path
        id="Vector"
        d="M5.34819 14.7011C6.44383 17.5085 9.19734 19.5 12.4209 19.5C16.6067 19.5 20 16.1421 20 12C20 7.85786 16.6067 4.5 12.4209 4.5C10.1762 4.5 8.15945 5.46563 6.77166 7C6.11921 7.72137 5.60578 8.56845 5.27305 9.5M5.27305 9.5L4 7.83333M5.27305 9.5L7.78955 9.08333M12.6 7.2V13.2H16.2"
        stroke="currentColor"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
  </svg>
);
export default ClockHistoryIcon;
