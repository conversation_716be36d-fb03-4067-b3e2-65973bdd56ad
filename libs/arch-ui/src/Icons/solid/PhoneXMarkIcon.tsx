import type { SVGProps } from 'react';
import * as React from 'react';

const PhoneXMarkIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/phone-x-mark">
      <g id="Icon">
        <path
          d="M15.9364 2.66399C15.5849 2.31252 15.0151 2.31252 14.6636 2.66399C14.3122 3.01547 14.3122 3.58531 14.6636 3.93679L16.7272 6.00039L14.6636 8.06399C14.3122 8.41547 14.3122 8.98531 14.6636 9.33679C15.0151 9.68826 15.5849 9.68826 15.9364 9.33679L18 7.27318L20.0636 9.33679C20.4151 9.68826 20.9849 9.68826 21.3364 9.33679C21.6879 8.98531 21.6879 8.41547 21.3364 8.06399L19.2728 6.00039L21.3364 3.93679C21.6879 3.58531 21.6879 3.01547 21.3364 2.66399C20.9849 2.31252 20.4151 2.31252 20.0636 2.66399L18 4.7276L15.9364 2.66399Z"
          fill="currentColor"
        />
        <path
          d="M4.20002 2.40039C3.20591 2.40039 2.40002 3.20628 2.40002 4.20039V6.00039C2.40002 7.37866 2.57898 8.71649 2.91541 9.9913C4.34292 15.4004 8.6 19.6575 14.0091 21.085C15.2839 21.4214 16.6218 21.6004 18 21.6004H19.8C20.7941 21.6004 21.6 20.7945 21.6 19.8004V18.4223C21.6 17.5787 21.0141 16.8482 20.1905 16.6652L16.3229 15.8058C15.3932 15.5991 14.4634 16.1526 14.2017 17.0684L13.8819 18.1877C13.7411 18.6807 13.216 18.9591 12.7421 18.7636C9.34954 17.3644 6.63604 14.6509 5.23678 11.2583C5.04131 10.7844 5.31974 10.2593 5.81268 10.1185L6.93203 9.79869C7.84783 9.53703 8.40128 8.60724 8.19466 7.67747L7.33521 3.80992C7.15219 2.98635 6.42173 2.40039 5.57807 2.40039H4.20002Z"
          fill="currentColor"
        />
      </g>
    </g>
  </svg>
);
export default PhoneXMarkIcon;
