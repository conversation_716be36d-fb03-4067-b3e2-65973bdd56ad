import type { SVGProps } from 'react';
import * as React from 'react';

const OvalIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/oval">
      <path
        id="Icons"
        d="M21.6 12C21.6 17.302 17.302 21.6 12 21.6C6.69809 21.6 2.40002 17.302 2.40002 12C2.40002 6.69809 6.69809 2.40002 12 2.40002C17.302 2.40002 21.6 6.69809 21.6 12Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default OvalIcon;
