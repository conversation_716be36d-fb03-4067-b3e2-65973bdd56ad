import type { SVGProps } from 'react';
import * as React from 'react';

const PuzzlePieceIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/puzzle-piece">
      <path
        id="Icon"
        d="M14.4 5.36049C14.4 4.87411 14.7147 4.45958 15.0705 4.12802C15.4017 3.81942 15.6 3.42717 15.6 3.0002C15.6 2.00608 14.5254 1.2002 13.2 1.2002C11.8745 1.2002 10.8 2.00608 10.8 3.0002C10.8 3.4346 11.0051 3.83306 11.3468 4.14405C11.6958 4.4618 12 4.86482 12 5.33686C12 6.00854 11.442 6.54735 10.7717 6.5051C9.55263 6.42826 8.34621 6.30535 7.15415 6.13813C6.87441 6.09889 6.59247 6.19326 6.39273 6.39301C6.19298 6.59275 6.09861 6.87469 6.13785 7.15443C6.30507 8.34649 6.42798 9.5529 6.50482 10.7719C6.54707 11.4423 6.00826 12.0002 5.3366 12.0002C4.86457 12.0002 4.46156 11.6961 4.14381 11.347C3.83283 11.0054 3.43436 10.8002 2.99995 10.8002C2.00584 10.8002 1.19995 11.8747 1.19995 13.2002C1.19995 14.5257 2.00584 15.6002 2.99995 15.6002C3.42693 15.6002 3.81919 15.402 4.12778 15.0708C4.45934 14.7149 4.87386 14.4002 5.36023 14.4002C6.04037 14.4002 6.59096 14.9561 6.56508 15.6358C6.50748 17.149 6.37897 18.6438 6.18283 20.117C6.11751 20.6076 6.46058 21.0589 6.95078 21.1272C8.20985 21.3026 9.48405 21.4303 10.7715 21.5085C11.4422 21.5493 12 21.01 12 20.338C12 19.8653 11.6953 19.4617 11.3459 19.1433C11.0048 18.8324 10.8 18.4343 10.8 18.0002C10.8 17.0061 11.8745 16.2002 13.2 16.2002C14.5254 16.2002 15.6 17.0061 15.6 18.0002C15.6 18.4271 15.4018 18.8193 15.0707 19.1278C14.7148 19.4596 14.4 19.8742 14.4 20.3608C14.4 21.0409 14.9558 21.5916 15.6355 21.5666C17.2408 21.5077 18.8268 21.3719 20.3897 21.1626C20.792 21.1088 21.1085 20.7923 21.1624 20.39C21.3716 18.8271 21.5074 17.2411 21.5663 15.6357C21.5913 14.956 21.0407 14.4002 20.3605 14.4002C19.874 14.4002 19.4593 14.7151 19.1276 15.071C18.819 15.4021 18.4268 15.6002 18 15.6002C17.0058 15.6002 16.2 14.5257 16.2 13.2002C16.2 11.8747 17.0058 10.8002 18 10.8002C18.434 10.8002 18.8322 11.005 19.1431 11.3462C19.4615 11.6956 19.865 12.0002 20.3377 12.0002C21.0097 12.0002 21.549 11.4425 21.5082 10.7717C21.43 9.48432 21.3023 8.21013 21.1269 6.95106C21.0586 6.46086 20.6074 6.11779 20.1168 6.18311C18.6435 6.37925 17.1488 6.50776 15.6356 6.56536C14.9559 6.59123 14.4 6.04064 14.4 5.36049Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default PuzzlePieceIcon;
