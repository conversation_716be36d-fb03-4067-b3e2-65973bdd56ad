import type { SVGProps } from 'react';
import * as React from 'react';

const CursorArrowRippleIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/cursor-arrow-ripple">
      <g id="Icon">
        <path
          d="M7.33319 14.2671C4.75573 11.6896 4.75573 7.51075 7.33319 4.93329C9.91065 2.35583 14.0895 2.35583 16.667 4.93329C17.9559 6.2222 18.6001 7.90971 18.6001 9.60019C18.6001 10.0973 19.003 10.5002 19.5001 10.5002C19.9972 10.5002 20.4001 10.0973 20.4001 9.60019C20.4001 7.45184 19.5798 5.30052 17.9398 3.6605C14.6594 0.380094 9.3408 0.380094 6.0604 3.6605C2.78 6.9409 2.78 12.2595 6.0604 15.5399C6.41187 15.8914 6.98172 15.8914 7.33319 15.5399C7.68466 15.1884 7.68466 14.6186 7.33319 14.2671Z"
          fill="currentColor"
        />
        <path
          d="M9.87882 7.47888C8.70725 8.65045 8.70725 10.5499 9.87882 11.7215C10.2303 12.073 10.2303 12.6428 9.87882 12.9943C9.52735 13.3458 8.9575 13.3458 8.60603 12.9943C6.73151 11.1198 6.73151 8.0806 8.60603 6.20608C10.4805 4.33157 13.5197 4.33157 15.3943 6.20608C16.3313 7.14316 16.8001 8.37316 16.8001 9.60019C16.8001 10.0973 16.3972 10.5002 15.9001 10.5002C15.4031 10.5002 15.0001 10.0973 15.0001 9.60019C15.0001 8.83103 14.7074 8.06484 14.1215 7.47888C12.9499 6.3073 11.0504 6.3073 9.87882 7.47888Z"
          fill="currentColor"
        />
        <path
          d="M12.9188 9.01294C12.7046 8.6941 12.3116 8.5464 11.9403 8.6452C11.5691 8.744 11.3015 9.06752 11.2741 9.45068L10.6849 17.6837C10.6596 18.0369 10.8438 18.3721 11.1555 18.5401C11.4671 18.7082 11.8484 18.6779 12.1296 18.4627L13.381 17.505L14.5636 21.9185C14.6923 22.3986 15.1858 22.6836 15.6659 22.5549C16.146 22.4263 16.4309 21.9328 16.3023 21.4526L15.13 17.0776L16.6806 17.3221C17.0341 17.3779 17.3871 17.2189 17.5796 16.9171C17.7721 16.6153 17.7674 16.2282 17.5678 15.9311L12.9188 9.01294Z"
          fill="currentColor"
        />
      </g>
    </g>
  </svg>
);
export default CursorArrowRippleIcon;
