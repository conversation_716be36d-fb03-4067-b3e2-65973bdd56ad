import type { SVGProps } from 'react';
import * as React from 'react';

const ShieldCheckIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/shield-check">
      <path
        id="Icon"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.5933 2.68359C11.8285 2.48828 12.1715 2.48828 12.4067 2.68359C14.7333 4.61542 17.6778 5.82868 20.9 5.98292C21.1896 5.99678 21.4369 6.20597 21.4749 6.49342C21.5574 7.11725 21.6 7.75367 21.6 8.40005C21.6 14.595 17.6882 19.8762 12.1999 21.9083C12.0712 21.956 11.9293 21.956 11.8006 21.9083C6.31208 19.8763 2.40002 14.595 2.40002 8.3999C2.40002 7.75358 2.4426 7.11721 2.52511 6.49343C2.56314 6.20598 2.81044 5.99678 3.10007 5.98292C6.32222 5.82868 9.26679 4.61542 11.5933 2.68359ZM16.6279 9.82906C16.9202 9.42707 16.8314 8.8642 16.4294 8.57184C16.0274 8.27949 15.4645 8.36836 15.1722 8.77035L10.9917 14.5185L8.73642 12.2633C8.38495 11.9118 7.8151 11.9118 7.46363 12.2633C7.11216 12.6148 7.11216 13.1846 7.46363 13.5361L10.4636 16.5361C10.6497 16.7222 10.908 16.8175 11.1704 16.7969C11.4328 16.7764 11.6731 16.6419 11.8279 16.4291L16.6279 9.82906Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default ShieldCheckIcon;
