import type { SVGProps } from 'react';
import * as React from 'react';

const BellSnoozeIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/bell-snooze">
      <path
        id="Icon"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.79991 9.60039C4.79991 5.62394 8.02346 2.40039 11.9999 2.40039C15.9764 2.40039 19.1999 5.62394 19.1999 9.60039C19.1999 11.8643 19.7444 13.9985 20.7087 15.8816C20.8368 16.1316 20.8407 16.427 20.7195 16.6803C20.5982 16.9337 20.3657 17.1159 20.0906 17.173C18.8095 17.439 17.5057 17.6431 16.1828 17.782C15.99 19.9228 14.1909 21.6004 12 21.6004C9.809 21.6004 8.00988 19.9228 7.81706 17.782C6.49413 17.6431 5.1904 17.439 3.90918 17.173C3.63417 17.1159 3.40158 16.9337 3.28033 16.6803C3.15907 16.427 3.16305 16.1316 3.29108 15.8816C4.25543 13.9985 4.79991 11.8643 4.79991 9.60039ZM11.9999 18.0004C11.2142 18.0004 10.4337 17.9775 9.65911 17.9323C9.90112 19.0018 10.8573 19.8004 12 19.8004C13.1426 19.8004 14.0988 19.0018 14.3408 17.9323C13.5662 17.9775 12.7857 18.0004 11.9999 18.0004ZM10.5 7.20039C10.0029 7.20039 9.59995 7.60333 9.59995 8.10039C9.59995 8.59745 10.0029 9.00039 10.5 9.00039H11.7511L9.76759 11.7773C9.57164 12.0516 9.54544 12.4125 9.6997 12.7122C9.85397 13.012 10.1628 13.2004 10.5 13.2004H13.5C13.997 13.2004 14.4 12.7974 14.4 12.3004C14.4 11.8033 13.997 11.4004 13.5 11.4004H12.2488L14.2323 8.62351C14.4283 8.34917 14.4545 7.98833 14.3002 7.68857C14.1459 7.3888 13.8371 7.20039 13.5 7.20039H10.5Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default BellSnoozeIcon;
