import type { SVGProps } from 'react';
import * as React from 'react';

const RectangleIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/rectangle">
      <path
        id="Icon"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.10002 2.40002C3.60886 2.40002 2.40002 3.60886 2.40002 5.10002V18.9C2.40002 20.3912 3.60886 21.6 5.10002 21.6H18.9C20.3912 21.6 21.6 20.3912 21.6 18.9V5.10002C21.6 3.60886 20.3912 2.40002 18.9 2.40002H5.10002Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default RectangleIcon;
