import type { SVGProps } from 'react';
import * as React from 'react';

const PanelRightCloseIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/panel-right-close">
      <g id="Icon">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M19 2.09961C20.6016 2.09961 21.9004 3.39837 21.9004 5V19C21.9004 20.6016 20.6016 21.9004 19 21.9004H5C3.39837 21.9004 2.09961 20.6016 2.09961 19V5C2.09961 3.39837 3.39837 2.09961 5 2.09961H19ZM5 3.90039C4.39249 3.90039 3.90039 4.39249 3.90039 5V19C3.90039 19.6075 4.39249 20.0996 5 20.0996H14.0996V3.90039H5Z"
          fill="currentColor"
        />
        <path
          d="M8.53704 8.36334C8.18557 8.01186 7.61508 8.01186 7.2636 8.36334C6.91213 8.71481 6.91213 9.2853 7.2636 9.63677L9.62689 12.0001L7.2636 14.3633L7.20208 14.4317C6.91363 14.7852 6.93405 15.3072 7.2636 15.6368C7.59316 15.9663 8.11518 15.9867 8.46868 15.6983L8.53704 15.6368L11.537 12.6368C11.8885 12.2853 11.8885 11.7148 11.537 11.3633L8.53704 8.36334Z"
          fill="currentColor"
        />
      </g>
    </g>
  </svg>
);
export default PanelRightCloseIcon;
