import type { SVGProps } from 'react';
import * as React from 'react';

const RectangleStackIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/rectangle-stack">
      <g id="Icon">
        <path
          d="M6.15288 4.20275C6.20175 4.20118 6.25082 4.20039 6.30008 4.20039H17.7001C17.7493 4.20039 17.7984 4.20119 17.8473 4.20276C17.4773 3.15285 16.4766 2.40039 15.3001 2.40039H8.70008C7.5236 2.40039 6.52287 3.15285 6.15288 4.20275Z"
          fill="currentColor"
        />
        <path
          d="M1.19995 12.3004C1.19995 10.8092 2.40878 9.60039 3.89995 9.60039H20.1C21.5911 9.60039 22.8 10.8092 22.8 12.3004V18.9004C22.8 20.3916 21.5911 21.6004 20.1 21.6004H3.89995C2.40878 21.6004 1.19995 20.3916 1.19995 18.9004V12.3004Z"
          fill="currentColor"
        />
        <path
          d="M3.90008 7.80039C3.85082 7.80039 3.80175 7.80118 3.75288 7.80275C4.12287 6.75285 5.1236 6.00039 6.30008 6.00039H17.7001C18.8766 6.00039 19.8773 6.75285 20.2473 7.80276C20.1984 7.80119 20.1493 7.80039 20.1001 7.80039H3.90008Z"
          fill="currentColor"
        />
      </g>
    </g>
  </svg>
);
export default RectangleStackIcon;
