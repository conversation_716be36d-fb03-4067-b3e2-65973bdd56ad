import type { SVGProps } from 'react';
import * as React from 'react';

const PlayIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/play">
      <path
        id="Icon"
        d="M7.55999 3.40914C6.36137 2.65349 4.80005 3.51487 4.80005 4.9318V19.0692C4.80005 20.4861 6.36137 21.3475 7.55999 20.5918L18.7724 13.5232C19.8925 12.817 19.8925 11.184 18.7724 10.4778L7.55999 3.40914Z"
        fill="currentColor"
      />
    </g>
  </svg>
);
export default PlayIcon;
