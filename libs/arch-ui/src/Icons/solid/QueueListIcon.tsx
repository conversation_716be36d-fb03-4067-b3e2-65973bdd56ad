import type { SVGProps } from 'react';
import * as React from 'react';

const QueueListIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/queue-list">
      <g id="Icon">
        <path
          d="M2.40002 5.40039C2.40002 3.74354 3.74317 2.40039 5.40002 2.40039H18.6C20.2569 2.40039 21.6 3.74354 21.6 5.40039C21.6 7.05724 20.2569 8.40039 18.6 8.40039H5.40002C3.74317 8.40039 2.40002 7.05724 2.40002 5.40039Z"
          fill="currentColor"
        />
        <path
          d="M3.30002 10.9004C2.80297 10.9004 2.40002 11.3034 2.40002 11.8004C2.40002 12.2975 2.80297 12.7004 3.30002 12.7004H20.7C21.1971 12.7004 21.6 12.2975 21.6 11.8004C21.6 11.3034 21.1971 10.9004 20.7 10.9004H3.30002Z"
          fill="currentColor"
        />
        <path
          d="M3.30002 15.1964C2.80297 15.1964 2.40002 15.5993 2.40002 16.0964C2.40002 16.5934 2.80297 16.9964 3.30002 16.9964H20.7C21.1971 16.9964 21.6 16.5934 21.6 16.0964C21.6 15.5993 21.1971 15.1964 20.7 15.1964H3.30002Z"
          fill="currentColor"
        />
        <path
          d="M3.30002 19.5004C2.80297 19.5004 2.40002 19.9034 2.40002 20.4004C2.40002 20.8975 2.80297 21.3004 3.30002 21.3004H20.7C21.1971 21.3004 21.6 20.8975 21.6 20.4004C21.6 19.9034 21.1971 19.5004 20.7 19.5004H3.30002Z"
          fill="currentColor"
        />
      </g>
    </g>
  </svg>
);
export default QueueListIcon;
