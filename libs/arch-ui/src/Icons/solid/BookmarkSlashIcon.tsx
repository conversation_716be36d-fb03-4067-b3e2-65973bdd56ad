import type { SVGProps } from 'react';
import * as React from 'react';

const BookmarkSlashIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg width={24} height={24} viewBox="0 0 24 24" fill="none" aria-hidden="true" {...props}>
    <g id="solid/bookmark-slash">
      <g id="Icon">
        <path
          d="M20.4 5.41996V16.5816L6.52021 2.70184C8.31964 2.50239 10.148 2.40002 12 2.40002C14.0596 2.40002 16.0901 2.52663 18.084 2.77257C19.4331 2.93899 20.4 4.09651 20.4 5.41996Z"
          fill="currentColor"
        />
        <path
          d="M3.60002 20.7V7.4184L16.3845 20.2029L12 18.0983L4.88948 21.5114C4.61061 21.6453 4.28253 21.6265 4.02071 21.4618C3.75889 21.297 3.60002 21.0094 3.60002 20.7Z"
          fill="currentColor"
        />
        <path
          d="M3.93642 2.66363C3.58495 2.31216 3.0151 2.31216 2.66363 2.66363C2.31216 3.0151 2.31216 3.58495 2.66363 3.93642L20.0636 21.3364C20.4151 21.6879 20.985 21.6879 21.3364 21.3364C21.6879 20.9849 21.6879 20.4151 21.3364 20.0636L3.93642 2.66363Z"
          fill="currentColor"
        />
      </g>
    </g>
  </svg>
);
export default BookmarkSlashIcon;
