import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import createMatchMedia from 'tests/create-match-media';
import { CloudIcon } from '../../Icons/outline';
import { mediaQueryOptions } from '../../utils/breakpoints';
import Sidebar, { useSidebar } from '.';

const SidebarRootComponent = () => {
  const { toggleSidebar } = useSidebar();
  return (
    <>
      <Sidebar.Root />
      <button type="button" onClick={() => toggleSidebar()}>
        Toggle Sidebar
      </button>
    </>
  );
};

const renderComponent = (Component: React.ReactNode, props = {}) => {
  return render(<Sidebar.Provider {...props}>{Component}</Sidebar.Provider>);
};

describe('<Sidebar.Root />', () => {
  describe('when toggleSidebar is called', () => {
    it('triggers onOpenChange', async () => {
      const spyOnOpenChange = jest.fn();
      const props = { onOpenChange: spyOnOpenChange };
      renderComponent(<SidebarRootComponent />, props);

      userEvent.click(screen.getByRole('button', { name: 'Toggle Sidebar' }));

      expect(spyOnOpenChange).toBeCalled();
    });
  });

  describe('when on large screens', () => {
    beforeEach(() => {
      window.matchMedia = createMatchMedia(mediaQueryOptions.lg);
    });

    describe('when open is true', () => {
      it('renders Sidebar as expanded', async () => {
        const props = { open: true };
        renderComponent(<SidebarRootComponent />, props);

        expect(screen.queryByTestId('sidebar')).toBeInTheDocument();
        expect(screen.queryByTestId('sidebar')).toHaveAttribute('data-state', 'expanded');
      });
    });

    describe('when open is false', () => {
      it('renders Sidebar as collapsed', async () => {
        const props = { open: false };
        renderComponent(<SidebarRootComponent />, props);

        expect(screen.queryByTestId('sidebar')).toBeInTheDocument();
        expect(screen.queryByTestId('sidebar')).toHaveAttribute('data-state', 'collapsed');
      });
    });
  });

  describe('when on small screens', () => {
    beforeEach(() => {
      window.matchMedia = createMatchMedia(mediaQueryOptions.sm);
    });

    describe('when open is true', () => {
      it('renders Sidebar as a dialog', async () => {
        const props = { open: true };
        renderComponent(<SidebarRootComponent />, props);

        expect(await screen.findByRole('dialog')).toBeInTheDocument();
      });
    });

    describe('when open is false', () => {
      it('does not render Sidebar dialog', () => {
        const props = { open: false };
        renderComponent(<SidebarRootComponent />, props);

        expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
      });
    });
  });
});

describe('<Sidebar.Item />', () => {
  describe('when open is true', () => {
    it('renders with aria-expanded=true', () => {
      const props = { open: true };
      renderComponent(
        <Sidebar.Item>
          <Sidebar.Icon data-testid="sidebar-icon" icon={CloudIcon} />
          Item
        </Sidebar.Item>,
        props
      );

      expect(screen.getByRole('button', { name: /Item/ })).toBeVisible();
      expect(screen.getByRole('button', { name: /Item/ })).toHaveAttribute('aria-expanded', 'true');
      expect(screen.queryByTestId('sidebar-icon')).toBeVisible();
    });
  });

  describe('when open is false', () => {
    it('renders with aria-expanded=false', async () => {
      const props = { open: false };
      renderComponent(
        <Sidebar.Item>
          <Sidebar.Icon data-testid="sidebar-icon" icon={CloudIcon} />
          Item
        </Sidebar.Item>,
        props
      );

      expect(screen.getByRole('button', { name: /Item/ })).toHaveAttribute('aria-expanded', 'false');
      expect(screen.queryByTestId('sidebar-icon')).toBeVisible();
    });

    describe('when on large screen', () => {
      it('renders tooltip on hover', async () => {
        window.matchMedia = createMatchMedia(mediaQueryOptions.lg);
        const props = { open: false };
        renderComponent(
          <Sidebar.Item tooltip="Item Tooltip">
            <Sidebar.Icon data-testid="sidebar-icon" icon={CloudIcon} />
            Item
          </Sidebar.Item>,
          props
        );

        userEvent.hover(screen.getByRole('button', { name: /Item/ }));

        expect(await screen.findByRole('tooltip', { name: 'Item Tooltip' })).toBeInTheDocument();
      });
    });

    describe('when on small screen', () => {
      it('does not render tooltip on hover', async () => {
        window.matchMedia = createMatchMedia(mediaQueryOptions.sm);
        const props = { open: false };
        renderComponent(
          <Sidebar.Item tooltip="Item Tooltip">
            <Sidebar.Icon data-testid="sidebar-icon" icon={CloudIcon} />
            Item
          </Sidebar.Item>,
          props
        );

        userEvent.hover(screen.getByRole('button', { name: /Item/ }));

        expect(screen.queryByRole('tooltip', { name: 'Item Tooltip' })).not.toBeInTheDocument();
      });
    });
  });

  describe('when item is active', () => {
    it('adds *-selected classes to button', async () => {
      renderComponent(<Sidebar.Item active>Item</Sidebar.Item>, {});

      expect(screen.getByRole('button', { name: /Item/ })).toHaveClass('bg-selected! text-selected', { exact: false });
    });
  });
});

describe('<Sidebar.Badge />', () => {
  it('renders badge with label', async () => {
    renderComponent(<Sidebar.Badge label="Badge" />, {});

    expect(screen.getByRole('status', { name: 'Badge' })).toBeInTheDocument();
  });
});

describe('<Sidebar.Section />', () => {
  describe('when divider is true', () => {
    it('renders Divider', async () => {
      renderComponent(<Sidebar.Section divider />, {});

      expect(screen.getByRole('separator', { name: 'divider' })).toBeInTheDocument();
    });
  });
});
