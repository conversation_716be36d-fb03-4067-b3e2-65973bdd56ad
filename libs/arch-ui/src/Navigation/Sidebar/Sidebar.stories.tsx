import React, { type ComponentType, useState } from 'react';
import type { <PERSON>a, StoryFn, StoryObj } from '@storybook/react-vite';
import Button from '../../Button';
import {
  ChevronRightIcon,
  CloudIcon,
  Cog6ToothIcon,
  ExclamationCircleIcon,
  InboxIcon,
  UserIcon,
} from '../../Icons/outline';
import type { IconColor } from '../icon-config';
import Sidebar from '.';
import type { SidebarRootProps } from './Sidebar';
import { useSidebar } from './Sidebar.context';

type Story = StoryObj<SidebarRootProps>;
const meta: Meta<typeof Sidebar.Root> = {
  title: 'Navigation/Sidebar',
  component: Sidebar.Root,
  subcomponents: {
    'Sidebar.Badge': Sidebar.Badge as ComponentType<unknown>,
    'Sidebar.Header': Sidebar.Header as ComponentType<unknown>,
    'Sidebar.Content': Sidebar.Content as ComponentType<unknown>,
    'Sidebar.Section': Sidebar.Section as ComponentType<unknown>,
    'Sidebar.Footer': Sidebar.Footer as ComponentType<unknown>,
    'Sidebar.Item': Sidebar.Item as ComponentType<unknown>,
    'Sidebar.Icon': Sidebar.Icon as ComponentType<unknown>,
    'Sidebar.Submenu': Sidebar.Submenu as unknown as ComponentType<unknown>,
  },
  decorators: [
    (Story) => (
      <Sidebar.Provider>
        <Story />
      </Sidebar.Provider>
    ),
  ],
};
export default meta;

const items = [
  {
    key: 'active',
    title: 'Selected Item',
    image: UserIcon,
    active: true,
    color: 'primary',
  },
  {
    key: 'not-active',
    title: 'Not Selected Item',
    image: CloudIcon,
    color: 'success',
  },
  {
    key: 'badge',
    title: 'Badge Item',
    image: InboxIcon,
    badge: '5',
    color: 'warning',
  },
  {
    key: 'disabled-item',
    title: 'Disabled Item',
    image: ExclamationCircleIcon,
    disabled: true,
    color: 'secondary',
  },
];

const renderSidebarComponents = () => (
  <>
    <Sidebar.Header>
      <div className="p-1 text-sm">This is the Header</div>
    </Sidebar.Header>
    <Sidebar.Content>
      <Sidebar.Section divider>
        {items.map((item) => (
          <Sidebar.Item key={item.key} active={item.active} disabled={item.disabled} tooltip={item.title}>
            <Sidebar.Icon icon={item.image} color={item.color as IconColor} variant="contained" />
            {item.title}
            {item.badge && <Sidebar.Badge label={item.badge} />}
          </Sidebar.Item>
        ))}
      </Sidebar.Section>
      <Sidebar.Section divider>
        {items.map((item) => (
          <Sidebar.Item key={item.key} active={item.active} disabled={item.disabled}>
            <Sidebar.Icon icon={item.image} color={item.color as IconColor} variant="outlined" />
            <span>{item.title}</span>
            {item.badge && <Sidebar.Badge label={item.badge} />}
          </Sidebar.Item>
        ))}
      </Sidebar.Section>
      <Sidebar.Section>
        {items.map((item) => (
          <Sidebar.Item key={item.key} active={item.active} disabled={item.disabled}>
            <Sidebar.Icon icon={item.image} color={item.color as IconColor} />
            <span>{item.title}</span>
            {item.badge && <Sidebar.Badge label={item.badge} />}
          </Sidebar.Item>
        ))}
      </Sidebar.Section>
    </Sidebar.Content>
    <Sidebar.Footer>
      <div className="p-1 text-sm">This is the Footer</div>
    </Sidebar.Footer>
  </>
);

const Template: StoryFn<SidebarRootProps> = (props) => {
  const { toggleSidebar } = useSidebar();

  return (
    <>
      <Sidebar.Root {...props}>{renderSidebarComponents()}</Sidebar.Root>
      <div>
        <Button color="primary" variant="contained" size="md" onClick={() => toggleSidebar()}>
          Toggle Sidebar
        </Button>
      </div>
    </>
  );
};

export const Default: Story = {
  render: Template,
};

export const RightSidebar: Story = {
  render: Template,
  args: { side: 'right' },
};

export const Floating: Story = {
  render: Template,
  args: { variant: 'floating' },
};

export const Inset: Story = {
  render: Template,
  args: { variant: 'inset' },
};

const WithSubmenuTemplate: StoryFn<SidebarRootProps> = (props) => {
  const { toggleSidebar } = useSidebar();

  return (
    <>
      <Sidebar.Root {...props}>
        <Sidebar.Header>
          <div className="p-1 text-sm">This is the Header</div>
        </Sidebar.Header>
        <Sidebar.Content>
          <Sidebar.Section divider>
            {items.map((item) => (
              <Sidebar.Item key={item.key} active={item.active} disabled={item.disabled} tooltip={item.title}>
                <Sidebar.Icon icon={item.image} color={item.color as IconColor} variant="contained" />
                {item.title}
                {item.badge && <Sidebar.Badge label={item.badge} />}
              </Sidebar.Item>
            ))}
          </Sidebar.Section>
          <Sidebar.Submenu.Root>
            <Sidebar.Submenu.Trigger>
              <Sidebar.Item tooltip="Submenu">
                <Sidebar.Icon icon={Cog6ToothIcon} color="blue" variant="contained" />
                Submenu
                <ChevronRightIcon className="w-5 h-5 ml-auto text-icon-neutral-subtle" />
              </Sidebar.Item>
            </Sidebar.Submenu.Trigger>
            <Sidebar.Submenu.Content title="Submenu">
              <Sidebar.Section>
                {items.map((item) => (
                  <Sidebar.Item key={item.key} active={item.active} disabled={item.disabled}>
                    <Sidebar.Icon icon={item.image} color={item.color as IconColor} variant="contained" />
                    <span>{item.title}</span>
                    {item.badge && <Sidebar.Badge label={item.badge} />}
                  </Sidebar.Item>
                ))}
              </Sidebar.Section>
              <Sidebar.Section>
                {items.map((item) => (
                  <Sidebar.Item key={item.key} active={item.active} disabled={item.disabled}>
                    <Sidebar.Icon icon={item.image} color={item.color as IconColor} variant="outlined" />
                    <span>{item.title}</span>
                    {item.badge && <Sidebar.Badge label={item.badge} />}
                  </Sidebar.Item>
                ))}
              </Sidebar.Section>
            </Sidebar.Submenu.Content>
          </Sidebar.Submenu.Root>
          <Sidebar.Submenu.Root>
            <Sidebar.Submenu.Trigger>
              <Sidebar.Item tooltip="Submenu with Badge">
                <Sidebar.Icon icon={Cog6ToothIcon} color="blue" variant="outlined" />
                Submenu with Badge
                <Sidebar.Badge label="5" />
                <ChevronRightIcon className="w-5 h-5 text-icon-neutral-subtle" />
              </Sidebar.Item>
            </Sidebar.Submenu.Trigger>
            <Sidebar.Submenu.Content title="Submenu with Badge">
              <Sidebar.Section divider>
                {items.map((item) => (
                  <Sidebar.Item key={item.key} active={item.active} disabled={item.disabled} tooltip={item.title}>
                    <Sidebar.Icon icon={item.image} color={item.color as IconColor} />
                    {item.title}
                    {item.badge && <Sidebar.Badge label={item.badge} />}
                  </Sidebar.Item>
                ))}
              </Sidebar.Section>
            </Sidebar.Submenu.Content>
          </Sidebar.Submenu.Root>
          <Sidebar.Submenu.Root>
            <Sidebar.Submenu.Trigger>
              <Sidebar.Item disabled tooltip="Disabled Submenu">
                <Sidebar.Icon icon={Cog6ToothIcon} variant="contained" />
                Disabled Submenu
                <ChevronRightIcon className="w-5 h-5 ml-auto text-icon-neutral-subtle" />
              </Sidebar.Item>
            </Sidebar.Submenu.Trigger>
          </Sidebar.Submenu.Root>
        </Sidebar.Content>
        <Sidebar.Footer>
          <div className="p-1 text-sm">This is the Footer</div>
        </Sidebar.Footer>
      </Sidebar.Root>
      <div>
        <Button color="primary" variant="contained" size="md" onClick={() => toggleSidebar()}>
          Toggle Sidebar
        </Button>
      </div>
    </>
  );
};
export const WithSubmenu: Story = WithSubmenuTemplate.bind({});
export const RightSidebarWithSubmenu: Story = {
  render: WithSubmenuTemplate,
  args: { side: 'right' },
};

const ControlledTemplate: StoryFn<SidebarRootProps> = () => {
  const [open, setOpen] = useState(false);
  return (
    <Sidebar.Provider open={open} onOpenChange={(newOpen) => setOpen(newOpen)}>
      <div className="flex flex-row">
        <Sidebar.Root>{renderSidebarComponents()}</Sidebar.Root>
        <div>
          <div className="mb-1 text-sm">state {`{open: ${open}}`}</div>
          <Button color="primary" variant={open ? 'outlined' : 'contained'} size="md" onClick={() => setOpen(!open)}>
            {open ? 'Close Sidebar' : 'Open Sidebar'}
          </Button>
        </div>
      </div>
    </Sidebar.Provider>
  );
};
export const Controlled: Story = ControlledTemplate.bind({});

const allColorItems = [
  {
    key: 'primary',
    title: 'Primary',
    image: UserIcon,
    color: 'primary',
  },
  {
    key: 'secondary',
    title: 'Secondary',
    image: UserIcon,
    color: 'secondary',
  },
  {
    key: 'warning',
    title: 'Warning',
    image: UserIcon,
    color: 'warning',
  },
  {
    key: 'success',
    title: 'Success',
    image: UserIcon,
    color: 'success',
  },
  {
    key: 'danger',
    title: 'Danger',
    image: UserIcon,
    color: 'danger',
  },
  {
    key: 'pink',
    title: 'Pink',
    image: UserIcon,
    color: 'pink',
  },
  {
    key: 'blue',
    title: 'Blue',
    image: UserIcon,
    color: 'blue',
  },
  {
    key: 'sky',
    title: 'Sky',
    image: UserIcon,
    color: 'sky',
  },
];
const AllColorsTemplate: StoryFn<SidebarRootProps> = (props) => {
  const { toggleSidebar } = useSidebar();

  return (
    <>
      <Sidebar.Root {...props}>
        <Sidebar.Header>
          <div className="p-1 text-sm">This is the Header</div>
        </Sidebar.Header>
        <Sidebar.Content>
          <Sidebar.Section divider>
            {allColorItems.map((item) => (
              <Sidebar.Item key={item.key} tooltip={item.title}>
                <Sidebar.Icon icon={item.image} color={item.color as IconColor} variant="contained" />
                {item.title}
              </Sidebar.Item>
            ))}
          </Sidebar.Section>
          <Sidebar.Section divider>
            {allColorItems.map((item) => (
              <Sidebar.Item key={item.key} tooltip={item.title}>
                <Sidebar.Icon icon={item.image} color={item.color as IconColor} variant="outlined" />
                {item.title}
              </Sidebar.Item>
            ))}
          </Sidebar.Section>
          <Sidebar.Section divider>
            {allColorItems.map((item) => (
              <Sidebar.Item key={item.key} tooltip={item.title}>
                <Sidebar.Icon icon={item.image} color={item.color as IconColor} />
                {item.title}
              </Sidebar.Item>
            ))}
          </Sidebar.Section>
        </Sidebar.Content>
        <Sidebar.Footer>
          <div className="p-1 text-sm">This is the Footer</div>
        </Sidebar.Footer>
      </Sidebar.Root>
      <div>
        <Button color="primary" variant="contained" size="md" onClick={() => toggleSidebar()}>
          Toggle Sidebar
        </Button>
      </div>
    </>
  );
};
export const AllColors: Story = AllColorsTemplate.bind({});
