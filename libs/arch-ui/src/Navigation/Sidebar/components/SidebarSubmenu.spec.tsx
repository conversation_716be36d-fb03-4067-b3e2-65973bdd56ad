import React from 'react';
import { render, screen, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import Sidebar from '../';
import { SidebarSubmenu, type SidebarSubmenuRootProps } from './SidebarSubmenu';
import '@testing-library/jest-dom';

const renderComponent = ({ defaultOpen, divider }: Partial<SidebarSubmenuRootProps>) =>
  render(
    <Sidebar.Provider>
      <Sidebar.Root>
        <Sidebar.Content>
          <SidebarSubmenu.Root defaultOpen={defaultOpen} divider={divider}>
            <SidebarSubmenu.Trigger>
              <button type="button">Click me</button>
            </SidebarSubmenu.Trigger>
            <SidebarSubmenu.Content title="Submenu title">Content</SidebarSubmenu.Content>
          </SidebarSubmenu.Root>
        </Sidebar.Content>
      </Sidebar.Root>
    </Sidebar.Provider>
  );

describe('SidebarSubmenu', () => {
  it('renders the submenu trigger only', () => {
    renderComponent({});

    expect(screen.getByRole('button', { name: 'Click me' })).toBeInTheDocument();
    expect(screen.queryByRole('menu', { name: 'Submenu title' })).not.toBeInTheDocument();
  });

  describe('when submenu trigger is clicked', () => {
    it('opens the submenu with the content', async () => {
      renderComponent({});

      await userEvent.click(screen.getByRole('button', { name: 'Click me' }));

      const submenu = await screen.findByRole('menu', { name: 'Submenu title' });
      expect(submenu).toBeInTheDocument();
      expect(submenu).toHaveAttribute('data-state', 'open');
      expect(within(submenu).getByText('Content')).toBeInTheDocument();
    });
  });

  describe('when defaultOpen prop is true', () => {
    it('opens submenu by default', async () => {
      renderComponent({ defaultOpen: true });

      const submenu = await screen.findByRole('menu', { name: 'Submenu title' });
      expect(submenu).toBeInTheDocument();
      expect(submenu).toHaveAttribute('data-state', 'open');
      expect(within(submenu).getByText('Content')).toBeInTheDocument();
    });
  });

  describe('when divider prop is true', () => {
    it('renders Divider', async () => {
      renderComponent({ divider: true });

      expect(screen.getByRole('separator', { name: 'divider' })).toBeInTheDocument();
    });
  });

  describe('when Sidebar is expanded', () => {
    it('renders the submenu content inside the main sidebar content', async () => {
      render(
        <Sidebar.Provider open>
          <Sidebar.Root>
            <Sidebar.Content>
              <SidebarSubmenu.Root defaultOpen>
                <SidebarSubmenu.Trigger>Click me</SidebarSubmenu.Trigger>
                <SidebarSubmenu.Content title="Submenu title">Content</SidebarSubmenu.Content>
              </SidebarSubmenu.Root>
            </Sidebar.Content>
          </Sidebar.Root>
        </Sidebar.Provider>
      );
      const portalContainer = document.querySelector('div[data-sidebar=content]') as HTMLDivElement;

      const submenu = await within(portalContainer).findByRole('menu', { name: 'Submenu title' });
      expect(submenu).toBeInTheDocument();
      expect(within(submenu).getByText('Content')).toBeInTheDocument();
    });
  });

  describe('when Sidebar is collapsed', () => {
    it('renders the submenu content inside submenu-container of the sidebar', async () => {
      render(
        <Sidebar.Provider open={false}>
          <Sidebar.Root>
            <Sidebar.Content>
              <SidebarSubmenu.Root defaultOpen>
                <SidebarSubmenu.Trigger>Click me</SidebarSubmenu.Trigger>
                <SidebarSubmenu.Content title="Submenu title">Content</SidebarSubmenu.Content>
              </SidebarSubmenu.Root>
            </Sidebar.Content>
          </Sidebar.Root>
        </Sidebar.Provider>
      );
      const portalContainer = document.querySelector('div[data-sidebar=submenu-container]') as HTMLDivElement;

      const submenu = await within(portalContainer).findByRole('menu', { name: 'Submenu title' });
      expect(submenu).toBeInTheDocument();
      expect(within(submenu).getByText('Content')).toBeInTheDocument();
    });
  });
});
