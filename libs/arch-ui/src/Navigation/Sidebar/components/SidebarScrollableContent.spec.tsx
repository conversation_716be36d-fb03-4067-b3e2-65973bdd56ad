import React from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import SidebarScrollableContent from './SidebarScrollableContent';

const scrollMock = jest.fn();

const renderComponents = () =>
  render(
    <SidebarScrollableContent.Root>
      <SidebarScrollableContent.ScrollUpButton />
      <SidebarScrollableContent.ScrollDownButton />
    </SidebarScrollableContent.Root>
  );

describe('SidebarScrollableContent', () => {
  describe('when scrollHeight is greater than clientHeight', () => {
    beforeEach(() => {
      Object.defineProperties(HTMLElement.prototype, {
        clientHeight: { value: 100, configurable: true },
        scrollHeight: { value: 500, configurable: true },
        scroll: { value: scrollMock },
      });
    });

    it('shows sidebar bottom overflow button', async () => {
      renderComponents();

      expect(await screen.findByRole('button', { name: 'sidebar overflow bottom' })).toBeInTheDocument();
    });

    describe('when bottom overflow is clicked', () => {
      it('scrolls to the bottom', async () => {
        renderComponents();

        await userEvent.click(await screen.findByRole('button', { name: 'sidebar overflow bottom' }));

        expect(scrollMock).toHaveBeenCalledWith({ top: 500, behavior: 'smooth' });
      });
    });

    describe('when container is scrolled down', () => {
      it('shows sidebar top overflow button', async () => {
        renderComponents();

        await fireEvent.scroll(await screen.findByTestId('sidebar-scrollable-content'), { target: { scrollTop: 100 } });

        expect(screen.getByRole('button', { name: 'sidebar overflow top' })).toBeInTheDocument();
      });

      describe('when top overflow is clicked', () => {
        it('scrolls to the top', async () => {
          renderComponents();

          await fireEvent.scroll(await screen.findByTestId('sidebar-scrollable-content'), {
            target: { scrollTop: 100 },
          });
          await userEvent.click(screen.getByRole('button', { name: 'sidebar overflow top' }));

          expect(scrollMock).toHaveBeenCalledWith({ top: 0, behavior: 'smooth' });
        });
      });
    });
  });

  describe('when there are no overflows', () => {
    it('hides overflow buttons', () => {
      Object.defineProperties(HTMLElement.prototype, {
        clientHeight: { value: 100, configurable: true },
        scrollHeight: { value: 0, configurable: true },
      });
      renderComponents();

      expect(screen.queryByRole('button', { name: 'sidebar overflow bottom' })).not.toBeInTheDocument();
      expect(screen.queryByRole('button', { name: 'sidebar overflow top' })).not.toBeInTheDocument();
    });
  });
});
