import React from 'react';
import Badge from '../../Badge';
import { SHAPE, SIZE, THEME } from '../../Badge/Badge.types';
import { cn } from '../../utils/classes';
import { getIconClasses, type IconColor, type IconVariant } from '../icon-config';

export const BottomNavigationBadge = ({ label }: React.ComponentProps<typeof Badge>) => {
  return (
    <span className="absolute top-1 left-1/2 [&>span]:rounded-full [&>span]:border [&>span]:border-white">
      <Badge label={label} shape={SHAPE.BASIC} size={SIZE.EXTRA_SMALL} theme={THEME.RED} />
    </span>
  );
};

export type BottomNavigationIconProps = {
  icon: React.ElementType;
  color?: IconColor;
  variant?: IconVariant;
};
export const BottomNavigationIcon = ({
  icon: Icon,
  color = 'secondary',
  variant = 'text',
  className,
  ...props
}: Omit<React.ComponentProps<'div'>, 'children'> & BottomNavigationIconProps) => {
  const classes = getIconClasses({ color, variant });
  return (
    <div className={cn('size-6 grid items-center justify-center shrink-0', classes, className)} {...props}>
      {<Icon />}
    </div>
  );
};

export type BottomNavigationItemProps = { active?: boolean; disabled?: boolean } & React.ComponentProps<'button'>;
export const BottomNavigationItem = ({
  active,
  disabled,
  className,
  children,
  ...props
}: BottomNavigationItemProps) => {
  return (
    <button
      data-active={active}
      data-disabled={disabled}
      className={cn(
        'group relative max-w-fit flex flex-1 flex-col items-center justify-center gap-2 font-medium text-center text-nowrap text-neutral-subtlest text-xs leading-none p-0.5',
        {
          'text-brand-subtle after:w-10 after:absolute after:top-0 after:content-[""] after:border-t-[3px] after:border-selected':
            active,
          'pointer-events-none opacity-50': disabled,
        },
        className
      )}
      {...props}
    >
      {children}
    </button>
  );
};

export const BottomNavigationRoot = ({ className, children, ...props }: React.ComponentProps<'nav'>) => {
  return (
    <nav
      className={cn(
        'h-20 sticky inset-x-0 bottom-0 z-navigation bg-surface-navigation border-t border-neutral-subtlest flex justify-evenly overflow-x-auto',
        className
      )}
      {...props}
    >
      {children}
    </nav>
  );
};

BottomNavigationBadge.displayName = 'BottomNavigation.Badge';
BottomNavigationIcon.displayName = 'BottomNavigation.Icon';
BottomNavigationItem.displayName = 'BottomNavigation.Item';
BottomNavigationRoot.displayName = 'BottomNavigation.Root';
