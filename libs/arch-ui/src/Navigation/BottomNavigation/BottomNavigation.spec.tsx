import React from 'react';
import { render, screen, within } from '@testing-library/react';
import { CloudIcon } from '../../Icons/outline';
import BottomNavigation from '.';

describe('BottomNavigation', () => {
  it('renders items correctly', () => {
    render(
      <BottomNavigation.Root>
        <BottomNavigation.Item>
          <BottomNavigation.Icon data-testid="bottom-navigation-icon" icon={CloudIcon} />
          Item 1
          <BottomNavigation.Badge label="badge" />
        </BottomNavigation.Item>
        <BottomNavigation.Item>Item 2</BottomNavigation.Item>
      </BottomNavigation.Root>
    );

    const nav = screen.getByRole('navigation');
    expect(nav).toBeInTheDocument();
    expect(within(nav).getByRole('button', { name: /Item 1/ })).toBeInTheDocument();
    expect(within(nav).getByRole('button', { name: /Item 2/ })).toBeInTheDocument();
    expect(within(nav).getByText('badge')).toBeInTheDocument();
    expect(within(nav).queryByTestId('bottom-navigation-icon')).toBeVisible();
  });
});
