import React, { type ComponentType } from 'react';
import type { Meta, StoryFn, StoryObj } from '@storybook/react';
import { CloudIcon, ExclamationCircleIcon, InboxIcon, UserIcon } from '../../Icons/outline';
import type { IconColor } from '../icon-config';
import BottomNavigation from '.';

type Story = StoryObj<typeof BottomNavigation.Root>;
const meta: Meta<typeof BottomNavigation.Root> = {
  component: BottomNavigation.Root,
  subcomponents: {
    'BottomNavigation.Badge': BottomNavigation.Badge as ComponentType<unknown>,
    'BottomNavigation.Icon': BottomNavigation.Icon as ComponentType<unknown>,
    'BottomNavigation.Item': BottomNavigation.Item as ComponentType<unknown>,
    'BottomNavigation.Root': BottomNavigation.Root as ComponentType<unknown>,
  },
  title: 'Navigation/BottomNavigation',
};
export default meta;

const items = [
  {
    key: 'active',
    title: 'Selected Item',
    image: UserIcon,
    active: true,
    color: 'primary',
  },
  {
    key: 'not-active',
    title: 'Not Selected Item',
    image: CloudIcon,
    color: 'success',
  },
  {
    key: 'badge',
    title: 'Badge Item',
    image: InboxIcon,
    badge: '5',
    color: 'warning',
  },
  {
    key: 'disabled-item',
    title: 'Disabled Item',
    image: ExclamationCircleIcon,
    disabled: true,
    color: 'secondary',
  },
];

const Template: StoryFn<typeof BottomNavigation.Root> = (props) => (
  <div className="flex flex-col gap-2">
    <BottomNavigation.Root>
      {items.map((item) => (
        <BottomNavigation.Item key={item.key} active={item.active} disabled={item.disabled}>
          <BottomNavigation.Icon icon={item.image} color={item.color as IconColor} variant="contained" />
          {item.title}
          {item.badge && <BottomNavigation.Badge label={item.badge} />}
        </BottomNavigation.Item>
      ))}
    </BottomNavigation.Root>
    <BottomNavigation.Root>
      {items.map((item) => (
        <BottomNavigation.Item key={item.key} active={item.active} disabled={item.disabled}>
          <BottomNavigation.Icon icon={item.image} color={item.color as IconColor} variant="outlined" />
          {item.title}
          {item.badge && <BottomNavigation.Badge label={item.badge} />}
        </BottomNavigation.Item>
      ))}
    </BottomNavigation.Root>
    <BottomNavigation.Root>
      {items.map((item) => (
        <BottomNavigation.Item key={item.key} active={item.active} disabled={item.disabled}>
          <BottomNavigation.Icon icon={item.image} color={item.color as IconColor} variant="text" />
          {item.title}
          {item.badge && <BottomNavigation.Badge label={item.badge} />}
        </BottomNavigation.Item>
      ))}
    </BottomNavigation.Root>
  </div>
);

export const Default: Story = { render: Template };

const allColorItems = [
  {
    key: 'primary',
    title: 'Primary',
    image: UserIcon,
    color: 'primary',
  },
  {
    key: 'secondary',
    title: 'Secondary',
    image: UserIcon,
    color: 'secondary',
  },
  {
    key: 'warning',
    title: 'Warning',
    image: UserIcon,
    color: 'warning',
  },
  {
    key: 'success',
    title: 'Success',
    image: UserIcon,
    color: 'success',
  },
  {
    key: 'danger',
    title: 'Danger',
    image: UserIcon,
    color: 'danger',
  },
  {
    key: 'pink',
    title: 'Pink',
    image: UserIcon,
    color: 'pink',
  },
  {
    key: 'blue',
    title: 'Blue',
    image: UserIcon,
    color: 'blue',
  },
  {
    key: 'sky',
    title: 'Sky',
    image: UserIcon,
    color: 'sky',
  },
];
const AllColorsTemplate: StoryFn<typeof BottomNavigation.Root> = (props) => {
  return (
    <div className="flex flex-col gap-2">
      <BottomNavigation.Root>
        {allColorItems.map((item) => (
          <BottomNavigation.Item key={item.key}>
            <BottomNavigation.Icon icon={item.image} color={item.color as IconColor} variant="contained" />
            {item.title}
          </BottomNavigation.Item>
        ))}
      </BottomNavigation.Root>
      <BottomNavigation.Root>
        {allColorItems.map((item) => (
          <BottomNavigation.Item key={item.key}>
            <BottomNavigation.Icon icon={item.image} color={item.color as IconColor} variant="outlined" />
            {item.title}
          </BottomNavigation.Item>
        ))}
      </BottomNavigation.Root>
      <BottomNavigation.Root>
        {allColorItems.map((item) => (
          <BottomNavigation.Item key={item.key}>
            <BottomNavigation.Icon icon={item.image} color={item.color as IconColor} variant="text" />
            {item.title}
          </BottomNavigation.Item>
        ))}
      </BottomNavigation.Root>
    </div>
  );
};
export const AllColors: Story = AllColorsTemplate.bind({});
