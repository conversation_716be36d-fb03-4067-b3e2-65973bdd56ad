import React from 'react';
import type { Meta } from '@storybook/react-vite';
import { InputText } from './InputText';

export default {
  title: 'Input/InputText',
  component: InputText,
} as Meta<typeof InputText>;

export const Standard = {
  args: {
    label: 'Example',
    name: 'example',
    id: 'example',
    required: true,
  },
};

export const WithValue = {
  args: {
    ...Standard.args,
    value: 'This is my value!',
  },
};

export const WithError = {
  args: {
    ...Standard.args,
    error: 'This field has an error message!',
  },
};

export const Disabled = {
  args: {
    ...Standard.args,
    value: 'This is my value!',
    disabled: true,
  },
};

export const WithCornerAdornment = {
  args: {
    ...Standard.args,
    value: 'This is my value!',
    cornerAdornment: <>Corner Adornment</>,
  },
};

export const WithCornerAdornmentAndNoLabel = {
  args: {
    ...Standard.args,
    value: 'This is my value!',
    cornerAdornment: <>Corner Adornment</>,
  },
};

export const WithFullWidth = {
  args: {
    ...Standard.args,
    fullWidth: true,
  },
};
