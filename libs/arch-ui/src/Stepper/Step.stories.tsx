import React from 'react';
import type { Meta, StoryFn } from '@storybook/react-vite';
import { Step, type StepProps } from './Step';
import { Steps } from './Steps';

export default {
  component: Step,
  title: 'Status/Stepper/Step',
} as Meta<StepProps>;

const Template: StoryFn<StepProps> = ({ ...args }) => (
  <Steps>
    <Step {...args} />
  </Steps>
);

export const Default = {
  render: Template,
};

export const Current = {
  render: Template,

  args: {
    variant: 'current',
  },
};

export const Complete = {
  render: Template,

  args: {
    variant: 'complete',
  },
};
