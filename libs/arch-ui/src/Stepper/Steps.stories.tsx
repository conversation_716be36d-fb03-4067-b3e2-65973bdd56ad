import React from 'react';
import type { Meta } from '@storybook/react-vite';
import { Step } from './Step';
import { Steps, type StepsProps } from './Steps';

export default {
  component: Steps,
  title: 'Status/Stepper/Steps',
} as Meta<StepsProps>;

export const Default = {
  args: {
    children: [<Step variant="complete" key={0} />, <Step variant="current" key={1} />, <Step key={2} />],
  },
};
