import React from 'react';
import type { <PERSON>a, StoryObj } from '@storybook/react-vite';
import { Default as LogIn } from '../LogIn/LogIn.stories';
import { SplitScreen, type SplitScreenProps } from './SplitScreen';

type Story = StoryObj<typeof SplitScreen>;

const meta: Meta<typeof SplitScreen> = {
  title: 'Screens/SplitScreen',
  component: SplitScreen,
  subcomponents: {
    LeftPanel: SplitScreen.LeftPanel,
    RightPanel: SplitScreen.RightPanel,
  },
  parameters: {
    layout: 'fullscreen',
  },
};
export default meta;

const Template = (args: SplitScreenProps) => (
  <SplitScreen {...args}>
    <SplitScreen.LeftPanel>Example Content</SplitScreen.LeftPanel>
    <SplitScreen.RightPanel>
      <img
        className="absolute inset-0 h-full w-full object-cover"
        src="https://via.placeholder.com/150"
        alt="title of the example"
      />
    </SplitScreen.RightPanel>
  </SplitScreen>
);

export const Standard: Story = {
  render: Template,
  args: {},
};

const ref = React.createRef<HTMLInputElement>();
const LoginProps = {
  extraProps: { email: { name: 'email', ref }, password: { name: 'password', ref } },
  submitForm: () => {},
  disableSubmitButton: false,
  forgotPasswordLink: <a href="/forgot-password">Forgot password</a>,
  notYouLink: <a href="/auth">Not you?</a>,
  title: 'Log in',
  emailLabel: 'Email',
  passwordLabel: 'Password',
  submitCTA: 'Log in',
};

export const WithLogIn: Story = {
  render: () => (
    <SplitScreen>
      <SplitScreen.LeftPanel>{LogIn.render({ ...LoginProps })}</SplitScreen.LeftPanel>
      <SplitScreen.RightPanel className="bg-indigo-500 lg:flex">
        <img
          className="absolute h-full w-full object-none object-center"
          src="https://via.placeholder.com/150"
          alt="title of the example"
        />
      </SplitScreen.RightPanel>
    </SplitScreen>
  ),
};
