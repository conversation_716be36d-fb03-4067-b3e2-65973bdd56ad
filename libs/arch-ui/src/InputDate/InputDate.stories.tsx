import React from 'react';
import type { Meta } from '@storybook/react-vite';
import { InputDate } from './InputDate';

export default {
  title: 'Input/InputDate',
  component: InputDate,
} as Meta<typeof InputDate>;

export const Default = {
  args: {
    label: 'Date',
    name: 'date',
  },
};

export const WithValue = {
  args: { ...Default.args, value: '2020-09-29' },
};

export const WithErrors = {
  args: { ...Default.args, error: 'error' },
};

export const Disabled = {
  args: { ...Default.args, disabled: true },
};

export const WithCornerAdornment = {
  args: { ...Default.args, cornerAdornment: <>Corner Adornment</> },
};

export const WithFullWidth = {
  args: {
    ...Default.args,
    fullWidth: true,
  },
};
