import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import type { Toast as HotToastType } from 'react-hot-toast';
import { Toast } from './Toast';

describe('Toast', () => {
  it('renders a toast', () => {
    const toast: HotToastType = {
      type: 'custom',
      duration: 6000,
      createdAt: 1687273966218,
      visible: true,
      ariaProps: {
        role: 'status',
        'aria-live': 'polite',
      },
      pauseDuration: 0,
      id: '1',
      style: {},
      message: '',
    };
    render(<Toast color="success" message="This is a toast" toast={toast} />);

    expect(screen.getByText('This is a toast')).toBeInTheDocument();
  });

  it('renders Toast actions', () => {
    const toast: HotToastType = {
      type: 'custom',
      duration: 6000,
      createdAt: 1687273966218,
      visible: true,
      ariaProps: {
        role: 'status',
        'aria-live': 'polite',
      },
      pauseDuration: 0,
      id: '1',
      style: {},
      message: '',
    };
    render(
      <Toast color="success" message="This is a toast" toast={toast} Actions={<button type="button">Button</button>} />
    );

    expect(screen.getByRole('button', { name: 'Button' })).toBeInTheDocument();
  });

  it('renders the close button and invokes onClose on click', async () => {
    const toast: HotToastType = {
      type: 'custom',
      duration: 6000,
      createdAt: 1687273966218,
      visible: true,
      ariaProps: {
        role: 'status',
        'aria-live': 'polite',
      },
      pauseDuration: 0,
      id: '1',
      style: {},
      message: '',
    };
    const spyOnClose = jest.fn();
    render(<Toast color="success" message="This is a toast" toast={toast} onClose={spyOnClose} />);

    await userEvent.click(screen.getByLabelText('close toast'));

    expect(spyOnClose).toBeCalled();
  });
});
