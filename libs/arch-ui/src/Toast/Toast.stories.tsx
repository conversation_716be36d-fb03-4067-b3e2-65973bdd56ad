import React from 'react';
import type { Meta } from '@storybook/react-vite';
import { toast } from 'react-hot-toast';
import { Button } from '../Button/Button';
import { colors, Toast, type ToastProps } from './Toast';
import { Toaster } from './Toaster';

export default {
  title: 'Feedback/Toast',
  component: Toast,
} as Meta<ToastProps>;

export const Default = () => {
  const notify = () => toast.custom((t) => <Toast message="Toast message goes here" color="primary" toast={t} />);

  return (
    <>
      <Button color="primary" variant="contained" size="md" onClick={notify}>
        Make me a toast
      </Button>
      <Toaster />
    </>
  );
};

export const Colors = () => {
  const notify = () => {
    colors.map((color) => toast.custom((t) => <Toast message="Toast message goes here" color={color} toast={t} />));
  };

  return (
    <>
      <Button color="primary" variant="contained" size="md" onClick={notify}>
        Make me a toast
      </Button>
      <Toaster />
    </>
  );
};

export const WithoutIcon = () => {
  const notify = () =>
    toast.custom((t) => <Toast message="Toast message goes here" color="primary" toast={t} hasIcon={false} />);

  return (
    <>
      <Button color="primary" variant="contained" size="md" onClick={notify}>
        Make me a toast
      </Button>
      <Toaster />
    </>
  );
};

export const WithActions = () => {
  const notify = () =>
    toast.custom((t) => (
      <Toast
        message="Toast message goes here"
        color="primary"
        Actions={
          <Button variant="outlined" size="xs" color="white">
            Button text
          </Button>
        }
        toast={t}
      />
    ));

  return (
    <>
      <Button color="primary" variant="contained" size="md" onClick={notify}>
        Make me a toast
      </Button>
      <Toaster />
    </>
  );
};

export const Dismissable = () => {
  const notify = () =>
    toast.custom((t) => (
      <Toast message="Toast message goes here" color="primary" onClose={() => toast.dismiss(t.id)} toast={t} />
    ));

  return (
    <>
      <Button color="primary" variant="contained" size="md" onClick={notify}>
        Make me a toast
      </Button>
      <Toaster />
    </>
  );
};

export const DismissableWithActions = () => {
  const notify = () =>
    toast.custom((t) => (
      <Toast
        message="Toast message goes here"
        color="primary"
        Actions={
          <Button variant="outlined" size="xs" color="white">
            Button text
          </Button>
        }
        onClose={() => toast.dismiss(t.id)}
        toast={t}
      />
    ));

  return (
    <>
      <Button color="primary" variant="contained" size="md" onClick={notify}>
        Make me a toast
      </Button>
      <Toaster />
    </>
  );
};

export const WithLongMessage = () => {
  const notify = () =>
    toast.custom((t) => (
      <Toast
        message="Toast message that overflows its vertical container"
        color="primary"
        Actions={
          <Button variant="outlined" size="xs" color="white">
            Button text
          </Button>
        }
        onClose={() => toast.dismiss(t.id)}
        alignContent="start"
        toast={t}
      />
    ));

  return (
    <>
      <Button color="primary" variant="contained" size="md" onClick={notify}>
        Make me a toast
      </Button>
      <Toaster />
    </>
  );
};
