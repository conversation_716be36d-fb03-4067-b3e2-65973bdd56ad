import React, { useState } from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { SearchField } from './components/SearchField';
import { SearchOption } from './components/SearchOption';
import { SearchOptions } from './components/SearchOptions';
import { SearchRoot } from './SearchRoot';

const people = [
  { id: 1, name: '<PERSON><PERSON><PERSON>' },
  { id: 2, name: 'Kenton <PERSON>' },
  { id: 3, name: '<PERSON><PERSON>' },
  { id: 4, name: '<PERSON>' },
  { id: 5, name: '<PERSON><PERSON>' },
];
type People = (typeof people)[number];

const SearchPeople = () => {
  const [query, setQuery] = useState('');
  const [selectedPerson, setSelectedPerson] = useState('');
  const filteredPeople =
    query === ''
      ? people
      : people.filter((person) => {
          return person.name.toLowerCase().includes(query.toLowerCase());
        });

  return (
    <SearchRoot value={selectedPerson} onChange={setSelectedPerson}>
      <SearchField
        placeholder="Search people"
        onChange={(search) => setQuery(search)}
        displayValue={(person: People) => person.name}
      />
      <SearchOptions>
        {filteredPeople.map((person) => (
          <SearchOption key={person.id} value={person}>
            {person.name}
          </SearchOption>
        ))}
      </SearchOptions>
    </SearchRoot>
  );
};

describe('Search', () => {
  it('renders search field with no option', async () => {
    render(<SearchPeople />);

    expect(screen.getByRole('combobox')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Search people')).toBeInTheDocument();
  });

  it('renders search field with filtered results', async () => {
    render(<SearchPeople />);

    expect(screen.getByPlaceholderText('Search people')).toBeInTheDocument();

    await userEvent.type(screen.getByPlaceholderText('Search people'), 'Durward');

    await waitFor(() => expect(screen.getByRole('option', { name: 'Durward Reynolds' })).toBeInTheDocument());
    await waitFor(() => expect(screen.queryByRole('option', { name: 'Katelyn Rohan' })).not.toBeInTheDocument());
  });

  describe('when there is an error', () => {
    it('renders the error icon', () => {
      render(
        <SearchRoot>
          <SearchField placeholder="Search people" error="Invalid option" onChange={jest.fn()} />
        </SearchRoot>
      );

      expect(screen.getByRole('button', { name: 'search-field-error-icon-trigger' })).toBeInTheDocument();
    });

    describe('when the error icon is hovered', () => {
      it('displays the error message', async () => {
        render(
          <SearchRoot>
            <SearchField placeholder="Search people" error="Invalid option" onChange={jest.fn()} />
          </SearchRoot>
        );

        await userEvent.hover(screen.getByRole('button', { name: 'search-field-error-icon-trigger' }));

        expect(await screen.findByRole('tooltip', { name: 'Invalid option' })).toBeInTheDocument();
      });
    });
  });

  describe('when there is a selected value', () => {
    describe('when clear callback is provided', () => {
      it('renders the clear button', () => {
        const selectedValue = people[0];
        render(
          <SearchRoot value={selectedValue} onChange={jest.fn()}>
            <SearchField
              placeholder="Search people"
              selectedValue={selectedValue}
              displayValue={(person: People) => person.name}
              onChange={jest.fn()}
              onClear={jest.fn()}
            />
            <SearchOptions>
              {people.map((person) => (
                <SearchOption key={person.id} value={person}>
                  {person.name}
                </SearchOption>
              ))}
            </SearchOptions>
          </SearchRoot>
        );

        expect(screen.getByRole('button', { name: 'Clear' })).toBeInTheDocument();
      });
    });

    describe('when delete callback is provided', () => {
      it('renders the delete button', () => {
        const selectedValue = people[0];
        render(
          <SearchRoot value={selectedValue} onChange={jest.fn()}>
            <SearchField
              placeholder="Search people"
              selectedValue={selectedValue}
              displayValue={(person: People) => person.name}
              onChange={jest.fn()}
              onDelete={jest.fn()}
            />
            <SearchOptions>
              {people.map((person) => (
                <SearchOption key={person.id} value={person}>
                  {person.name}
                </SearchOption>
              ))}
            </SearchOptions>
          </SearchRoot>
        );

        expect(screen.getByRole('button', { name: 'Delete' })).toBeInTheDocument();
      });
    });

    describe('when clear and delete callbacks are provided', () => {
      it('renders the clear and delete buttons', () => {
        const selectedValue = people[0];
        render(
          <SearchRoot value={selectedValue} onChange={jest.fn()}>
            <SearchField
              placeholder="Search people"
              selectedValue={selectedValue}
              displayValue={(person: People) => person.name}
              onChange={jest.fn()}
              onClear={jest.fn()}
              onDelete={jest.fn()}
            />
            <SearchOptions>
              {people.map((person) => (
                <SearchOption key={person.id} value={person}>
                  {person.name}
                </SearchOption>
              ))}
            </SearchOptions>
          </SearchRoot>
        );

        expect(screen.getByRole('button', { name: 'Clear' })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: 'Delete' })).toBeInTheDocument();
      });
    });
  });
});
