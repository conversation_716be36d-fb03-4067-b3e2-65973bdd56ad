import React from 'react';
import { Combobox, type ComboboxOptionProps } from '@headlessui/react';
import classNames from 'clsx';
import { twMerge } from 'tailwind-merge';
import type { PrimitiveProps } from '../../utils/render';

export type SearchOptionProps<TValue> = PrimitiveProps<ComboboxOptionProps<'li', TValue>>;

export function SearchOption<TValue>({ className, ...props }: SearchOptionProps<TValue>) {
  return (
    <Combobox.Option
      className={twMerge(
        classNames(
          'bg-white cursor-pointer w-full px-4 py-1 gap-2.5 flex justify-start items-center',
          'hover:bg-indigo-50 aria-selected:bg-indigo-100 focus-visible:outline-hidden aria-disabled:opacity-60 aria-disabled:cursor-not-allowed'
        ),
        className
      )}
      {...props}
    />
  );
}
SearchOption.displayName = 'Search.Option';
