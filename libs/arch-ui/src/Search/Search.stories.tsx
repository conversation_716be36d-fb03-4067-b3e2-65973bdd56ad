import React, { useState } from 'react';
import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite';
import type { Variant } from './components/SearchField';
import * as Search from './index';

const meta: Meta<typeof Search.Root> = {
  title: 'Input/Search/SearchWithOptions',
  component: Search.Root,
};

export default meta;
type Story = StoryObj<typeof Search.Root>;

const people = [
  { id: 1, name: '<PERSON><PERSON><PERSON>' },
  { id: 2, name: '<PERSON>on <PERSON>' },
  { id: 3, name: '<PERSON><PERSON>' },
  { id: 4, name: '<PERSON>' },
  { id: 5, name: '<PERSON><PERSON>' },
];
type People = (typeof people)[number];
const variants: Variant[] = ['bordered', 'plain'];

export const Variants = () => {
  const [selectedPerson, setSelectedPerson] = useState('');
  const [query, setQuery] = useState('');

  const filteredPeople =
    query === ''
      ? people
      : people.filter((person) => {
          return person.name.toLowerCase().includes(query.toLowerCase());
        });

  return (
    <div className="flex flex-col gap-6">
      {variants.map((variant) => (
        <div key={variant} className="w-full sm:w-[400px]">
          <Search.Root label={`Input label - ${variant} variant`} value={selectedPerson} onChange={setSelectedPerson}>
            <Search.Field
              placeholder="Search person"
              onChange={(search) => setQuery(search)}
              displayValue={(person: People) => person.name}
              variant={variant}
            />
            <Search.Options>
              {filteredPeople.length === 0 && query !== '' ? (
                <div className="relative cursor-default select-none px-4 py-2 text-gray-700">Nothing found.</div>
              ) : (
                filteredPeople.map((person) => (
                  <Search.Option key={person.id} value={person}>
                    {person.name}
                  </Search.Option>
                ))
              )}
            </Search.Options>
          </Search.Root>
        </div>
      ))}
    </div>
  );
};

export const WithoutMagnifyingGlassIcon = () => {
  const [selectedPerson, setSelectedPerson] = useState('');
  const [query, setQuery] = useState('');

  const filteredPeople =
    query === ''
      ? people
      : people.filter((person) => {
          return person.name.toLowerCase().includes(query.toLowerCase());
        });

  return (
    <div className="w-full sm:w-[400px]">
      <Search.Root label="Input Label" value={selectedPerson} onChange={setSelectedPerson}>
        <Search.Field
          placeholder="Search person"
          onChange={(search) => setQuery(search)}
          displayValue={(person: People) => person.name}
          withSearchIcon={false}
        />
        <Search.Options>
          {filteredPeople.length === 0 && query !== '' ? (
            <div className="relative cursor-default select-none px-4 py-2 text-gray-700">Nothing found.</div>
          ) : (
            filteredPeople.map((person) => (
              <Search.Option key={person.id} value={person}>
                {person.name}
              </Search.Option>
            ))
          )}
        </Search.Options>
      </Search.Root>
    </div>
  );
};

export const WithError = () => {
  return (
    <div className="w-full sm:w-[400px]">
      <Search.Root label="Input Label" defaultValue={null} onChange={() => {}}>
        <Search.Field
          error="Option is not valid"
          placeholder="Search person"
          onChange={() => {}}
          displayValue={() => 'Invalid'}
        />
      </Search.Root>
    </div>
  );
};

export const WithClearSelection = () => {
  const [selectedPerson, setSelectedPerson] = useState<(typeof people)[0] | null>(null);
  const [query, setQuery] = useState('');

  const filteredPeople =
    query === ''
      ? people
      : people.filter((person) => {
          return person.name.toLowerCase().includes(query.toLowerCase());
        });

  return (
    <div className="w-full sm:w-[400px]">
      <Search.Root label="Input Label" value={selectedPerson} onChange={setSelectedPerson}>
        <Search.Field
          placeholder="Search person"
          displayValue={(person: People) => person?.name}
          withSearchIcon={false}
          selectedValue={selectedPerson}
          onChange={(search) => setQuery(search)}
          onClear={() => setSelectedPerson(null)}
        />
        <Search.Options>
          {filteredPeople.length === 0 && query !== '' ? (
            <div className="relative cursor-default select-none px-4 py-2 text-gray-700">Nothing found.</div>
          ) : (
            filteredPeople.map((person) => (
              <Search.Option key={person.id} value={person}>
                {person.name}
              </Search.Option>
            ))
          )}
        </Search.Options>
      </Search.Root>
    </div>
  );
};

export const WithDeleteSelection = () => {
  const [peopleCopy, setPeople] = useState([...people]);
  const [selectedPerson, setSelectedPerson] = useState<(typeof people)[0] | null>(null);
  const [query, setQuery] = useState('');

  const filteredPeople =
    query === ''
      ? peopleCopy
      : peopleCopy.filter((person) => {
          return person.name.toLowerCase().includes(query.toLowerCase());
        });

  const handleDelete = () => {
    setPeople(peopleCopy.filter((person) => person.id !== selectedPerson?.id));
    setSelectedPerson(null);
  };

  return (
    <div className="w-full sm:w-[400px]">
      <Search.Root label="Input Label" value={selectedPerson} onChange={setSelectedPerson}>
        <Search.Field
          placeholder="Search person"
          displayValue={(person: People) => person?.name}
          withSearchIcon={false}
          selectedValue={selectedPerson}
          onChange={(search) => setQuery(search)}
          onDelete={handleDelete}
        />
        <Search.Options>
          {filteredPeople.length === 0 && query !== '' ? (
            <div className="relative cursor-default select-none px-4 py-2 text-gray-700">Nothing found.</div>
          ) : (
            filteredPeople.map((person) => (
              <Search.Option key={person.id} value={person}>
                {person.name}
              </Search.Option>
            ))
          )}
        </Search.Options>
      </Search.Root>
    </div>
  );
};

export const WithClearAndDeleteSelection = () => {
  const [peopleCopy, setPeople] = useState([...people]);
  const [selectedPerson, setSelectedPerson] = useState<(typeof people)[0] | null>(null);
  const [query, setQuery] = useState('');

  const filteredPeople =
    query === ''
      ? peopleCopy
      : peopleCopy.filter((person) => {
          return person.name.toLowerCase().includes(query.toLowerCase());
        });

  const handleDelete = () => {
    setPeople(peopleCopy.filter((person) => person.id !== selectedPerson?.id));
    setSelectedPerson(null);
  };

  return (
    <div className="w-full sm:w-[400px]">
      <Search.Root label="Input Label" value={selectedPerson} onChange={setSelectedPerson}>
        <Search.Field
          placeholder="Search person"
          displayValue={(person: People) => person?.name}
          withSearchIcon={false}
          selectedValue={selectedPerson}
          onChange={(search) => setQuery(search)}
          onClear={() => setSelectedPerson(null)}
          onDelete={handleDelete}
        />
        <Search.Options>
          {filteredPeople.length === 0 && query !== '' ? (
            <div className="relative cursor-default select-none px-4 py-2 text-gray-700">Nothing found.</div>
          ) : (
            filteredPeople.map((person) => (
              <Search.Option key={person.id} value={person}>
                {person.name}
              </Search.Option>
            ))
          )}
        </Search.Options>
      </Search.Root>
    </div>
  );
};

export const WithButton = () => {
  return (
    <Search.Root label="Input Label">
      <div className="flex items-center justify-start gap-2">
        <Search.Field placeholder="Search person" onChange={() => {}} />
        <Search.Button onClick={() => alert('Button clicked')}>Button</Search.Button>
      </div>
    </Search.Root>
  );
};
