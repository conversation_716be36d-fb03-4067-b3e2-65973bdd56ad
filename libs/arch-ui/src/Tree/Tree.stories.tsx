import React, { useRef } from 'react';
import type { Meta, StoryObj } from '@storybook/react-vite';
import { TreeItem } from './components/TreeItem';
import type { NodeModel, TreeMethods, TreeProps } from './Tree';
import { Tree } from './Tree';
import { createItem } from './tree-utils';

type CustomData = { name: string };
const sampleData: NodeModel<CustomData>[] = [
  createItem({
    id: '1',
    parent: '0',
    text: 'Folder 1',
    data: {
      name: 'Folder 1-1',
    },
  }),
  createItem({
    id: '2',
    parent: '1',
    text: 'File 1-1',
    data: {
      name: 'File 1-1',
    },
  }),
  createItem({
    id: '3',
    parent: '1',
    text: 'File 1-2',
    data: {
      name: 'File 1-2',
    },
  }),
  createItem({
    id: '4',
    parent: '0',
    text: 'Folder 2',
    data: {
      name: 'Folder 2',
    },
  }),
  createItem({
    id: '5',
    parent: '4',
    text: 'Folder 2-1',
    data: {
      name: 'Folder 2-1',
    },
  }),
  createItem({
    id: '6',
    parent: '5',
    text: 'File 2-1-1',
    data: {
      name: 'File 2-1-1',
    },
  }),
  createItem({
    id: '7',
    parent: '0',
    text: 'File 3',
    data: {
      name: 'File 3',
    },
  }),
];
type Story = StoryObj<typeof Tree>;

const meta: Meta<typeof Tree> = {
  title: 'Layout and Organization/Tree/Tree',
  component: Tree,
} as Meta<typeof Tree>;
export default meta;

function Template<T>(props: TreeProps<T>) {
  return <Tree {...props} />;
}

export const Default: Story = {
  render: Template,

  args: {
    data: sampleData,
    rootId: '0',
    render: (node, params) => <TreeItem node={node} params={params} />,
  },
};

export const Actions = () => {
  const treeRef = useRef<TreeMethods>(null);
  const actions = [
    { label: 'check all', handler: () => treeRef?.current?.checkAll() },
    { label: 'uncheck all', handler: () => treeRef?.current?.uncheckAll() },
    { label: 'open all', handler: () => treeRef?.current?.openAll() },
    { label: 'close all', handler: () => treeRef?.current?.closeAll() },
  ];

  return (
    <div>
      <div className="flex flex-row gap-4 mb-4">
        {actions.map(({ label, handler }) => (
          <button key={label} type="button" className="border p-1" onClick={handler}>
            {label}
          </button>
        ))}
      </div>
      <div className="flex w-96 flex-col p-7">
        <Tree rootId="0" data={sampleData} ref={treeRef} />
      </div>
    </div>
  );
};

export const DisableDrag = () => {
  return <Tree enableDrag={false} rootId="0" data={sampleData} />;
};
