import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import type { TreeItemProps } from './TreeItem';
import { TreeItem } from './TreeItem';
import { createItem } from 'Tree/tree-utils';

function buildParams<T>(props: Partial<TreeItemProps<T>['params']> = {}): TreeItemProps<T>['params'] {
  return {
    depth: 0,
    isOpen: true,
    hasChild: false,
    draggable: true,
    checked: false,
    onToggle: jest.fn,
    onCheck: jest.fn,
    ...props,
  };
}
function buildTreeItemProps<T>(props: Partial<TreeItemProps<T>> = {}): TreeItemProps<T> {
  return {
    node: createItem({ id: 'item 1', parent: '', text: 'Item 1' }),
    params: buildParams({
      depth: 0,
      isOpen: true,
      hasChild: false,
      onToggle: jest.fn,
    }),
    ...props,
  };
}

describe('TreeItem', () => {
  it('renders checked', () => {
    const props = buildTreeItemProps({ params: buildParams({ checked: true }) });
    render(<TreeItem {...props} />);

    expect(screen.getByRole('checkbox', { checked: true })).toBeInTheDocument();
  });

  it('invokes onCheck when clicked on the checkbox', async () => {
    const props = buildTreeItemProps({
      params: buildParams({ checked: false, onCheck: jest.fn() }),
    });
    render(<TreeItem {...props} />);

    await userEvent.click(screen.getByRole('checkbox', { checked: false }));

    expect(props.params.onCheck).toBeCalledWith(props.node, true);
  });

  it('renders collapsed', () => {
    const props = buildTreeItemProps({ params: buildParams({ isOpen: false }) });

    render(<TreeItem {...props} />);

    expect(screen.getByRole('treeitem', { expanded: false })).toBeInTheDocument();
  });

  it('renders expanded', () => {
    const props = buildTreeItemProps({ params: buildParams({ isOpen: true }) });

    render(<TreeItem {...props} />);

    expect(screen.getByRole('treeitem', { expanded: true })).toBeInTheDocument();
  });

  it('invokes the onToggle when the user toggles the tree item', async () => {
    const props = buildTreeItemProps({
      params: buildParams({ isOpen: true, hasChild: true, onToggle: jest.fn() }),
    });
    render(<TreeItem {...props} />);

    await userEvent.click(screen.getByRole('button', { name: 'toggle' }));

    expect(props.params.onToggle).toBeCalled();
  });

  it('renders the item text when there is no custom children', () => {
    const props = buildTreeItemProps({
      node: createItem({ id: 'item.1', parent: '', text: 'Item 1' }),
    });

    render(<TreeItem {...props} />);

    expect(screen.getByText('Item 1')).toBeInTheDocument();
  });

  it('renders a custom content', () => {
    const props = buildTreeItemProps({
      node: createItem({
        id: 'item.1',
        parent: '',
        text: 'Item 1',
      }),
      children: <>Custom element</>,
    });

    render(<TreeItem {...props} />);

    expect(screen.getByText('Custom element')).toBeInTheDocument();
  });

  it('render drag icon if draggable', () => {
    const props = buildTreeItemProps({
      node: createItem({
        id: 'item.1',
        parent: '',
        text: 'Item 1',
      }),
      children: <>Custom element</>,
      params: buildParams({ draggable: true }),
    });

    render(<TreeItem {...props} />);

    expect(screen.getByRole('button', { name: 'Drag location' })).toBeInTheDocument();
  });

  it('does not render drag icon if not draggable', () => {
    const props = buildTreeItemProps({
      node: createItem({
        id: 'item.1',
        parent: '',
        text: 'Item 1',
      }),
      children: <>Custom element</>,
      params: buildParams({ draggable: false }),
    });

    render(<TreeItem {...props} />);

    expect(screen.queryByRole('button', { name: 'Drag location' })).not.toBeInTheDocument();
  });
});
