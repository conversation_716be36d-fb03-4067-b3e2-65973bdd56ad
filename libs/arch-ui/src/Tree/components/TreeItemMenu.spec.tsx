import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import type { TreeItemMenuProps } from './TreeItemMenu';
import { TreeItemMenu } from './TreeItemMenu';

const buildTreeItemMenuProps = (props: Partial<TreeItemMenuProps> = {}): TreeItemMenuProps => ({
  icon: () => null,
  'aria-label': 'Menu options',
  options: [],
  ...props,
});

describe('TreeItemMenu', () => {
  it('renders the menu options', async () => {
    const props = buildTreeItemMenuProps({
      'aria-label': 'Menu options',
      options: [
        {
          icon: () => null,
          label: 'First option',
          onClick: jest.fn(),
          disabled: false,
        },
        {
          icon: () => null,
          label: 'Second option',
          onClick: jest.fn(),
          disabled: false,
        },
      ],
    });
    render(<TreeItemMenu {...props} />);

    await userEvent.click(screen.getByRole('button', { name: 'Menu options' }));

    expect(screen.getByRole('menu')).toBeInTheDocument();
    expect(screen.getByRole('menuitem', { name: /First option/ })).toBeInTheDocument();
    expect(screen.getByRole('menuitem', { name: /Second option/ })).toBeInTheDocument();
  });

  it('renders a disable option', async () => {
    const props = buildTreeItemMenuProps({
      'aria-label': 'Menu options',
      options: [
        {
          icon: () => null,
          label: 'First option',
          onClick: jest.fn(),
          disabled: true,
        },
      ],
    });
    render(<TreeItemMenu {...props} />);

    await userEvent.click(screen.getByRole('button', { name: 'Menu options' }));

    expect(screen.getByRole('menu')).toBeInTheDocument();
    expect(screen.getByRole('menuitem', { name: /First option/ })).toHaveAttribute('aria-disabled', 'true');
  });
});
