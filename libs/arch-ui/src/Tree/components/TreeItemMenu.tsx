import React from 'react';
import type { IconButtonProps } from '../../Button/IconButton/IconButton';
import { IconButton } from '../../Button/IconButton/IconButton';
import * as Dropdown from '../../Dropdown/Dropdown';

type Option = {
  label: string;
  icon: React.FC<React.ComponentProps<'svg'>>;
  onClick: () => void;
  disabled?: boolean;
};

export type TreeItemMenuProps = Pick<IconButtonProps, 'icon' | 'aria-label'> & {
  options: Option[];
};

export function TreeItemMenu({ options, ...iconButtonProps }: TreeItemMenuProps) {
  return (
    <Dropdown.Root>
      <Dropdown.Trigger asChild>
        <IconButton color="secondary" size="xs" variant="text" {...iconButtonProps} />
      </Dropdown.Trigger>
      <Dropdown.Items>
        {options.map(({ disabled, label, icon: Icon, onClick }) => {
          return (
            <Dropdown.Item
              key={label}
              onClick={onClick}
              startAdornment={<Icon className="h-4 w-4 text-gray-400" />}
              disabled={disabled}
            >
              {label}
            </Dropdown.Item>
          );
        })}
      </Dropdown.Items>
    </Dropdown.Root>
  );
}
