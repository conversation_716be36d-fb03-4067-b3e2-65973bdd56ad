import React, { createRef } from 'react';
import { act } from 'react-dom/test-utils';
import { render, screen, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { TreeItem } from './components/TreeItem';
import type { TreeMethods, TreeProps } from './Tree';
import { Tree } from './Tree';
import { createItem } from './tree-utils';

function buildTreeProps<T = unknown>(props: Partial<TreeProps<T>>): TreeProps<T> {
  return {
    rootId: '0',
    data: [],
    onDrop: jest.fn(),
    onCheck: jest.fn(),
    ...props,
  };
}

describe('TreeSelector', () => {
  it('renders a single node', () => {
    const props = buildTreeProps({ data: [createItem({ id: '1', parent: '0', text: 'Item 1' })] });

    render(<Tree {...props} />);

    expect(screen.getByRole('treeitem', { name: /Item 1/ })).toBeInTheDocument();
  });

  describe('when has children', () => {
    it('renders the parent node and respective children', () => {
      const props = buildTreeProps({
        data: [
          createItem({ id: '1', parent: '0', text: 'Item 1' }),
          createItem({ id: '2', parent: '1', text: 'Item 2' }),
        ],
      });

      render(<Tree {...props} />);

      expect(screen.getByRole('treeitem', { name: /Item 1/ })).toBeInTheDocument();
      expect(screen.getByRole('treeitem', { name: /Item 1/ })).toHaveAttribute('aria-level', '1');
      expect(screen.getByRole('treeitem', { name: /Item 2/ })).toBeInTheDocument();
      expect(screen.getByRole('treeitem', { name: /Item 2/ })).toHaveAttribute('aria-level', '2');
    });

    it('toggles node with children when is clicked', async () => {
      const props = buildTreeProps({
        data: [
          createItem({ id: '1', parent: '0', text: 'Item 1' }),
          createItem({ id: '2', parent: '1', text: 'Item 2' }),
        ],
      });

      render(<Tree {...props} />);

      const collapseButton = within(screen.getByRole('treeitem', { name: /Item 1/, expanded: true })).getByRole(
        'button',
        { name: 'toggle' }
      );
      await userEvent.click(collapseButton);

      expect(screen.queryByRole('treeitem', { name: /Item 2/ })).not.toBeInTheDocument();
      screen.getByRole('treeitem', { name: /Item 1/, expanded: false });

      const expandButton = within(screen.getByRole('treeitem', { name: /Item 1/, expanded: false })).getByRole(
        'button',
        { name: 'toggle' }
      );
      await userEvent.click(expandButton);

      expect(screen.getByRole('treeitem', { name: /Item 2/ })).toBeInTheDocument();
      screen.getByRole('treeitem', { name: /Item 1/, expanded: true });
    });
  });

  describe('checking options', () => {
    it('toggles the check of a single option', async () => {
      const treeRef = createRef<TreeMethods>();
      const props = buildTreeProps({
        data: [
          createItem({ id: '1', parent: '0', text: 'Item 1' }),
          createItem({ id: '2', parent: '0', text: 'Item 2' }),
        ],
      });

      render(<Tree ref={treeRef} {...props} />);

      await userEvent.click(within(screen.getByRole('treeitem', { name: /Item 1/ })).getByRole('checkbox'));

      expect(props.onCheck).toBeCalledWith(['1']);
      expect(within(screen.getByRole('treeitem', { name: /Item 1/ })).getByRole('checkbox')).toBeChecked();

      await userEvent.click(within(screen.getByRole('treeitem', { name: /Item 1/ })).getByRole('checkbox'));

      expect(props.onCheck).toBeCalledWith([]);
      expect(within(screen.getByRole('treeitem', { name: /Item 1/ })).getByRole('checkbox')).not.toBeChecked();
    });

    it('toggles the check of the option and its own descendents', async () => {
      const treeRef = createRef<TreeMethods>();
      const props = buildTreeProps({
        data: [
          createItem({ id: '1', parent: '0', text: 'Item 1' }),
          createItem({ id: '2', parent: '1', text: 'Item 2' }),
        ],
      });

      render(<Tree ref={treeRef} {...props} />);

      await userEvent.click(within(screen.getByRole('treeitem', { name: /Item 1/ })).getByRole('checkbox'));

      expect(props.onCheck).toBeCalledWith(['1', '2']);
      expect(within(screen.getByRole('treeitem', { name: /Item 1/ })).getByRole('checkbox')).toBeChecked();
      expect(within(screen.getByRole('treeitem', { name: /Item 2/ })).getByRole('checkbox')).toBeChecked();

      await userEvent.click(within(screen.getByRole('treeitem', { name: /Item 1/ })).getByRole('checkbox'));

      expect(props.onCheck).toBeCalledWith([]);
      expect(within(screen.getByRole('treeitem', { name: /Item 1/ })).getByRole('checkbox')).not.toBeChecked();
      expect(within(screen.getByRole('treeitem', { name: /Item 2/ })).getByRole('checkbox')).not.toBeChecked();
    });

    it('checks and unchecks all the options', async () => {
      const treeRef = createRef<TreeMethods>();
      const props = buildTreeProps({
        data: [
          createItem({ id: '1', parent: '0', text: 'Item 1' }),
          createItem({ id: '2', parent: '1', text: 'Item 2' }),
        ],
      });

      render(<Tree ref={treeRef} {...props} />);

      act(() => {
        treeRef.current?.checkAll();
      });

      expect(props.onCheck).toBeCalledWith(['1', '2']);
      expect(within(screen.getByRole('treeitem', { name: /Item 1/ })).getByRole('checkbox')).toBeChecked();
      expect(within(screen.getByRole('treeitem', { name: /Item 2/ })).getByRole('checkbox')).toBeChecked();

      act(() => {
        treeRef.current?.uncheckAll();
      });

      expect(props.onCheck).toBeCalledWith([]);
      expect(within(screen.getByRole('treeitem', { name: /Item 1/ })).getByRole('checkbox')).not.toBeChecked();
      expect(within(screen.getByRole('treeitem', { name: /Item 2/ })).getByRole('checkbox')).not.toBeChecked();
    });
  });

  it('renders the item with custom information', () => {
    const props = buildTreeProps({
      data: [
        createItem({ id: '1', parent: '0', text: 'Item 1', data: { subtitle: 'Subtitle 1' } }),
        createItem({ id: '2', parent: '1', text: 'Item 2', data: { subtitle: 'Subtitle 2' } }),
      ],
      render: (node) => <span>{node.data?.subtitle}</span>,
    });

    render(
      <Tree
        {...props}
        render={(node, params) => (
          <TreeItem node={node} params={params}>
            {node.data?.subtitle}
          </TreeItem>
        )}
      />
    );

    expect(screen.getByRole('treeitem', { name: /Subtitle 1/ })).toBeInTheDocument();
    expect(screen.getByRole('treeitem', { name: /Subtitle 2/ })).toBeInTheDocument();
  });
});
