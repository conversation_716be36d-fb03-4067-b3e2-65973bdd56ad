import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { TreeSelector } from './TreeSelector';

const exampleTree = {
  id: '1',
  name: 'Whole Wide',
  shortCode: 'ROOT',
  children: [
    {
      id: '1',
      parentId: null,
      name: 'Project Wide',
      shortCode: 'PROJ',
      hasChildren: true,
    },
    {
      id: '2',
      parentId: '1',
      name: 'Battersea Power Station',
      shortCode: 'BPS',
      hasChildren: true,
    },
    {
      id: '3',
      parentId: '2',
      name: 'Concourse',
      shortCode: 'CON',
    },
    {
      id: '4',
      parentId: '21',
      name: 'Car Park',
      shortCode: 'CPK',
      hasChildren: true,
    },
    {
      id: '5',
      parentId: '4',
      name: 'Level 1',
      shortCode: 'LV1',
    },
    {
      id: '6',
      parentId: '1',
      name: 'Nine Elms',
      shortCode: 'NELMS',
      hasChildren: true,
    },
    {
      id: '7',
      parentId: '6',
      name: 'Ticket Office',
      shortCode: 'TICK',
      hasChildren: true,
    },
    {
      id: '8',
      parentId: '7',
      name: 'Front',
      shortCode: 'FRO',
    },
    {
      id: '9',
      parentId: '7',
      name: 'Back',
      shortCode: 'BAC',
    },
    {
      id: '10',
      parentId: '4',
      name: 'Level 2',
      shortCode: 'LV2',
    },
    {
      id: '11',
      parentId: '4',
      name: 'Level 3',
      shortCode: 'LV3',
    },
    {
      id: '12',
      parentId: '4',
      name: 'Level 4',
      shortCode: 'LV4',
    },
    {
      id: '13',
      parentId: '4',
      name: 'Level 5',
      shortCode: 'LV5',
    },
    {
      id: '14',
      parentId: '4',
      name: 'Level 6',
      shortCode: 'LV6',
    },
    {
      id: '15',
      parentId: '4',
      name: 'Level 7',
      shortCode: 'LV7',
    },
    {
      id: '16',
      parentId: '4',
      name: 'Level 8',
      shortCode: 'LV8',
    },
    {
      id: '17',
      parentId: '4',
      name: 'Level 9',
      shortCode: 'LV9',
    },
    {
      id: '18',
      parentId: '4',
      name: 'Level 10',
      shortCode: 'LV10',
    },
    {
      id: '19',
      parentId: '4',
      name: 'Level 11',
      shortCode: 'LV11',
      hasChildren: true,
    },
    {
      id: '20',
      parentId: '19',
      name: 'East',
      shortCode: 'EAS',
    },
    {
      id: '21',
      parentId: '2',
      name: 'Outside',
      shortCode: 'OUT',
      hasChildren: true,
    },
  ],
};

describe('TreeSelector', () => {
  it('renders the title and first level of nodes ', () => {
    render(<TreeSelector nodes={exampleTree.children} rootNodeId="1" />);

    expect(screen.getByRole('button', { name: /Battersea Power Station/ })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Nine Elms/ })).toBeInTheDocument();
    expect(screen.getByText(/Project Wide/)).toBeInTheDocument();
  });

  it('renders the child nodes after parent is selected', async () => {
    render(<TreeSelector nodes={exampleTree.children} rootNodeId="1" />);

    await userEvent.click(screen.getByRole('button', { name: /Battersea Power Station/ }));

    expect(screen.getByRole('button', { name: /Concourse/ })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Outside/ })).toBeInTheDocument();
  });

  it('updates breadcrumbs on selecting nodes', async () => {
    render(<TreeSelector nodes={exampleTree.children} rootNodeId="1" />);

    await userEvent.click(screen.getByRole('button', { name: /Battersea Power Station/ }));
    expect(screen.getByRole('button', { name: /Remove Battersea Power Station/ })).toBeInTheDocument();

    await userEvent.click(screen.getByRole('button', { name: /Outside/ }));
    expect(screen.getByRole('button', { name: /Remove Outside/ })).toBeInTheDocument();
  });

  it('updates breadcrumbs on clearing all, going back to the first level of nodes', async () => {
    render(<TreeSelector nodes={exampleTree.children} rootNodeId="1" />);

    await userEvent.click(screen.getByRole('button', { name: /Battersea Power Station/ }));
    expect(screen.getByRole('button', { name: /Remove Battersea Power Station/ })).toBeInTheDocument();

    await userEvent.click(screen.getByRole('button', { name: /Outside/ }));
    expect(screen.getByRole('button', { name: /Remove Outside/ })).toBeInTheDocument();

    await userEvent.click(screen.getByRole('button', { name: /Clear all/ }));
    expect(screen.queryByRole('button', { name: /Remove Battersea Power Station/ })).not.toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /Remove Outside/ })).not.toBeInTheDocument();
  });

  describe('Search feature', () => {
    it('renders the search section', () => {
      render(<TreeSelector nodes={exampleTree.children} rootNodeId="1" />);

      expect(screen.getByRole('searchbox')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Search')).toBeInTheDocument();
    });

    it('hides the search section if hideSearchField is true', () => {
      render(<TreeSelector nodes={exampleTree.children} rootNodeId="1" hideSearchField />);

      expect(screen.queryByRole('searchbox')).not.toBeInTheDocument();
    });

    it('renders a custom placeholder', () => {
      render(
        <TreeSelector nodes={exampleTree.children} rootNodeId="1" searchFieldPlaceholder="This is a placeholder" />
      );

      expect(screen.getByPlaceholderText('This is a placeholder')).toBeInTheDocument();
    });

    it("displays the no search results placeholder when there's no results", async () => {
      render(
        <TreeSelector nodes={exampleTree.children} rootNodeId="1" searchFieldPlaceholder="This is a placeholder" />
      );

      await userEvent.type(screen.getByRole('searchbox'), 'random text!');

      expect(await screen.findByText('No matching search results')).toBeInTheDocument();
    });

    it('displays the nodes path that match the search field value', async () => {
      render(
        <TreeSelector nodes={exampleTree.children} rootNodeId="1" searchFieldPlaceholder="This is a placeholder" />
      );

      await userEvent.type(screen.getByRole('searchbox'), 'Car Park');

      expect(
        await screen.findByRole('button', { name: /Battersea Power Station\b.*\bCar Park\b/ })
      ).toBeInTheDocument();
    });

    it('clears the search field value when a breadcrumb item is removed', async () => {
      render(<TreeSelector nodes={exampleTree.children} rootNodeId="1" />);

      await userEvent.type(screen.getByRole('searchbox'), 'Car Park');
      await userEvent.click(await screen.findByRole('button', { name: /Car Park/ }));
      await userEvent.click(await screen.findByRole('button', { name: /Remove Car Park/ }));

      expect(await screen.findByRole('searchbox')).toHaveValue('');
    });
  });
});
