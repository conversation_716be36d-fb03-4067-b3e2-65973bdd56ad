import React from 'react';
import type { Meta } from '@storybook/react-vite';
import { InputDateTime } from './InputDateTime';

export default {
  title: 'Input/InputDateTime',
  component: InputDateTime,
} as Meta<typeof InputDateTime>;

export const Default = {
  args: {
    label: 'Date',
    name: 'date',
  },
};

export const WithValue = {
  args: { ...Default.args, value: '2020-09-29 09:00' },
};

export const WithErrors = {
  args: { ...Default.args, error: 'error' },
};

export const Disabled = {
  args: { ...Default.args, disabled: true },
};

export const WithCornerAdornment = {
  args: { ...Default.args, cornerAdornment: <>Corner Adornment</> },
};

export const WithFullWidth = {
  args: {
    ...Default.args,
    fullWidth: true,
  },
};
