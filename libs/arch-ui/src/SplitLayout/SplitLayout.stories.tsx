import React, { type ComponentProps, type FC, type ReactNodeArray, useState } from 'react';
import type { Meta, StoryFn, StoryObj } from '@storybook/react-vite';
import { useLayout } from '../hooks/useLayout';
import { SplitLayout, type SplitLayoutProps } from './SplitLayout';

export default {
  title: 'Screens/SplitLayout',
  component: SplitLayout,
  subcomponents: {
    Panel: SplitLayout.Panel,
  },
  decorators: [(Children) => <div style={{ height: '80vh' }}>{Children()}</div>],
} as Meta<SplitLayoutProps>;

// Used only to access LayoutContext values (setExpaded method)
const TemplateWithContext: FC<ComponentProps<typeof SplitLayout.Panel>> = ({ children, ...props }) => {
  const { expandedPanel, setExpandedPanel } = useLayout();

  return (
    <SplitLayout.Panel {...props}>
      <div className="flex flex-col">
        {children}
        <button
          type="button"
          className="w-40 border border-r-2"
          aria-label="panel-action"
          onClick={() => setExpandedPanel(expandedPanel !== props.index ? props.index : undefined)}
        >
          click me
        </button>
      </div>
    </SplitLayout.Panel>
  );
};

const Template: StoryFn<SplitLayoutProps> = (args) => {
  const [expandedPanel, setExpandedPanel] = useState<number>();
  return <SplitLayout expandedPanel={expandedPanel} {...args} onSetLayout={setExpandedPanel} />;
};

export const Row: StoryObj<SplitLayoutProps> = {
  render: Template,

  args: {
    direction: 'row',
    children: [
      <SplitLayout.Panel key="panel-0" className="flex-1 bg-rose-300">
        first panel (flex)
      </SplitLayout.Panel>,
      <SplitLayout.Panel key="panel-1" className="flex-1 bg-violet-300">
        second panel (flex)
      </SplitLayout.Panel>,
    ],
  },
};

export const Column: StoryObj<SplitLayoutProps> = {
  render: Template,

  args: {
    ...Row.args,
    direction: 'col',
  },
};

export const ThreePanels: StoryObj<SplitLayoutProps> = {
  render: Template,

  args: {
    ...Row.args,
    children: [
      ...(Row.args?.children as ReactNodeArray),
      <SplitLayout.Panel key="panel-2" className="w-5/12 bg-blue-300">
        third panel (5/12)
      </SplitLayout.Panel>,
    ],
  },
};

export const ExpandPanelOnClick: StoryObj<SplitLayoutProps> = {
  render: Template,

  args: {
    ...Row.args,
    children: [
      <TemplateWithContext key="panel-0" className="w-4/12 bg-rose-300">
        first panel (4/12)
      </TemplateWithContext>,
      <SplitLayout.Panel key="panel-1" className="w-8/12 bg-violet-300">
        second panel (8/12)
      </SplitLayout.Panel>,
    ],
  },
};

export const WithFirstActivePanel: StoryObj<SplitLayoutProps> = {
  render: Template,

  args: {
    ...Row.args,
    expandedPanel: 0,
  },
};
