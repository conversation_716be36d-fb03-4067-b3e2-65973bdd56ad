import * as React from 'react';
import type { Meta, StoryObj } from '@storybook/react-vite';
import { SkeletonText } from './SkeletonText';
import { sizes } from './skeleton-config';

export default {
  title: 'Status/Skeleton/Text',
  component: SkeletonText,
} as Meta<typeof SkeletonText>;

export const Text: StoryObj<typeof SkeletonText> = {
  render: (args) => (
    <div className="flex flex-col flex-nowrap items-center gap-4">
      {sizes.map((size) => (
        <SkeletonText key={size} {...args} size={size} />
      ))}
    </div>
  ),
};
