import * as React from 'react';
import type { Meta, StoryObj } from '@storybook/react-vite';
import { SkeletonSquare } from './SkeletonSquare';
import { sizes } from './skeleton-config';

export default {
  title: 'Status/Skeleton/Square',
  component: SkeletonSquare,
} as Meta<typeof SkeletonSquare>;

export const Square: StoryObj<typeof SkeletonSquare> = {
  render: (args) => (
    <div className="flex flex-row flex-nowrap items-center gap-4">
      {sizes.map((size) => (
        <SkeletonSquare key={size} {...args} size={size} />
      ))}
    </div>
  ),
};
