import * as React from 'react';
import classNames from 'clsx';
import { SkeletonBase, type SkeletonBaseProps } from './SkeletonBase';
import type { Size } from './skeleton-config';

const sizeClasses: Record<Size, string> = {
  xxs: 'h-4 w-4',
  xs: 'h-6 w-6',
  sm: 'h-8 w-8',
  md: 'h-10 w-10',
  lg: 'h-12 w-12',
  xl: 'h-14 w-14',
  xxl: 'h-16 w-16',
};

export interface SkeletonSquareProps extends SkeletonBaseProps {
  size: Size;
}

export const SkeletonSquare: React.FC<SkeletonSquareProps> = ({ size, ...props }) => (
  <SkeletonBase {...props} className={classNames('rounded-md', sizeClasses[size])} />
);
