import * as React from 'react';
import type { Meta, StoryObj } from '@storybook/react-vite';
import { SkeletonCircular } from './SkeletonCircular';
import { sizes } from './skeleton-config';

export default {
  title: 'Status/Skeleton/Circular',
  component: SkeletonCircular,
} as Meta<typeof SkeletonCircular>;

export const Circular: StoryObj<typeof SkeletonCircular> = {
  render: (args) => (
    <div className="flex flex-row flex-nowrap items-center gap-4">
      {sizes.map((size) => (
        <SkeletonCircular key={size} {...args} size={size} />
      ))}
    </div>
  ),
};
