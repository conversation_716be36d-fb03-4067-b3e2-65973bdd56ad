import * as React from 'react';
import type { Meta, StoryObj } from '@storybook/react-vite';
import { SkeletonCard } from './SkeletonCard';
import { sizes } from './skeleton-config';

export default {
  title: 'Status/Skeleton/Card',
  component: SkeletonCard,
} as Meta<typeof SkeletonCard>;

export const Card: StoryObj<typeof SkeletonCard> = {
  render: (args) => (
    <div className="flex flex-col flex-nowrap items-center gap-4">
      {sizes.map((size) => (
        <SkeletonCard key={size} {...args} size={size} />
      ))}
    </div>
  ),
};
