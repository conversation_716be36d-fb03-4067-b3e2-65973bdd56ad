import React from 'react';
import type { Meta } from '@storybook/react-vite';
import { InputTextArea } from './InputTextArea';

export default {
  title: 'Input/InputTextArea',
  component: InputTextArea,
} as Meta<typeof InputTextArea>;

export const Standard = {
  args: {
    label: 'Example',
    name: 'example',
    id: 'example',
    required: true,
    rows: 5,
  },
};

export const WithValue = {
  args: {
    label: 'Example',
    name: 'example',
    id: 'example',
    value: 'This is my value!',
  },
};

export const WithError = {
  args: {
    label: 'Example',
    name: 'example',
    id: 'example',
    error: 'This field has an error message!',
  },
};

export const Disabled = {
  args: {
    label: 'Example',
    name: 'example',
    id: 'example',
    value: 'This is my value!',
    disabled: true,
  },
};

export const WithCornerAdornment = {
  args: {
    label: 'Example',
    name: 'example',
    id: 'example',
    value: 'This is my value!',
    cornerAdornment: <>Corner Adornment</>,
  },
};

export const WithCornerAdornmentAndNoLabel = {
  args: {
    name: 'example',
    id: 'example',
    value: 'This is my value!',
    cornerAdornment: <>Corner Adornment</>,
  },
};

export const WithFullWidth = {
  args: {
    ...Standard.args,
    fullWidth: true,
  },
};
