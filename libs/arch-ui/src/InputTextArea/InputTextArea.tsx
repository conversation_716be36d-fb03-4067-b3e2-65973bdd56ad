import React from 'react';
import classNames from 'clsx';
import { twMerge } from 'tailwind-merge';
import { ExclamationCircleIcon } from '../Icons/solid';
import type { InputBaseProps } from '../types/InputBase';

export interface InputTextAreaProps extends InputBaseProps<HTMLTextAreaElement> {
  /**
   * Determines how many lines/rows should be displayed on the textarea
   * @default 3
   */
  rows?: number;

  className?: string;
}

export const InputTextArea = React.forwardRef<HTMLTextAreaElement, InputTextAreaProps>(
  (
    {
      cornerAdornment,
      description,
      disabled,
      error,
      fullWidth,
      touched = true,
      id,
      label,
      name,
      onBlur,
      onChange,
      required,
      placeholder,
      rows = 3,
      value,
      className,
    },
    ref
  ) => {
    const showError = error && touched;

    const labelClassnames = classNames('block text-sm font-medium text-gray-700', {
      'w-full': fullWidth,
    });

    const inputClassnames = twMerge(
      classNames(
        'appearance-none block w-full px-3 py-2 border rounded-md shadow-xs focus:outline-hidden sm:text-sm',
        {
          'pr-10 border-red-300 text-red-800 placeholder-red-300 focus:ring-red-400 focus:border-red-400': showError,
          'border-gray-300 placeholder-gray-400 focus:ring-indigo-500 focus:border-indigo-500': !showError,
          'opacity-50': disabled,
        },
        className
      )
    );

    return (
      <label htmlFor={id} className={labelClassnames}>
        {label && (
          <div className="mb-1 flex justify-between">
            <div className="mr-auto">{label}</div>
          </div>
        )}
        {cornerAdornment && (
          <div className="mb-1 flex justify-between">
            <div className="ml-auto">{cornerAdornment}</div>
          </div>
        )}
        <div className="relative">
          <textarea
            ref={ref}
            className={inputClassnames}
            disabled={disabled}
            id={id}
            name={name}
            onBlur={onBlur}
            onChange={onChange}
            required={required}
            placeholder={placeholder}
            defaultValue={value}
            rows={rows}
            // Temporarily disable the Grammarly support in the <textarea />
            // Since many text area components are rendered in a modal,
            // clicking the Grammarly icon registers as a click and dismisses the modal.
            // https://stackoverflow.com/a/46777787
            data-gramm={false}
            data-enable-grammarly={false}
          />
          {showError && (
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
              <ExclamationCircleIcon className="h-5 w-5 text-red-600" aria-hidden="true" />
            </div>
          )}
        </div>
        {showError && <p className="mt-2 text-sm font-normal text-red-700">{error}</p>}
        {description && <p className="mt-2 text-sm font-normal text-gray-500">{description}</p>}
      </label>
    );
  }
);

InputTextArea.displayName = 'InputTextArea';
