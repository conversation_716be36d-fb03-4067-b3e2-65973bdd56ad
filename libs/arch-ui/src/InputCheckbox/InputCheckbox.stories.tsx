import React from 'react';
import type { Meta, StoryFn } from '@storybook/react-vite';
import { type CheckboxProps, type Color, InputCheckbox } from './InputCheckbox';

const meta: Meta<CheckboxProps> = {
  title: 'Input/InputCheckbox',
  component: InputCheckbox,
};
export default meta;

const colors: Color[] = ['primary', 'success'];

const Template: StoryFn<CheckboxProps> = (props) => (
  <div className="space-y-2">
    {colors.map((color) => (
      <div key={color}>
        <InputCheckbox color={color} label={color} id={color} ref={React.createRef<HTMLInputElement>()} {...props} />
      </div>
    ))}
  </div>
);

export const Standard = {
  args: {
    label: 'Example',
    name: 'example',
    id: 'example',
    required: true,
  },
};

export const Checked = {
  render: Template,
  args: {
    checked: true,
  },
};

export const Indeterminate = {
  args: {
    label: 'Example',
    name: 'example',
    id: 'example',
    isIndeterminate: true,
    ref: React.createRef<HTMLInputElement>(),
  },
};

export const WithError = {
  args: {
    label: 'Example',
    name: 'example',
    id: 'example',
    error: 'This field has an error message!',
  },
};

export const Disabled = {
  render: Template,
  args: {
    disabled: true,
    checked: true,
  },
};
