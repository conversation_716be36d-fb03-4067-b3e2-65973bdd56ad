import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Tooltip, TooltipContent, TooltipTrigger } from './Tooltip';

describe('Tooltip', () => {
  it('renders the trigger element', () => {
    render(
      <Tooltip>
        <TooltipTrigger>
          <button type="button">Hover me</button>
        </TooltipTrigger>
        <TooltipContent>Tooltip Content</TooltipContent>
      </Tooltip>
    );

    expect(screen.getAllByRole('button', { name: 'Hover me' }).length).toBe(2);
  });

  it('renders content when the trigger is hovered', async () => {
    render(
      <Tooltip>
        <TooltipTrigger>
          <button type="button">Hover me</button>
        </TooltipTrigger>
        <TooltipContent>Tooltip Content</TooltipContent>
      </Tooltip>
    );

    await userEvent.hover(screen.getAllByRole('button', { name: 'Hover me' })[0]);

    expect((await screen.findAllByText('Tooltip Content')).length).toBe(2);
  });
});
