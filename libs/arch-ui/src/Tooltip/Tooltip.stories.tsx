import React from 'react';
import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite';
import Button from '../Button';
import { Tooltip, TooltipContent, TooltipTrigger } from './Tooltip';

const meta: Meta<typeof Tooltip> = {
  title: 'Uncategorised/Tooltip',
  component: Tooltip,
};

export default meta;
type Story = StoryObj<typeof Tooltip>;

export const Default: Story = {
  args: {},
  render: (args) => (
    <Tooltip {...args}>
      <TooltipTrigger>
        <Button color="primary" size="md" variant="contained">
          Hover me
        </Button>
      </TooltipTrigger>
      <TooltipContent align="start" side="bottom" collisionPadding={5}>
        This is a tooltip
      </TooltipContent>
    </Tooltip>
  ),
};

export const WithLongText: Story = {
  args: {},
  render: (args) => (
    <Tooltip {...args}>
      <TooltipTrigger>
        <Button color="primary" size="md" variant="contained">
          Hover me
        </Button>
      </TooltipTrigger>
      <TooltipContent align="start" side="bottom" collisionPadding={5}>
        This is the content of the tooltip This is the content of the tooltip This is the content of the tooltip This is
        the content of the tooltip This is the content of the tooltip This is the content of the tooltip This is the
        content of the tooltip
      </TooltipContent>
    </Tooltip>
  ),
};
