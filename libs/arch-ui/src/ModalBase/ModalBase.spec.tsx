import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import ModalBase from '.';

describe('ModalBase', () => {
  describe('ModalBaseHeader', () => {
    it('renders the title section', () => {
      render(
        <ModalBase.Root open onClose={() => {}}>
          <ModalBase.Header onClose={() => {}}>
            <ModalBase.Title>modal title</ModalBase.Title>
          </ModalBase.Header>
        </ModalBase.Root>
      );

      expect(screen.getByText(/modal title/i)).toBeInTheDocument();
    });

    it('renders the subtitle section', () => {
      render(
        <ModalBase.Root open onClose={() => {}}>
          <ModalBase.Header onClose={() => {}}>
            <ModalBase.SubTitle>modal subtitle</ModalBase.SubTitle>
          </ModalBase.Header>
        </ModalBase.Root>
      );

      expect(screen.getByText(/modal subtitle/i)).toBeInTheDocument();
    });

    it('does not render close button', () => {
      render(
        <ModalBase.Root open onClose={() => {}}>
          <ModalBase.Header>
            <ModalBase.Title>modal title</ModalBase.Title>
          </ModalBase.Header>
          <ModalBase.Footer>
            <button type="button">cancel</button>
          </ModalBase.Footer>
        </ModalBase.Root>
      );

      expect(screen.queryByRole('button', { name: /close overlay/i })).not.toBeInTheDocument();
    });

    it('renders secondary button', () => {
      render(
        <ModalBase.Root open onClose={() => {}}>
          <ModalBase.Header rightContent={<button type="button">secondary button</button>}>
            <ModalBase.Title>modal title</ModalBase.Title>
          </ModalBase.Header>
          <ModalBase.Footer>
            <button type="button">cancel</button>
          </ModalBase.Footer>
        </ModalBase.Root>
      );

      expect(screen.getByRole('button', { name: /secondary button/i })).toBeInTheDocument();
    });
  });

  it('renders the content section', () => {
    render(
      <ModalBase.Root open onClose={() => {}}>
        <ModalBase.Header onClose={() => {}} />
        <ModalBase.Content>modal content</ModalBase.Content>
      </ModalBase.Root>
    );

    expect(screen.getByText(/modal content/i)).toBeInTheDocument();
  });

  it('renders the footer section', () => {
    render(
      <ModalBase.Root open onClose={() => {}}>
        <ModalBase.Header onClose={() => {}} />
        <ModalBase.Footer>modal footer</ModalBase.Footer>
      </ModalBase.Root>
    );

    expect(screen.getByText(/modal footer/i)).toBeInTheDocument();
  });

  it('invoke onClose on clicking close modal', async () => {
    const spyOnClose = jest.fn();
    render(
      <ModalBase.Root open onClose={spyOnClose}>
        <ModalBase.Header onClose={spyOnClose} />
        <ModalBase.Footer>modal footer</ModalBase.Footer>
      </ModalBase.Root>
    );

    await userEvent.click(screen.getByRole('button', { name: /close overlay/i }));

    expect(spyOnClose).toBeCalled();
  });

  describe('when disableEscapeKey is enabled', () => {
    describe('when press escape key', () => {
      it('does not invoke onClose', async () => {
        const spyOnClose = jest.fn();
        render(
          <ModalBase.Root open onClose={spyOnClose} disableEscapeKey>
            <ModalBase.Header onClose={spyOnClose} />
            <ModalBase.Footer>modal footer</ModalBase.Footer>
          </ModalBase.Root>
        );

        await userEvent.type(document.body, '{escape}');

        expect(spyOnClose).not.toHaveBeenCalled();
      });
    });
  });
});
