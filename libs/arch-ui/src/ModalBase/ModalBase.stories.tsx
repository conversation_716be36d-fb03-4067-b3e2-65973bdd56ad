import React, { type ComponentProps, useEffect, useState } from 'react';
import type { Meta, StoryObj } from '@storybook/react-vite';
import Button, { IconButton } from '../Button';
import { BellIcon, CogIcon } from '../Icons/solid'; // Adjust import path as needed
import ModalBase from '.';

type Story = StoryObj<typeof ModalBase.Root>;
const meta: Meta<typeof ModalBase.Root> = {
  component: ModalBase.Root,
  subcomponents: ModalBase,
  title: 'Factories/ModalBase',
};
export default meta;

const Template: Story['render'] = ({ children, ...props }) => {
  const [isOpen, setIsOpen] = useState(Boolean(props.open));
  const handleClose: ComponentProps<typeof ModalBase.Root>['onClose'] = () => {
    props.onClose();
    setIsOpen(false);
  };

  useEffect(() => {
    setIsOpen(Boolean(props.open));
  }, [props.open]);

  return (
    <>
      <Button color="primary" variant="contained" size="md" onClick={() => setIsOpen(true)}>
        Open ModalBase
      </Button>

      <ModalBase.Root {...props} open={isOpen} onClose={handleClose}>
        <ModalBase.Header onClose={handleClose}>
          <ModalBase.Title>Occaecat Lorem commodo</ModalBase.Title>
          <ModalBase.SubTitle>Pariatur dolore sit duis ut ex</ModalBase.SubTitle>
        </ModalBase.Header>
        <ModalBase.Content>
          Ex adipisicing aute pariatur do elit ea ea ullamco exercitation officia ea anim aute.
        </ModalBase.Content>
        <ModalBase.Footer>
          <Button color="secondary" variant="outlined" size="md" onClick={() => handleClose()}>
            Cancel
          </Button>
          <Button color="primary" variant="contained" size="md" onClick={() => handleClose()}>
            Submit
          </Button>
        </ModalBase.Footer>
      </ModalBase.Root>
    </>
  );
};

export const Default: Story = {
  render: Template,
};

export const Fullscreen: Story = {
  render: Template,

  args: {
    fullScreen: true,
  },
};

export const Fullwidth: Story = {
  render: Template,

  args: {
    fullWidth: true,
  },
};

export const Maxwidth: Story = {
  render: Template,

  args: {
    maxWidth: 'md',
  },
};

export const DisableEscapeKey: Story = {
  render: Template,

  args: {
    disableEscapeKey: true,
  },
};

export const RoundBorders: Story = {
  render: Template,

  args: {
    roundBorders: true,
  },
};

export const HeaderFooterBorders: Story = {
  render: (args) => {
    const [isOpen, setIsOpen] = useState(Boolean(args.open));

    const handleClose: ComponentProps<typeof ModalBase.Root>['onClose'] = () => {
      args.onClose();
      setIsOpen(false);
    };

    useEffect(() => {
      setIsOpen(Boolean(args.open));
    }, [args.open]);

    return (
      <>
        <Button color="primary" variant="contained" size="md" onClick={() => setIsOpen(true)}>
          Open ModalBase
        </Button>

        <ModalBase.Root {...args} open={isOpen} onClose={handleClose}>
          <ModalBase.Header bottomBorder onClose={handleClose}>
            <ModalBase.Title>Occaecat Lorem commodo</ModalBase.Title>
          </ModalBase.Header>
          <ModalBase.Content>
            Ex adipisicing aute pariatur do elit
            <br />
            ea ea ullamco exercitation officia ea anim aute.
            <br />
            Sed cursus turpis vitae tortor. Cras ultricies mi eu turpis hendrerit fringilla.
          </ModalBase.Content>
          <ModalBase.Footer topBorder>
            <Button color="secondary" variant="outlined" size="md" onClick={() => handleClose()}>
              Cancel
            </Button>
            <Button color="primary" variant="contained" size="md" onClick={() => handleClose()}>
              Submit
            </Button>
          </ModalBase.Footer>
        </ModalBase.Root>
      </>
    );
  },
};

export const WithRightContent: Story = {
  render: (args) => {
    const [isOpen, setIsOpen] = useState(Boolean(args.open));

    const handleClose: ComponentProps<typeof ModalBase.Root>['onClose'] = () => {
      args.onClose();
      setIsOpen(false);
    };

    useEffect(() => {
      setIsOpen(Boolean(args.open));
    }, [args.open]);

    return (
      <>
        <Button color="primary" variant="contained" size="md" onClick={() => setIsOpen(true)}>
          Open ModalBase with Right Content
        </Button>

        <ModalBase.Root {...args} open={isOpen} onClose={handleClose}>
          <ModalBase.Header
            onClose={handleClose}
            rightContent={
              <IconButton
                icon={CogIcon}
                size="sm"
                color="secondary"
                variant="text"
                onClick={() => console.log('Settings clicked')}
              />
            }
          >
            <ModalBase.Title>Modal with Right Content</ModalBase.Title>
            <ModalBase.SubTitle>Example showing rightContent prop usage</ModalBase.SubTitle>
          </ModalBase.Header>
          <ModalBase.Content>
            This modal demonstrates the rightContent prop in the header. Notice the settings icon button positioned
            between the title and the close button.
          </ModalBase.Content>
          <ModalBase.Footer>
            <Button color="secondary" variant="outlined" size="md" onClick={() => handleClose()}>
              Cancel
            </Button>
            <Button color="primary" variant="contained" size="md" onClick={() => handleClose()}>
              Submit
            </Button>
          </ModalBase.Footer>
        </ModalBase.Root>
      </>
    );
  },
};

export const WithMultipleRightContent: Story = {
  render: (args) => {
    const [isOpen, setIsOpen] = useState(Boolean(args.open));

    const handleClose: ComponentProps<typeof ModalBase.Root>['onClose'] = () => {
      args.onClose();
      setIsOpen(false);
    };

    useEffect(() => {
      setIsOpen(Boolean(args.open));
    }, [args.open]);

    return (
      <>
        <Button color="primary" variant="contained" size="md" onClick={() => setIsOpen(true)}>
          Open ModalBase with Multiple Right Content
        </Button>

        <ModalBase.Root {...args} open={isOpen} onClose={handleClose}>
          <ModalBase.Header
            onClose={handleClose}
            rightContent={
              <div className="flex gap-2">
                <IconButton
                  icon={BellIcon}
                  size="sm"
                  color="secondary"
                  variant="text"
                  onClick={() => console.log('Notifications clicked')}
                />
                <IconButton
                  icon={CogIcon}
                  size="sm"
                  color="secondary"
                  variant="text"
                  onClick={() => console.log('Settings clicked')}
                />
              </div>
            }
          >
            <ModalBase.Title>Modal with Multiple Actions</ModalBase.Title>
            <ModalBase.SubTitle>Multiple buttons in the header</ModalBase.SubTitle>
          </ModalBase.Header>
          <ModalBase.Content>
            This modal shows how you can pass multiple elements as rightContent. Notice both the notification and
            settings icons in the header.
          </ModalBase.Content>
          <ModalBase.Footer>
            <Button color="secondary" variant="outlined" size="md" onClick={() => handleClose()}>
              Cancel
            </Button>
            <Button color="primary" variant="contained" size="md" onClick={() => handleClose()}>
              Submit
            </Button>
          </ModalBase.Footer>
        </ModalBase.Root>
      </>
    );
  },
};

export const WithTextRightContent: Story = {
  render: (args) => {
    const [isOpen, setIsOpen] = useState(Boolean(args.open));

    const handleClose: ComponentProps<typeof ModalBase.Root>['onClose'] = () => {
      args.onClose();
      setIsOpen(false);
    };

    useEffect(() => {
      setIsOpen(Boolean(args.open));
    }, [args.open]);

    return (
      <>
        <Button color="primary" variant="contained" size="md" onClick={() => setIsOpen(true)}>
          Open ModalBase with Text Right Content
        </Button>

        <ModalBase.Root {...args} open={isOpen} onClose={handleClose}>
          <ModalBase.Header
            onClose={handleClose}
            rightContent={<span className="text-sm text-gray-500 font-medium">Step 2 of 3</span>}
          >
            <ModalBase.Title>Modal with Text Content</ModalBase.Title>
            <ModalBase.SubTitle>Example showing text as rightContent</ModalBase.SubTitle>
          </ModalBase.Header>
          <ModalBase.Content>
            This modal demonstrates using simple text as rightContent. This could be useful for showing progress,
            status, or other contextual information.
          </ModalBase.Content>
          <ModalBase.Footer>
            <Button color="secondary" variant="outlined" size="md" onClick={() => handleClose()}>
              Cancel
            </Button>
            <Button color="primary" variant="contained" size="md" onClick={() => handleClose()}>
              Submit
            </Button>
          </ModalBase.Footer>
        </ModalBase.Root>
      </>
    );
  },
};
