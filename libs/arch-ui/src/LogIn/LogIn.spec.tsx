import React from 'react';
import { render, screen } from '@testing-library/react';
import { LogIn } from './LogIn';

const logo = (
  <svg fill="none" width="100%" height="100%" viewBox="0 0 28 26">
    <title>Logo</title>
    <path clipRule="evenodd" fill="#6366F1" />
  </svg>
);

describe('<LogIn />', () => {
  it('renders the header', () => {
    const ref = React.createRef<HTMLInputElement>();
    const defaultProps = {
      logo,
      extraProps: { email: { name: 'email', ref }, password: { name: 'password', ref } },
      submitForm: () => {},
      disableSubmitButton: false,
      forgotPasswordLink: <a href="/forgot-password">Forgot password</a>,
      notYouLink: <a href="/auth">Not you?</a>,
      title: 'Log in',
      emailLabel: 'Email',
      passwordLabel: 'Password',
      submitCTA: 'Log in',
    };
    render(<LogIn {...defaultProps} />);

    expect(screen.getByText('Logo')).toBeInTheDocument();
    expect(screen.getByRole('heading', { name: 'Log in' })).toBeInTheDocument();
  });

  it('renders the forgot password and not you url', () => {
    const ref = React.createRef<HTMLInputElement>();
    const defaultProps = {
      logo,
      extraProps: { email: { name: 'email', ref }, password: { name: 'password', ref } },
      submitForm: () => {},
      disableSubmitButton: false,
      forgotPasswordLink: <a href="/forgot-password">Forgot password</a>,
      notYouLink: <a href="/auth">Not you?</a>,
      title: 'Log in',
      emailLabel: 'Email',
      passwordLabel: 'Password',
      submitCTA: 'Log in',
    };
    render(<LogIn {...defaultProps} />);

    expect(screen.getByRole('link', { name: 'Forgot password' })).toHaveAttribute('href', '/forgot-password');
    expect(screen.getByRole('link', { name: 'Not you?' })).toHaveAttribute('href', '/auth');
  });

  it('renders the email and password fields with given value', () => {
    const ref = React.createRef<HTMLInputElement>();
    const defaultProps = {
      logo,
      extraProps: {
        email: { name: 'email', value: '<EMAIL>', ref },
        password: { name: 'password', value: 'password', ref },
      },
      submitForm: () => {},
      disableSubmitButton: false,
      forgotPasswordLink: <a href="/forgot-password">Forgot password</a>,
      notYouLink: <a href="/auth">Not you?</a>,
      title: 'Log in',
      emailLabel: 'Email',
      passwordLabel: 'Password',
      submitCTA: 'Log in',
    };
    render(<LogIn {...defaultProps} />);

    expect(screen.getByLabelText('EmailNot you?')).toHaveAttribute('value', '<EMAIL>');
    expect(screen.getByLabelText('Password')).toHaveAttribute('value', 'password');
  });

  it('renders password field with given error', () => {
    const ref = React.createRef<HTMLInputElement>();
    const defaultProps = {
      logo,
      extraProps: {
        email: { name: 'email', value: '<EMAIL>', ref },
        password: { name: 'password', value: 'pass', ref },
      },
      submitForm: () => {},
      disableSubmitButton: false,
      forgotPasswordLink: <a href="/forgot-password">Forgot password</a>,
      notYouLink: <a href="/auth">Not you?</a>,
      passwordError: 'password error',
      title: 'Log in',
      emailLabel: 'Email',
      passwordLabel: 'Password',
      submitCTA: 'Log in',
    };
    render(<LogIn {...defaultProps} />);

    expect(screen.getByText('password error')).toBeInTheDocument();
  });

  it('renders the submit feedback message', () => {
    const ref = React.createRef<HTMLInputElement>();
    const defaultProps = {
      logo,
      extraProps: {
        email: { name: 'email', value: '<EMAIL>', ref },
        password: { name: 'password', value: 'pass', ref },
      },
      submitForm: () => {},
      disableSubmitButton: false,
      forgotPasswordLink: <a href="/forgot-password">Forgot password</a>,
      notYouLink: <a href="/auth">Not you?</a>,
      submitFeedback: 'Incorrect password',
      title: 'Log in',
      emailLabel: 'Email',
      passwordLabel: 'Password',
      submitCTA: 'Log in',
    };
    render(<LogIn {...defaultProps} />);

    expect(screen.getByText('Incorrect password')).toBeInTheDocument();
  });
});
