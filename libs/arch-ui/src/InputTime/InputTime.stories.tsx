import React from 'react';
import type { Meta } from '@storybook/react-vite';
import { InputTime } from './InputTime';

export default {
  title: 'Input/InputTime',
  component: InputTime,
} as Meta<typeof InputTime>;

export const Default = {
  args: { label: 'Time', name: 'time' },
};

export const WithValue = {
  args: { ...Default.args, value: '12:30' },
};

export const WithErrors = {
  args: { ...Default.args, error: 'error' },
};

export const Disabled = {
  args: { ...Default.args, disabled: true },
};

export const WithCornerAdornment = {
  args: { ...Default.args, cornerAdornment: <>Corner Adornment</> },
};

export const WithFullWidth = {
  args: {
    ...Default.args,
    fullWidth: true,
  },
};
