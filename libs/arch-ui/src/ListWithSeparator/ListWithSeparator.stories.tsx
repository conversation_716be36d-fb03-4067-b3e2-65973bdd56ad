import React from 'react';
import type { Meta } from '@storybook/react-vite';
import { ListWithSeparator, type ListWithSeparatorProps } from './ListWithSeparator';

export default {
  title: 'Uncategorised/ListWithSeparator',
  component: ListWithSeparator,
} as Meta<ListWithSeparatorProps>;

export const MultipleChildren = {
  args: {
    children: [1, <div key="2">2</div>, '3', <div key="123">4</div>, true, false, undefined],
  },
};

export const LongChildren = {
  args: {
    children: [
      'This is a long text',
      'This one is long text too',
      'This one is even more long than the other',
      true,
      false,
      undefined,
    ],
  },
};

export const SingleChild = {
  args: { children: [<div key="1">1</div>] },
};
