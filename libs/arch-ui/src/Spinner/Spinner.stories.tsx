import React from 'react';
import type { Meta } from '@storybook/react-vite';
import Spinner from './index';
import type { SpinnerProps, SpinnerSize } from './Spinner';

export default {
  title: 'Status/Spinner',
  component: Spinner,
} as Meta<SpinnerProps>;

export const Default = {
  render: () => <Spinner />,
  args: {},
};

export const Sizes = () => {
  const sizes: SpinnerSize[] = ['sm', 'md', 'lg', 'xl'];

  return (
    <div className="flex flex-col gap-4">
      {sizes.map((size) => (
        <div key={size}>
          <p>Size: {size}</p>
          <Spinner size={size} />
        </div>
      ))}
    </div>
  );
};

export const Colors = () => {
  return (
    <div className="flex flex-col gap-4">
      <div>
        <p>Color: Default</p>
        <Spinner color="default" />
      </div>
      <div>
        <p>Color: Inverse</p>
        <span className="block bg-brand-bold">
          <Spinner color="inverse" />
        </span>
      </div>
    </div>
  );
};
