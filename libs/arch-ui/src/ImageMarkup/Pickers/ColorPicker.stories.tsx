import React, { useState } from 'react';
import type { Meta, StoryFn } from '@storybook/react-vite';
import { type Color, colors } from '../colors';
import { ColorPicker as ColorPickerElement } from './ColorPicker';

export default {
  title: 'Uncategorised/Editor Markup/Pickers/Color Picker',
  component: ColorPickerElement,
  argTypes: {
    onSelect: { action: 'on select' },
  },
} as Meta<typeof ColorPickerElement>;

const Template: StoryFn<typeof ColorPickerElement> = (props) => {
  const [selectedColor, setSelectedColor] = useState<Color>(colors[0]);

  return (
    <ColorPickerElement
      {...props}
      selectedColor={selectedColor}
      onSelect={(color) => {
        props.onSelect(color);
        setSelectedColor(color);
      }}
    />
  );
};

export const ColorPicker = {
  render: Template,
};
