import React, { type FC, type MouseEventHand<PERSON>, useState } from 'react';
import { RadioGroup } from '@headlessui/react';
import { Popover } from 'radix-ui';
import { type Color, colors as defaultColors } from '../colors';
import { Color as ColorTool } from '../Tools';

export type ColorPickerProps = {
  /**
   * The list of colors that the picker will be able to select from.
   * They should match colors known by Tailwind
   */
  colors?: Color[];
  /**
   * Color from the colors list. The color of the icon
   */
  selectedColor: Color;
  /**
   * Handler attached to the ColorTool button
   */
  onClick?: MouseEventHandler;
  /**
   * Handler to run when the color is selected from the picker
   */
  onSelect: (color: Color) => void;
};

export const ColorPicker: FC<ColorPickerProps> = ({ colors = defaultColors, selectedColor, onSelect, ...props }) => {
  const [open, setOpen] = useState(false);
  if (!colors.includes(selectedColor)) throw Error('The selected color is not defined in colors');

  return (
    <Popover.Root open={open} onOpenChange={setOpen}>
      <Popover.Trigger>
        <ColorTool color={selectedColor} selected {...props} />
      </Popover.Trigger>
      <Popover.Content>
        <RadioGroup
          className="p-2 flex flex-col gap-2 justify-center rounded-md shadow-md bg-white sm:flex-row sm:items-center"
          value={selectedColor}
          onChange={(color: Color) => {
            onSelect(color);
            setOpen(false);
          }}
        >
          {colors.map((color) => {
            const isSelectedOption = selectedColor === color;

            return (
              <RadioGroup.Option key={color} value={color}>
                <ColorTool color={color} variant="text" selected={isSelectedOption} />
                <RadioGroup.Label as="p" className="sr-only">
                  {color}
                </RadioGroup.Label>
              </RadioGroup.Option>
            );
          })}
        </RadioGroup>
      </Popover.Content>
    </Popover.Root>
  );
};
