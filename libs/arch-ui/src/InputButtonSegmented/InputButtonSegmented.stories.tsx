import React from 'react';
import type { Meta, StoryObj } from '@storybook/react-vite';
import { InputButtonSegmented, type InputButtonSegmentedProps } from './InputButtonSegmented';

const meta: Meta<InputButtonSegmentedProps> = {
  title: 'Input/InputButtonSegmented',
  component: InputButtonSegmented,
};

export default meta;
type Story = StoryObj<InputButtonSegmentedProps>;

const options = [
  { value: '1', label: 'Option 1' },
  { value: '2', label: 'Option 2' },
  { value: '3', label: 'Option 3' },
  { value: '4', label: 'Option 4' },
  { value: '5', label: 'Option 5' },
  { value: '6', label: 'Option 6' },
];

export const SingleSelect: Story = {
  render: () => <InputButtonSegmented options={options} value="2" />,
};

export const MultiSelect: Story = {
  render: () => <InputButtonSegmented options={options} multiple value={['1', '2', '4']} />,
};

export const AllColors: Story = {
  render: () => (
    <>
      <InputButtonSegmented multiple value={['1', '2', '6']} options={options} color="primary" />
      <div className="mb-4" />
      <InputButtonSegmented multiple value={['1', '2', '6']} options={options} color="secondary" />
    </>
  ),
};

export const Wide: Story = {
  render: () => <InputButtonSegmented options={options} value="2" isWide />,
};

export const FixedWidth: Story = {
  render: () => (
    <InputButtonSegmented multiple value={['1', '6']} options={options} color="secondary" buttonClassName="w-32" />
  ),
};
