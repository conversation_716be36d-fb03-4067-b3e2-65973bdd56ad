import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { InputButtonSegmented } from './InputButtonSegmented';

describe('InputButtonSegmented', () => {
  describe('when multiple is true', () => {
    describe('when no options are selected', () => {
      it('renders options without selection', async () => {
        render(
          <InputButtonSegmented
            multiple
            options={[
              { label: 'One', value: 'one' },
              { label: 'Two', value: 'two' },
              { label: 'Three', value: 'three' },
            ]}
            value={[]}
            onChange={jest.fn()}
          />
        );

        expect(await screen.findAllByRole('option', { selected: false })).toHaveLength(3);
      });
    });

    describe('when some options are selected', () => {
      it('renders with selected items', async () => {
        render(
          <InputButtonSegmented
            multiple
            options={[
              { label: 'One', value: 'one' },
              { label: 'Two', value: 'two' },
              { label: 'Three', value: 'three' },
            ]}
            value={['one', 'three']}
            onChange={jest.fn()}
          />
        );

        expect(await screen.findAllByRole('option', { selected: true })).toHaveLength(2);
      });
    });

    describe('when an unselected option is clicked', () => {
      it('selects option and calls onChange & onSelect', async () => {
        const onChange = jest.fn();
        const onSelect = jest.fn();
        const onUnselect = jest.fn();

        render(
          <InputButtonSegmented
            multiple
            options={[
              { label: 'One', value: 'one' },
              { label: 'Two', value: 'two' },
              { label: 'Three', value: 'three' },
            ]}
            value={['one']}
            onChange={onChange}
            onSelect={onSelect}
            onUnselect={onUnselect}
          />
        );

        expect(onChange).toHaveBeenCalledTimes(0);
        expect(onSelect).toHaveBeenCalledTimes(0);
        expect(onUnselect).toHaveBeenCalledTimes(0);

        await userEvent.click(screen.getByRole('option', { name: 'Two', selected: false }));

        expect(await screen.findByRole('option', { name: 'Two', selected: true })).toBeInTheDocument();
        expect(onChange).toHaveBeenCalledTimes(1);
        expect(onChange).toHaveBeenCalledWith(['one', 'two']);
        expect(onSelect).toHaveBeenCalledTimes(1);
        expect(onUnselect).toHaveBeenCalledTimes(0);
      });
    });

    describe('when a selected option is clicked', () => {
      it('unselects option and calls onChange & onUnselect', async () => {
        const onChange = jest.fn();
        const onSelect = jest.fn();
        const onUnselect = jest.fn();

        render(
          <InputButtonSegmented
            multiple
            options={[
              { label: 'One', value: 'one' },
              { label: 'Two', value: 'two' },
              { label: 'Three', value: 'three' },
            ]}
            value={['one', 'two']}
            onChange={onChange}
            onSelect={onSelect}
            onUnselect={onUnselect}
          />
        );

        expect(onChange).toHaveBeenCalledTimes(0);
        expect(onSelect).toHaveBeenCalledTimes(0);
        expect(onUnselect).toHaveBeenCalledTimes(0);

        await userEvent.click(screen.getByRole('option', { name: 'Two', selected: true }));

        expect(await screen.findByRole('option', { name: 'Two', selected: false })).toBeInTheDocument();
        expect(onChange).toHaveBeenCalledTimes(1);
        expect(onChange).toHaveBeenCalledWith(['one']);
        expect(onSelect).toHaveBeenCalledTimes(0);
        expect(onUnselect).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe('when multiple is false', () => {
    describe('when value is not set', () => {
      it('renders options without selection', async () => {
        render(
          <InputButtonSegmented
            options={[
              { label: 'One', value: 'one' },
              { label: 'Two', value: 'two' },
              { label: 'Three', value: 'three' },
            ]}
          />
        );

        expect(await screen.findAllByRole('option', { selected: false })).toHaveLength(3);
      });
    });

    describe('when value is set', () => {
      it('renders with selected items', async () => {
        render(
          <InputButtonSegmented
            options={[
              { label: 'One', value: 'one' },
              { label: 'Two', value: 'two' },
              { label: 'Three', value: 'three' },
            ]}
            value={'three'}
            onChange={jest.fn()}
          />
        );

        expect(await screen.findAllByRole('option', { selected: true })).toHaveLength(1);
      });
    });

    describe('when an unselected option is clicked', () => {
      it('selects option and calls onSelect', async () => {
        const onSelect = jest.fn();

        render(
          <InputButtonSegmented
            multiple
            options={[
              { label: 'One', value: 'one' },
              { label: 'Two', value: 'two' },
              { label: 'Three', value: 'three' },
            ]}
            value="one"
            onSelect={onSelect}
          />
        );

        expect(onSelect).toHaveBeenCalledTimes(0);

        await userEvent.click(screen.getByRole('option', { name: 'Two', selected: false }));

        expect(await screen.findByRole('option', { name: 'Two', selected: true })).toBeInTheDocument();
        expect(onSelect).toHaveBeenCalledTimes(1);
        expect(onSelect).toHaveBeenCalledWith('two');
      });
    });

    describe('when a selected option is clicked', () => {
      it('option stays selected with no change', async () => {
        const onSelect = jest.fn();

        render(
          <InputButtonSegmented
            options={[
              { label: 'One', value: 'one' },
              { label: 'Two', value: 'two' },
              { label: 'Three', value: 'three' },
            ]}
            value="two"
            onSelect={onSelect}
          />
        );

        expect(onSelect).toHaveBeenCalledTimes(0);

        await userEvent.click(screen.getByRole('option', { name: 'Two', selected: true }));

        expect(await screen.findByRole('option', { name: 'Two', selected: true })).toBeInTheDocument();
        expect(onSelect).toHaveBeenCalledTimes(0);
      });
    });
  });
});
