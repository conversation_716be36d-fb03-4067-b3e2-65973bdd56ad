import React, { useEffect, useMemo } from 'react';
import { useImmutableSet, useLatest, useUpdateEffect } from '@shape-construction/hooks';
import classNames from 'clsx';
import { type Color, getButtonClasses, getContainerClasses } from './classes';

export type InputButtonSegmentedProps = {
  options: { label: string; value: string }[];
  multiple?: boolean;
  color?: Color;
  value?: string | string[];
  onSelect?: (value: string) => void;
  onUnselect?: (value: string) => void;
  onChange?: (values: string[]) => void;
  isWide?: boolean;
  buttonClassName?: string;
  disabled?: boolean;
};

const toValues = (value: string | string[] | undefined): string[] =>
  Array.isArray(value) ? value : [value as string].filter(Boolean);

export const InputButtonSegmented: React.FC<InputButtonSegmentedProps> = ({
  options,
  multiple = false,
  color = 'primary',
  value,
  onChange,
  onSelect,
  onUnselect: onRemove,
  isWide = false,
  buttonClassName,
  disabled = false,
}) => {
  const values = useMemo(() => toValues(value), [value]);
  const [selected, { toggle, set: setSelected, has: isSelected }] = useImmutableSet(new Set(values));
  const onChangeRef = useLatest(onChange);
  const selectedRef = useLatest(selected);

  useEffect(() => {
    const isEqual = values.length === selectedRef.current.size && values.every((v) => selectedRef.current.has(v));
    if (!isEqual) setSelected(new Set(values));
  }, [setSelected, selectedRef, values]);

  useUpdateEffect(() => {
    onChangeRef.current?.(Array.from(selected));
  }, [onChangeRef, selected]);

  const onButtonClick = (val: string) => {
    if (multiple) {
      if (isSelected(val)) {
        onRemove?.(val);
      } else {
        onSelect?.(val);
      }
      toggle(val);
    } else {
      if (!isSelected(val)) {
        onSelect?.(val);
        setSelected(new Set([val]));
      }
    }
  };

  return (
    <div
      className={classNames('rounded-sm flex gap-px w-fit', { 'gap-x-1': isWide }, getContainerClasses(color))}
      role="listbox"
      aria-multiselectable={multiple}
    >
      {options.map((option) => (
        <button
          key={option.value}
          type="button"
          role="option"
          aria-selected={isSelected(option.value)}
          disabled={disabled}
          className={classNames(
            'text-sm px-2 py-2 first:rounded-l last:rounded-r',
            buttonClassName,
            getButtonClasses(color, isSelected(option.value)),
            { 'pointer-events-none': disabled }
          )}
          onClick={() => onButtonClick(option.value)}
        >
          {option.label}
        </button>
      ))}
    </div>
  );
};
