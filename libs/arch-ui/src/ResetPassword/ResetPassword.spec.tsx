import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ResetPassword } from './ResetPassword';

describe('<ResetPassword />', () => {
  const defaultProps = {
    title: 'Reset Password',
    subtitle: (
      <p>
        Enter verification code sent to <span><EMAIL></span>
      </p>
    ),
    verificationCodePlaceholder: 'Enter code',
    passwordLabel: 'New Password',
    confirmPasswordLabel: 'Confirm Password',
    changePasswordLabel: 'Change Password',
    haventReceivedCodeLabel: "Haven't received code?",
    resendVerificationCodeLabel: 'Resend Code',
    havingTroubleLabel: 'Having trouble?',
    contactUsLink: <a href="/contact">Contact us</a>,
    logInLink: <a href="/login">Back to login</a>,
  };

  it('renders form with all required fields and buttons', () => {
    render(<ResetPassword {...defaultProps} />);

    expect(screen.getByText('Reset Password')).toBeInTheDocument();
    expect(screen.getByText('Enter verification code sent to')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter code')).toBeInTheDocument();
    expect(screen.getByLabelText('New Password')).toBeInTheDocument();
    expect(screen.getByLabelText('Confirm Password')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Change Password' })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Resend Code' })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: 'Contact us' })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: 'Back to login' })).toBeInTheDocument();
  });

  it('calls onSubmit when form is submitted', async () => {
    const onSubmit = jest.fn();
    render(<ResetPassword {...defaultProps} onSubmit={onSubmit} />);

    const submitButton = screen.getByRole('button', { name: 'Change Password' });
    await userEvent.click(submitButton);

    expect(onSubmit).toHaveBeenCalled();
  });

  it('calls onResendCode when resend button is clicked', async () => {
    const onResendCode = jest.fn();
    render(<ResetPassword {...defaultProps} onResendCode={onResendCode} />);

    await userEvent.click(screen.getByRole('button', { name: 'Resend Code' }));

    expect(onResendCode).toHaveBeenCalled();
  });

  it('renders submitFeedback when provided', () => {
    const feedbackMessage = 'Error occurred while resetting password';
    render(<ResetPassword {...defaultProps} submitFeedback={<div role="alert">{feedbackMessage}</div>} />);

    expect(screen.getByRole('alert')).toHaveTextContent(feedbackMessage);
  });
});
