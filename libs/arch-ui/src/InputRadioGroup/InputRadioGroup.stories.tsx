import React, { useState } from 'react';
import type { Meta, StoryObj } from '@storybook/react-vite';
import { action } from 'storybook/actions';
import Badge from '../Badge';
import FileExtensionIcon from '../FileExtensionIcon';
import { ArrowDownTrayIcon } from '../Icons/solid';
import { InputRadioGroup, type InputRadioGroupProps } from './InputRadioGroup';

const meta: Meta<InputRadioGroupProps> = {
  title: 'Input/InputRadioGroup',
  component: InputRadioGroup,
};

export default meta;
type Story = StoryObj<InputRadioGroupProps>;

// moving options array away from the Story component prevents the issue
// where a Storybook freezes when processing React nodes passed as props

const optionsAlpha = [
  { label: 'example', value: 'example', description: 'This is a description' },
  {
    label: 'another example',
    value: 'another example',
    description: 'And this is another one',
  },
];

const optionsBeta = [
  {
    value: 'csv',
    label: 'CSV',
    icon: <FileExtensionIcon extension="csv" width="20" />,
  },
  {
    value: 'xlsx',
    label: 'XLSX',
    icon: <FileExtensionIcon extension="xlsx" width="20" />,
  },
  {
    value: 'pdf',
    label: 'PDF',
    icon: <FileExtensionIcon extension="pdf" width="20" />,
  },
];

const optionsGamma = [
  {
    value: 'csv',
    label: 'CSV',
    badge: <Badge label="PRO" />,
    icon: <FileExtensionIcon extension="csv" width="20" />,
    disabled: true,
  },
  {
    value: 'xlsx',
    label: 'XLSX',
    badge: <ArrowDownTrayIcon className="w-4 h-4" />,
    icon: <FileExtensionIcon extension="xlsx" width="20" />,
  },
  {
    value: 'pdf',
    label: 'PDF',
    badge: <FileExtensionIcon extension="pdf" width="15" />,
    icon: <FileExtensionIcon extension="pdf" width="20" />,
  },
];

export const Default: Story = {
  render: () => {
    const [value, setValue] = useState(optionsAlpha[0].value);

    function changeValue(name: string, val: string) {
      setValue(val);
    }

    return (
      <InputRadioGroup
        name="field"
        options={optionsAlpha}
        value={value}
        setFieldValue={(name, val) => {
          changeValue(name, val);
          action('setFieldValue')({ name, value: val });
        }}
      />
    );
  },
};

export const WithErrors: Story = {
  name: 'Input radio group with errors',
  render: () => (
    <InputRadioGroup
      name="field"
      options={optionsAlpha}
      value={optionsAlpha[0].value}
      onChange={(res) => action('onChange')(res)}
      setFieldValue={() => {}}
      error="error"
    />
  ),
};

export const WithLabelAndIcon: Story = {
  name: 'Input radio group with label and icon',
  render: () => {
    const [value, setValue] = useState(optionsBeta[0].value);

    const changeValue = (_: any, val: string) => {
      setValue(val);
    };

    return (
      <div className="max-w-xl mx-auto ml-0">
        <InputRadioGroup
          name="field"
          options={optionsBeta}
          value={value}
          label="Choose a format:"
          setFieldValue={changeValue}
        />
      </div>
    );
  },
};

export const WithLabelIconAndBadge: Story = {
  name: 'Input radio group with icon, label and badge',
  render: () => {
    const [value, setValue] = useState(optionsGamma[2].value);

    const changeValue = (_: any, val: string) => {
      setValue(val);
    };

    return (
      <div className="max-w-xl mx-auto ml-0">
        <InputRadioGroup
          name="field"
          options={optionsGamma}
          value={value}
          label="Choose a format:"
          setFieldValue={changeValue}
        />
      </div>
    );
  },
};

export const Inline: Story = {
  name: 'Input radio group with icon, label and badge',
  render: () => {
    const [value, setValue] = useState(optionsGamma[2].value);

    const changeValue = (_: any, val: string) => {
      setValue(val);
    };

    return (
      <div className="max-w-xl mx-auto ml-0">
        <InputRadioGroup
          name="field"
          options={optionsGamma}
          value={value}
          label="Choose a format:"
          setFieldValue={changeValue}
          alignment="inline"
        />
      </div>
    );
  },
};
