import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import Badge from '../Badge';
import FileExtensionIcon from '../FileExtensionIcon';
import { InputRadioGroup, type InputRadioGroupOptionProps } from './InputRadioGroup';

const options: InputRadioGroupOptionProps[] = [
  { label: 'This is a label', value: 'example', description: 'This is a description' },
  {
    label: 'This is another label',
    value: 'another example',
    description: 'And this is another one',
  },
];

describe('InputRadioGroup', () => {
  it('renders with label', () => {
    render(
      <InputRadioGroup
        name="example"
        label="Example of Label"
        value="example"
        options={options}
        setFieldValue={() => {}}
      />
    );

    expect(screen.getByLabelText('Example of Label')).toBeInTheDocument();
  });

  it('renders with options', () => {
    render(
      <InputRadioGroup
        name="example"
        label="Example"
        value="another example"
        options={options}
        setFieldValue={() => {}}
      />
    );

    expect(screen.getByText('This is a label')).toBeInTheDocument();
    expect(screen.getByText('This is another label')).toBeInTheDocument();
  });

  it('renders with icons', () => {
    const optionsWithIcon: InputRadioGroupOptionProps[] = [
      {
        label: 'This is a label',
        value: 'value',
        icon: <FileExtensionIcon extension="csv" width="20" data-testid="csv-icon" />,
      },
      {
        label: 'This is another label',
        value: 'another value',
        icon: <FileExtensionIcon extension="pdf" width="20" data-testid="pdf-icon" />,
      },
    ];

    render(<InputRadioGroup name="example" value="value" options={optionsWithIcon} setFieldValue={() => {}} />);

    expect(screen.queryByTestId('csv-icon')).toBeInTheDocument();
    expect(screen.queryByTestId('pdf-icon')).toBeInTheDocument();
  });

  it('renders with an icon and a badge', () => {
    const optionsWithIconAndBadge: InputRadioGroupOptionProps[] = [
      {
        label: 'Export to CSV',
        value: 'csv',
        badge: <Badge label="PRO" />,
        icon: <FileExtensionIcon extension="csv" width="20" data-testid="csv-icon" />,
      },
      {
        label: 'Export to PDF',
        value: 'pdf',
        badge: <Badge label="FREE" />,
        icon: <FileExtensionIcon extension="pdf" width="20" data-testid="pdf-icon" />,
      },
    ];

    render(<InputRadioGroup name="example" value="value" options={optionsWithIconAndBadge} setFieldValue={() => {}} />);

    expect(screen.getByText('Export to CSV')).toBeInTheDocument();
    expect(screen.getByText('Export to PDF')).toBeInTheDocument();
    expect(screen.getByText('PRO')).toBeInTheDocument();
    expect(screen.getByText('FREE')).toBeInTheDocument();
    expect(screen.queryByTestId('csv-icon')).toBeInTheDocument();
    expect(screen.queryByTestId('pdf-icon')).toBeInTheDocument();
  });

  it('renders with an error message', () => {
    render(
      <InputRadioGroup
        name="example"
        label="Example"
        value="another example"
        options={options}
        setFieldValue={() => {}}
        error="This is not valid!"
      />
    );

    expect(screen.getByText('This is not valid!')).toBeInTheDocument();
  });

  it('does not render with an error message if untouched', () => {
    render(
      <InputRadioGroup
        name="example"
        label="Example"
        value="another example"
        options={options}
        setFieldValue={() => {}}
        error="This is not valid!"
        touched={false}
      />
    );

    expect(screen.queryByText('This is not valid!')).not.toBeInTheDocument();
  });

  it('calls setFieldValue when value changes', async () => {
    const mockedFieldValue = jest.fn();
    render(
      <InputRadioGroup
        name="example"
        label="Example of Label"
        value="another example"
        options={options}
        setFieldValue={mockedFieldValue}
        error="This is not valid!"
      />
    );
    const firstOption = screen.getByLabelText('This is a label');
    const secondOption = screen.getByLabelText('This is another label');

    await userEvent.click(secondOption);
    // value didn't change from the previous one, passed by props
    expect(mockedFieldValue).toHaveBeenCalledTimes(0);

    await userEvent.click(firstOption);

    // value changed from initial
    expect(mockedFieldValue).toHaveBeenCalledTimes(1);
    expect(mockedFieldValue).toHaveBeenCalledWith('example', 'example');
  });

  it('does not call setFieldValue when disabled', async () => {
    const optionsDisabled: InputRadioGroupOptionProps[] = [
      {
        label: 'Export to CSV',
        value: 'csv',
        badge: <Badge label="PRO" />,
        icon: <FileExtensionIcon extension="csv" width="20" data-testid="csv-icon" />,
        disabled: true,
      },
      {
        label: 'Export to PDF',
        value: 'pdf',
        badge: <Badge label="FREE" />,
        icon: <FileExtensionIcon extension="pdf" width="20" data-testid="pdf-icon" />,
      },
    ];

    const mockedFieldValue = jest.fn();
    render(
      <InputRadioGroup
        name="xlsx"
        label="Export to XLSX"
        value="xlsx"
        options={optionsDisabled}
        setFieldValue={mockedFieldValue}
        error="This is not valid!"
      />
    );
    const firstOption = screen.getByLabelText('Export to CSV');
    const secondOption = screen.getByLabelText('Export to PDF');

    await userEvent.click(secondOption);

    // value changed from the previous one, passed by props
    expect(mockedFieldValue).toHaveBeenCalledTimes(1);
    expect(mockedFieldValue).toHaveBeenCalledWith('xlsx', 'pdf');

    await userEvent.click(firstOption);

    // value did not change as the option is disabled
    expect(mockedFieldValue).toHaveBeenCalledTimes(1);
    expect(mockedFieldValue).toHaveBeenCalledWith('xlsx', 'pdf');
  });

  it('sets the radio value using ref', () => {
    const ref = React.createRef<HTMLInputElement>();
    render(<InputRadioGroup ref={ref} name="example" label="Example" options={options} />);
    if (ref.current) {
      ref.current.value = 'example';
    }
    expect(screen.getByLabelText('Example')).toHaveValue('example');
  });
});
