import React from 'react';
import type { <PERSON>a, StoryObj } from '@storybook/react-vite';
import { ChevronRightIcon, UserGroupIcon } from '../Icons/solid';
import * as List from './List';

const meta: Meta<typeof List.Root> = {
  title: 'Layout and Organisation/Stack/List',
  component: List.Root,
};

export default meta;

type Story = StoryObj<typeof List.Root>;

const list = ['Item 1', 'Item 2', 'Item 3', 'Item 4', 'Item 5', 'Item 6', 'Item 7'];

export const Default: Story = {
  render: (props) => {
    return (
      <List.Root {...props}>
        {list.map((item) => (
          <List.Item key={item}>
            <List.Content>
              <List.Title>{item}</List.Title>
              <List.SupportingText>Item supporting</List.SupportingText>
            </List.Content>
          </List.Item>
        ))}
      </List.Root>
    );
  },
};

export const WithDivider: Story = {
  args: { divider: true },
  render: (props) => {
    return (
      <List.Root {...props}>
        {list.map((item) => (
          <List.Item key={item}>
            <UserGroupIcon />
            <List.Content>
              <List.Title>{item}</List.Title>
              <List.SupportingText>Item supporting</List.SupportingText>
            </List.Content>
            <ChevronRightIcon />
          </List.Item>
        ))}
      </List.Root>
    );
  },
};

export const CustomPressble: Story = {
  render: (props) => {
    return (
      <List.Root {...props}>
        {list.map((item) => (
          <List.Item key={item} asChild>
            <button type="button">
              <List.Content>
                <List.Title>{item}</List.Title>
                <List.SupportingText>Item supporting</List.SupportingText>
              </List.Content>
            </button>
          </List.Item>
        ))}
      </List.Root>
    );
  },
};

export const WithIcon: Story = {
  render: (props) => {
    return (
      <List.Root {...props}>
        {list.map((item) => (
          <List.Item key={item}>
            <UserGroupIcon />
            <List.Content>
              <List.Title>{item}</List.Title>
              <List.SupportingText>Item supporting</List.SupportingText>
            </List.Content>
            <ChevronRightIcon />
          </List.Item>
        ))}
      </List.Root>
    );
  },
};
