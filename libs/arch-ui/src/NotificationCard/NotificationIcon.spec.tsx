import React from 'react';
import { render, screen } from '@testing-library/react';
import { NOTIFICATION_ICON_COLORS, NotificationIcon, type NotificationIconColorType } from './NotificationIcon';

const MockIcon = () => <svg />;

describe('NotificationIcon Component', () => {
  it.each(Object.entries(NOTIFICATION_ICON_COLORS))('renders with color %s correctly', (color) => {
    render(<NotificationIcon color={color as NotificationIconColorType} icon={MockIcon} />);

    const figureElement = screen.getByRole('figure');

    expect(figureElement).toHaveClass(NOTIFICATION_ICON_COLORS[color as NotificationIconColorType]);
  });
});
