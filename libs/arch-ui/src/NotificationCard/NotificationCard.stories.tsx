import React from 'react';
import type { Meta } from '@storybook/react-vite';
import { FolderIcon } from '../Icons/solid';
import NotificationCard from './NotificationCard';

const meta: Meta<typeof NotificationCard> = {
  title: 'Uncategorised/NotificationCard',
  component: NotificationCard,
  argTypes: {
    handleClick: { action: 'clicked' },
    read: { control: 'boolean' },
    icon: { control: undefined },
    color: {
      control: 'select',
      options: ['indigo', 'green', 'red', 'yellow', 'pink'],
    },
  },
};
export default meta;

export const Default = {
  args: {
    avatarUrl: 'https://via.placeholder.com/150',
    actorName: '<PERSON>',
    body: 'This is a notification body.',
    bodyExtra: 'Some quote here.',
    notificationId: '1',
    read: false,
    title: 'Notification Title',
    icon: FolderIcon,
    color: 'indigo',
  },
};

export const ReadNotification = {
  args: {
    ...Default.args,
    read: true,
  },
};

export const UnreadNotification = {
  args: {
    ...Default.args,
    read: false,
  },
};
