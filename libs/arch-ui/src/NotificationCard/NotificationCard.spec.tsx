import React from 'react';
import { render, screen } from '@testing-library/react';

import '@testing-library/jest-dom';
import userEvent from '@testing-library/user-event';
import NotificationCard from './NotificationCard';
import type { NotificationIconColorType } from './NotificationIcon';
import { FolderIcon } from 'Icons/solid';

describe('NotificationCard', () => {
  const mockHandleClick = jest.fn();
  const mockHandleMarkRead = jest.fn();
  const defaultProps = {
    notificationId: '1',
    read: false,
    handleClick: mockHandleClick,
    handleMarkRead: mockHandleMarkRead,
    color: 'indigo' as NotificationIconColorType,
    icon: FolderIcon,
  };

  it('displays title, body, and bodyExtra when provided', () => {
    const props = {
      ...defaultProps,
      title: 'Test Title',
      body: 'Test Body',
      bodyExtra: 'Test Body Extra',
    };

    render(<NotificationCard {...props} />);

    expect(screen.getByText('Test Title')).toBeInTheDocument();
    expect(screen.getByText('Test Body')).toBeInTheDocument();
    expect(screen.getByText('Test Body Extra')).toBeInTheDocument();
  });

  it('calls handleClick when the card is clicked', async () => {
    render(<NotificationCard {...defaultProps} />);

    await userEvent.click(screen.getByRole('button', { name: 'notification' }));

    expect(mockHandleClick).toHaveBeenCalled();
  });

  it('renders Avatar with correct props', () => {
    const actorProps = {
      ...defaultProps,
      avatarUrl: 'test-url',
      actorName: 'Test Name',
    };

    render(<NotificationCard {...actorProps} />);

    expect(screen.getByRole('img')).toBeInTheDocument();
    expect(screen.getByRole('img')).toHaveAttribute('src', 'test-url');
  });
});
