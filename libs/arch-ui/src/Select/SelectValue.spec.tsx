import React from 'react';
import { render, screen } from '@testing-library/react';
import { SelectRoot } from './Select';
import { SelectTrigger } from './SelectTrigger';
import { SelectValue } from './SelectValue';

describe('SelectValue', () => {
  it('displays the placeholder', () => {
    render(<SelectValue value={null} placeholder="Select people" />, {
      wrapper: ({ children }) => (
        <SelectRoot>
          <SelectTrigger variant="bordered">{children}</SelectTrigger>
        </SelectRoot>
      ),
    });

    expect(screen.getByRole('button', { name: 'Select people' })).toBeInTheDocument();
  });

  it('displays the value', () => {
    render(<SelectValue value="John Doe" placeholder="Select people" />, {
      wrapper: ({ children }) => (
        <SelectRoot>
          <SelectTrigger variant="bordered">{children}</SelectTrigger>
        </SelectRoot>
      ),
    });

    expect(screen.getByRole('button', { name: '<PERSON>' })).toBeInTheDocument();
  });
});
