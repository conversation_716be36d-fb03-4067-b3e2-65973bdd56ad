import React from 'react';
import { render, screen, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { getKey, SelectMultipleValue } from './SelectMultipleValue';

describe('SelectMultipleValue', () => {
  describe('when generating the iterator keys', () => {
    it('produces correct keys', () => {
      expect(getKey({ id: 0 })).toBe('0');
      expect(getKey({ id: 3 })).toBe('3');
      expect(getKey({ id: 34 })).toBe('34');
      expect(getKey({ id: '' })).toBe('');
      expect(getKey({ id: null })).toBe('');
      expect(getKey({ id: undefined })).toBe('');
      expect(getKey({ id: 'guid' })).toBe('guid');
      expect(getKey({ other: 32 })).toBe('');
      expect(getKey({})).toBe('');
      expect(getKey(null)).toBe('');
      expect(getKey(undefined)).toBe('');
    });
  });

  describe('when condensed', () => {
    it('does not show the remove and remove all options', () => {
      render(<SelectMultipleValue condensed value={['John']} />);

      expect(screen.queryByRole('button', { name: 'remove all' })).not.toBeInTheDocument();
      expect(screen.getByRole('status')).not.toHaveTextContent('John');
    });

    it('only displays the selection count', () => {
      render(<SelectMultipleValue condensed value={['John', 'Marie']} />);

      expect(screen.getByRole('status')).not.toHaveTextContent('John');
      expect(screen.getByRole('status')).not.toHaveTextContent('Marie');
      expect(screen.getByRole('status')).toHaveTextContent('2');
    });
  });

  it('render the selection option as Badge', () => {
    render(<SelectMultipleValue<string> onRemove={jest.fn} onRemoveAll={jest.fn} value={['John']} />);

    expect(screen.getByRole('status')).toHaveTextContent('John');
  });

  it('shows multiple selected options', () => {
    render(<SelectMultipleValue<string> onRemove={jest.fn} onRemoveAll={jest.fn} value={['John', 'Doe', 'Amilton']} />);

    const badges = screen.getAllByRole('status');
    expect(badges).toHaveLength(3);
    expect(badges[0]).toHaveTextContent('John');
    expect(badges[1]).toHaveTextContent('Doe');
    expect(badges[2]).toHaveTextContent('Amilton');
  });

  it('show limited selected options', () => {
    render(
      <SelectMultipleValue<string>
        onRemove={jest.fn}
        onRemoveAll={jest.fn}
        limit={2}
        value={['John', 'Doe', 'Amilton', 'Another']}
      />
    );

    const badges = screen.getAllByRole('status');
    expect(badges).toHaveLength(3);
    expect(badges[0]).toHaveTextContent('John');
    expect(badges[1]).toHaveTextContent('Doe');
    expect(badges[2]).toHaveTextContent('+2');
  });

  it('triggers onRemove', async () => {
    const spyOnRemove = jest.fn();
    render(
      <SelectMultipleValue<string>
        onRemove={spyOnRemove}
        onRemoveAll={jest.fn}
        limit={2}
        value={['John', 'Doe', 'Amilton', 'Another']}
      />
    );

    const [firstBadge] = screen.getAllByRole('status');
    await userEvent.click(within(firstBadge).getByRole('button'));

    expect(spyOnRemove).toBeCalledWith('John');
  });

  it('triggers onRemoveAll', async () => {
    const spyOnRemoveAll = jest.fn();
    render(
      <SelectMultipleValue<string>
        onRemove={jest.fn}
        onRemoveAll={spyOnRemoveAll}
        limit={2}
        value={['John', 'Doe', 'Amilton', 'Another']}
      />
    );

    await userEvent.click(screen.getByRole('button', { name: 'remove all' }));

    expect(spyOnRemoveAll).toBeCalled();
  });
});
