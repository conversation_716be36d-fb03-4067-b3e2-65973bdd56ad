import React from 'react';
import { render, screen } from '@testing-library/react';
import { SelectRoot } from './Select';
import { SelectTrigger } from './SelectTrigger';
import { SelectValue } from './SelectValue';

describe('SelectTrigger', () => {
  it('renders the prefix slot', () => {
    render(
      <SelectRoot>
        <SelectTrigger variant="bordered" startAdornment={<>I am the prefix</>}>
          <SelectValue value="John Doe" placeholder="Select people" />
        </SelectTrigger>
      </SelectRoot>
    );

    expect(screen.getByText('I am the prefix')).toBeInTheDocument();
  });

  it('renders the suffix slot', () => {
    render(
      <SelectRoot>
        <SelectTrigger variant="bordered" endAdornment={<>I am the suffix</>}>
          <SelectValue value="John Doe" placeholder="Select people" />
        </SelectTrigger>
      </SelectRoot>
    );

    expect(screen.getByText('I am the suffix')).toBeInTheDocument();
  });
});
