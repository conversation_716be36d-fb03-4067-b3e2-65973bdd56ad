import React, { useState } from 'react';
import { render, screen, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { SelectRoot, type SelectRootProps } from './Select';
import { SelectMultipleValue } from './SelectMultipleValue';
import { SelectOption, SelectOptionText } from './SelectOption';
import { SelectPanel } from './SelectPanel';
import { SelectTrigger } from './SelectTrigger';
import { SelectValue } from './SelectValue';

const people = [
  { id: 1, name: '<PERSON><PERSON><PERSON>' },
  { id: 2, name: '<PERSON><PERSON>' },
  { id: 3, name: '<PERSON><PERSON>' },
  { id: 4, name: '<PERSON>' },
  { id: 5, name: '<PERSON><PERSON>' },
];
type People = (typeof people)[number];

const SelectPeople = () => {
  const [selectedPerson, setSelectedPerson] = useState<People['name'] | null>(null);

  return (
    <SelectRoot value={selectedPerson} onChange={setSelectedPerson}>
      <SelectTrigger variant="bordered">
        <SelectValue value={selectedPerson} placeholder="Select people" />
      </SelectTrigger>
      <SelectPanel>
        {people.map((person) => (
          <SelectOption key={person.id} value={person.name}>
            <SelectOptionText>
              {person.id} - {person.name}
            </SelectOptionText>
          </SelectOption>
        ))}
      </SelectPanel>
    </SelectRoot>
  );
};

describe('Select', () => {
  it('opens the options list when click the trigger', async () => {
    render(<SelectPeople />);

    await userEvent.click(screen.getByRole('button', { name: 'Select people' }));

    expect(screen.getAllByRole('option')).toHaveLength(5);
  });

  it('displays the selected option in the trigger and set the option selected in the list', async () => {
    render(<SelectPeople />);

    await userEvent.click(screen.getByRole('button', { name: 'Select people' }));
    await userEvent.selectOptions(screen.getByRole('listbox'), [
      screen.getByRole('option', { name: /Therese Wunsch/ }),
    ]);

    expect(screen.getByRole('button', { name: 'Therese Wunsch' })).toBeInTheDocument();

    await userEvent.click(screen.getByRole('button', { name: 'Therese Wunsch' }));
    expect(screen.getByRole('option', { name: /Therese Wunsch/, selected: true })).toBeInTheDocument();
  });
});

const MultiSelectPeople = (props: Omit<SelectRootProps<People['name'][], true>, 'multiple'>) => {
  const [selectedPerson, setSelectedPerson] = useState<People['name'][]>([]);

  return (
    <SelectRoot {...props} multiple value={selectedPerson} onChange={setSelectedPerson}>
      <SelectTrigger variant="bordered">
        <SelectMultipleValue value={selectedPerson} placeholder="Select people" />
      </SelectTrigger>
      <SelectPanel>
        {people.map((person) => (
          <SelectOption key={person.id} value={person.name}>
            <SelectOptionText>
              {person.id} - {person.name}
            </SelectOptionText>
          </SelectOption>
        ))}
      </SelectPanel>
    </SelectRoot>
  );
};

describe('Multi select', () => {
  it('displays the selected options in the trigger', async () => {
    render(<MultiSelectPeople />);

    await userEvent.click(screen.getByRole('button', { name: 'Select people' }));
    await userEvent.selectOptions(screen.getByRole('listbox'), [
      screen.getByRole('option', { name: /Therese Wunsch/ }),
    ]);
    await userEvent.selectOptions(screen.getByRole('listbox'), [
      screen.getByRole('option', { name: /Benedict Kessler/ }),
    ]);
    await userEvent.type(document.body, '{escape}');

    const [firstBadge, secondBadge] = within(await screen.findByRole('button')).getAllByRole('status');
    expect(firstBadge).toHaveTextContent('Therese Wunsch');
    expect(secondBadge).toHaveTextContent('Benedict Kessler');
  });

  it('invokes onClose', async () => {
    const spyOnClose = jest.fn();
    render(<MultiSelectPeople onClose={spyOnClose} />);

    await userEvent.click(screen.getByRole('button', { name: 'Select people' }));
    expect(await screen.findByRole('listbox')).toBeInTheDocument();
    await userEvent.selectOptions(await screen.findByRole('listbox'), [
      screen.getByRole('option', { name: /Benedict Kessler/ }),
    ]);
    await userEvent.type(screen.getByRole('listbox'), '{escape}');

    await waitFor(() => expect(spyOnClose).toHaveBeenCalled());
  });
});
