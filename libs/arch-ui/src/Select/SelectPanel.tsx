import React, { type ElementType, useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { Listbox, type ListboxOptionsProps } from '@headlessui/react';
import { beforeWrite, type Placement, type PositioningStrategy } from '@popperjs/core';
import { useMediaQuery } from '@shape-construction/hooks';
import { type Modifier, usePopper } from 'react-popper';
import { breakpoints } from '../utils/breakpoints';
import { cn } from '../utils/classes';
import type { PrimitiveProps } from '../utils/render';
import { useSelectContext } from './SelectContext';

type PopperProps = {
  strategy?: PositioningStrategy;
  placement?: Placement;
};

const adaptiveWidthModifier = {
  name: 'adaptiveWidth',
  enabled: true,
  phase: beforeWrite,
  fn: () => {},
  effect: ({ state }) => {
    const reference = state.elements.reference as HTMLElement;
    state.elements.popper.style.width = `${reference.offsetWidth}px`;
  },
} as const satisfies Modifier<string, any>;

export type SelectPanelProps<TTag extends ElementType> = PrimitiveProps<ListboxOptionsProps<TTag>> &
  PopperProps & {
    show?: boolean;
    portal?: boolean;
    // Defaults to true, set to false to disable adapting panel width to reference (adaptiveWidthModifier)
    matchReferenceWidth?: boolean;
  };

export function SelectPanel<TTag extends ElementType>({
  className,
  placement = 'bottom-start',
  strategy = 'absolute',
  portal,
  matchReferenceWidth = true,
  ...props
}: SelectPanelProps<TTag>) {
  const isLargeScreen = useMediaQuery(breakpoints.up('md'));
  const { triggerRef } = useSelectContext();
  const [popperElement, setPopperElement] = useState<HTMLElement | null>(null);
  const { styles, attributes } = usePopper(triggerRef.current, popperElement, {
    strategy,
    placement,
    modifiers: matchReferenceWidth ? [adaptiveWidthModifier] : undefined,
  });

  useEffect(() => {
    if (popperElement && !isLargeScreen) document.body.style.overflow = 'hidden';
    else document.body.style.overflow = 'unset';

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isLargeScreen, popperElement]);

  const listboxOptions = (
    <Listbox.Options
      {...props}
      data-testid="select-panel"
      ref={setPopperElement}
      style={props.style || styles.popper}
      {...attributes.popper}
      className={cn(
        'absolute z-popover mt-1.5 flex flex-col min-w-full md:min-w-60',
        'bg-white rounded-md ring-1 ring-black/5 shadow-lg outline-hidden overflow-hidden',
        'divide-y divide-gray-100',
        portal && 'z-select',
        className
      )}
    />
  );

  if (!portal) return listboxOptions;

  return createPortal(listboxOptions, document.body);
}

SelectPanel.displayName = 'Select.Panel';
