import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Popover } from './Popover';

describe('Popover', () => {
  describe('when open is true', () => {
    it('shows the trigger', async () => {
      render(
        <Popover open>
          <Popover.Trigger>
            <button type="button">Trigger</button>
          </Popover.Trigger>
          <Popover.Content onClose={jest.fn()}>
            <Popover.Content.Heading>Popover heading</Popover.Content.Heading>
          </Popover.Content>
        </Popover>
      );

      expect(await screen.findAllByRole('button', { name: 'Trigger' })).toHaveLength(2);
    });

    it('shows the popover', async () => {
      render(
        <Popover open>
          <Popover.Trigger>
            <button type="button">Trigger</button>
          </Popover.Trigger>
          <Popover.Content onClose={jest.fn()}>
            <Popover.Content.Heading>Popover heading</Popover.Content.Heading>
          </Popover.Content>
        </Popover>
      );

      expect(await screen.findByRole('dialog', { name: 'Popover heading' })).toBeInTheDocument();
    });
  });

  describe('when open is false', () => {
    it('shows the trigger', async () => {
      render(
        <Popover>
          <Popover.Trigger>
            <button type="button">Trigger</button>
          </Popover.Trigger>
          <Popover.Content onClose={jest.fn()}>
            <Popover.Content.Heading>Popover heading</Popover.Content.Heading>
          </Popover.Content>
        </Popover>
      );

      expect(await screen.findAllByRole('button', { name: 'Trigger' })).toHaveLength(2);
    });

    it('does not show the popover', async () => {
      render(
        <Popover>
          <Popover.Trigger>
            <button type="button">Trigger</button>
          </Popover.Trigger>
          <Popover.Content onClose={jest.fn()}>
            <Popover.Content.Heading>Popover heading</Popover.Content.Heading>
          </Popover.Content>
        </Popover>
      );

      expect(screen.queryByRole('dialog', { name: 'Popover heading' })).not.toBeInTheDocument();
    });
  });

  describe('with onClose', () => {
    it('shows close button', async () => {
      render(
        <Popover open>
          <Popover.Trigger>
            <button type="button">Trigger</button>
          </Popover.Trigger>
          <Popover.Content>
            <Popover.Content.Heading>Popover heading</Popover.Content.Heading>
          </Popover.Content>
        </Popover>
      );

      expect(screen.queryByRole('button', { name: 'popover-close-icon' })).not.toBeInTheDocument();
    });

    describe('when user clicks the close button', () => {
      it('calls onClose', async () => {
        const onClose = jest.fn();
        render(
          <Popover open>
            <Popover.Trigger>
              <button type="button">Trigger</button>
            </Popover.Trigger>
            <Popover.Content onClose={onClose}>Popover content</Popover.Content>
          </Popover>
        );

        await userEvent.click(await screen.findByRole('button', { name: 'popover-close-icon' }));

        expect(onClose).toBeCalled();
      });
    });
  });

  describe('without onClose', () => {
    it('does not show the close button', async () => {
      render(
        <Popover open>
          <Popover.Trigger>
            <button type="button">Trigger</button>
          </Popover.Trigger>
          <Popover.Content>
            <Popover.Content.Heading>Popover heading</Popover.Content.Heading>
          </Popover.Content>
        </Popover>
      );

      expect(screen.queryByRole('button', { name: 'popover-close-icon' })).not.toBeInTheDocument();
    });
  });
});
