import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ModalManagerProvider, useModalManager } from './ModalManager';

const ModalComponent = ({ onClose }: { onClose: () => void }) => (
  <div>
    <span>First modal</span>
    <button type="button" onClick={onClose}>
      close modal
    </button>
  </div>
);

const modals = {
  'first-modal': ModalComponent,
};

describe('ModalManager', () => {
  describe('when openModal is called', () => {
    it('opens modal', async () => {
      const TestComponent = () => {
        const { openModal, closeModal } = useModalManager<typeof modals>();

        return (
          <button
            type="button"
            onClick={() => {
              openModal('first-modal', {
                onClose: () => closeModal('first-modal'),
              });
            }}
          >
            open modal
          </button>
        );
      };
      render(
        <ModalManagerProvider modals={modals}>
          <TestComponent />
        </ModalManagerProvider>
      );

      await userEvent.click(screen.getByRole('button', { name: 'open modal' }));

      expect(await screen.findByText('First modal')).toBeInTheDocument();
    });
  });

  describe('when modal is open', () => {
    describe('when closeModal is called', () => {
      it('closes modal', async () => {
        const TestComponent = () => {
          const { openModal, closeModal } = useModalManager<typeof modals>();

          return (
            <button
              type="button"
              onClick={() => {
                openModal('first-modal', {
                  onClose: () => closeModal('first-modal'),
                });
              }}
            >
              open modal
            </button>
          );
        };
        render(
          <ModalManagerProvider modals={modals}>
            <TestComponent />
          </ModalManagerProvider>
        );
        await userEvent.click(screen.getByRole('button', { name: 'open modal' }));

        await userEvent.click(await screen.findByRole('button', { name: 'close modal' }));

        expect(screen.queryByText('First modal')).not.toBeInTheDocument();
      });
    });
  });
});
