import React from 'react';
import type { Meta, StoryObj } from '@storybook/react-vite';
import Modal from '../Modal';
import { ModalManagerProvider, useModalManager } from './ModalManager';

type Story = StoryObj<typeof ModalManagerProvider>;
const meta: Meta<typeof ModalManagerProvider> = {
  component: ModalManagerProvider,
  subcomponents: {},
  title: 'Feedback/Modal/ModalManager',
};
export default meta;

const FirstModal: React.FC<{ title: string; onClose: () => void }> = ({ title, onClose }) => {
  return (
    <Modal.Root open onClose={onClose}>
      <Modal.Header onClose={onClose}>
        <Modal.Title>{title}</Modal.Title>
      </Modal.Header>
      <Modal.Content>{title}</Modal.Content>
    </Modal.Root>
  );
};

const SecondModal: React.FC<{ title: string }> = ({ title }) => {
  const { closeModal } = useModalManagerTyped();
  const onClose = () => closeModal('second-modal');

  return (
    <Modal.Root open onClose={onClose}>
      <Modal.Header onClose={onClose}>
        <Modal.Title>{title}</Modal.Title>
      </Modal.Header>
      <Modal.Content>{title}</Modal.Content>
    </Modal.Root>
  );
};

const modals = {
  'first-modal': FirstModal,
  'second-modal': SecondModal,
};

const useModalManagerTyped = useModalManager<typeof modals>;

const App: React.FC = () => {
  const { openModal, closeModal } = useModalManagerTyped();

  return (
    <div className="flex flex-col gap-4">
      <button
        type="button"
        onClick={() =>
          openModal('first-modal', {
            title: 'First modal',
            onClose: () => closeModal('first-modal'),
          })
        }
      >
        Open first modal
      </button>
      <button type="button" onClick={() => openModal('second-modal', { title: 'Second modal' })}>
        Open second modal
      </button>
    </div>
  );
};

const Template = () => (
  <ModalManagerProvider modals={modals}>
    <App />
  </ModalManagerProvider>
);

export const Default: Story = {
  render: Template,
};
