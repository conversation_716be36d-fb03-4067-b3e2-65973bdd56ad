import React, { type ComponentProps, type PropsWithChildren } from 'react';
import { initialiseAxiosClient } from '@shape-construction/api/client';
import {
  booleanFlagFactory,
  featureFlagsFactory,
  variantFlagFactory,
} from '@shape-construction/api/factories/feature-flags';
import { getApiFeatureFlagsMockHandler } from '@shape-construction/api/handlers-factories/feature-flags';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { renderHook, waitFor } from '@testing-library/react';
import axios from 'axios';
import { setupServer } from 'msw/node';
import { FeatureFlagEntityProvider } from './context';
import { useFeatureFlag } from './useFeatureFlag';
import { booleanFlag, variantFlag } from './utils/configHelpers';

const server = setupServer();

initialiseAxiosClient(axios.create());

jest.mock('../featureFlags.config', () => ({
  get FEATURE_FLAGS_CONFIG() {
    return {};
  },
}));

const createContextWrapper = (props: ComponentProps<typeof FeatureFlagEntityProvider>) => {
  const queryClient = new QueryClient();

  return ({ children }: PropsWithChildren) => {
    return (
      <QueryClientProvider client={queryClient}>
        <FeatureFlagEntityProvider {...props}>{children}</FeatureFlagEntityProvider>
      </QueryClientProvider>
    );
  };
};

describe('useFeatureFlag', () => {
  const config = jest.spyOn(jest.requireMock('../featureFlags.config'), 'FEATURE_FLAGS_CONFIG', 'get');

  beforeAll(() => {
    server.listen();
  });

  afterAll(() => {
    server.close();
  });

  beforeEach(() => {
    server.resetHandlers();
    config.mockClear();
  });

  describe('when using flag with target user', () => {
    describe('when a boolean flag is present', () => {
      it('returns the value of the flag', async () => {
        config.mockReturnValue({ flagBoolean1: booleanFlag({ expiry: '2020-20-20' }) });
        const flagData = featureFlagsFactory({
          user: [booleanFlagFactory('flagBoolean1', true)],
        });
        server.use(getApiFeatureFlagsMockHandler(() => flagData));
        const wrapper = createContextWrapper({ userId: '123' });

        const { result } = renderHook(() => useFeatureFlag<any>('flagBoolean1'), { wrapper });

        await waitFor(() => expect(result.current).toEqual({ value: true }));
      });
    });

    describe('when a boolean flags is not present', () => {
      describe('when the default value is not provided', () => {
        it('returns false (boolean default)', () => {
          config.mockReturnValue({ flagBoolean: booleanFlag({ expiry: '2020-20-20' }) });
          server.use(getApiFeatureFlagsMockHandler(() => featureFlagsFactory()));
          const wrapper = createContextWrapper({ userId: '123' });

          const { result } = renderHook(() => useFeatureFlag<any>('flagBoolean'), { wrapper });

          expect(result.current).toEqual({ value: false });
        });
      });

      describe('when the default value is provided', () => {
        it('returns the provided default', () => {
          config.mockReturnValue({ flagBoolean: booleanFlag({ expiry: '2020-20-20' }) });
          server.use(getApiFeatureFlagsMockHandler(() => featureFlagsFactory()));
          const wrapper = createContextWrapper({ userId: '123' });

          const { result } = renderHook(() => useFeatureFlag<any>('flagBoolean', false), { wrapper });

          expect(result.current).toEqual({ value: false });
        });
      });

      describe('when the default value is configured in config', () => {
        it('returns the configured default', () => {
          config.mockReturnValue({
            flagBoolean: booleanFlag({ expiry: '2020-20-20', default: true }),
          });
          server.use(getApiFeatureFlagsMockHandler(() => featureFlagsFactory()));
          const wrapper = createContextWrapper({ userId: '123' });

          const { result } = renderHook(() => useFeatureFlag<any>('flagBoolean'), { wrapper });

          expect(result.current).toEqual({ value: true });
        });
      });
    });

    describe('when a variant flag is present', () => {
      it('returns the value of the flag along with the attachment', async () => {
        config.mockReturnValue({ flagVariant1: variantFlag({ expiry: '2020-20-20' }) });
        const flagData = featureFlagsFactory({
          user: [variantFlagFactory('flagVariant1', 'B', '{"u": 1}')],
        });
        server.use(getApiFeatureFlagsMockHandler(() => flagData));
        const wrapper = createContextWrapper({ userId: '123' });

        const { result } = renderHook(() => useFeatureFlag<any>('flagVariant1'), { wrapper });

        await waitFor(() => expect(result.current).toEqual({ value: 'B', attachment: '{"u": 1}' }));
      });
    });

    describe('when a variant flag is not present', () => {
      describe('when the default value is not provided', () => {
        it('returns an empty string (variant default)', async () => {
          config.mockReturnValue({ flagVariant1: variantFlag({ expiry: '2020-20-20' }) });
          server.use(getApiFeatureFlagsMockHandler(() => featureFlagsFactory()));
          const wrapper = createContextWrapper({ userId: '123' });

          const { result } = renderHook(() => useFeatureFlag<any>('flagVariant1'), { wrapper });

          await waitFor(() => expect(result.current).toEqual({ value: '' }));
        });
      });

      describe('when a default value is provided', () => {
        it('returns the provided default', () => {
          config.mockReturnValue({ flagVariant1: variantFlag({ expiry: '2020-20-20' }) });
          server.use(getApiFeatureFlagsMockHandler(() => featureFlagsFactory()));
          const wrapper = createContextWrapper({ userId: '123' });

          const { result } = renderHook(() => useFeatureFlag<any>('flagVariant1', 'B'), { wrapper });

          expect(result.current).toEqual({ value: 'B' });
        });
      });

      describe('when a default value is configured in config', () => {
        it('returns the configured default', () => {
          config.mockReturnValue({
            flagVariant1: variantFlag({ expiry: '2020-20-20', default: 'C' }),
          });
          server.use(getApiFeatureFlagsMockHandler(() => featureFlagsFactory()));
          const wrapper = createContextWrapper({ userId: '123' });

          const { result } = renderHook(() => useFeatureFlag<any>('flagVariant1'), { wrapper });

          expect(result.current).toEqual({ value: 'C' });
        });
      });

      describe('when allowSessionFallback is set to true', () => {
        it('returns the session flag', async () => {
          config.mockReturnValue({
            flagVariant1: variantFlag({ expiry: '2020-20-20', allowSessionFallback: true }),
          });
          const flagData = featureFlagsFactory({
            user: [],
            session: [variantFlagFactory('flagVariant1', 'A', '{"s": 1}')],
          });
          server.use(getApiFeatureFlagsMockHandler(() => flagData));
          const wrapper = createContextWrapper({ userId: '123' });

          const { result } = renderHook(() => useFeatureFlag<any>('flagVariant1'), { wrapper });

          await waitFor(() => expect(result.current).toEqual({ value: 'A', attachment: '{"s": 1}' }));
        });
      });
    });
  });

  describe('when using flag with target team', () => {
    const teamId1 = 'team-1';
    const teamId2 = 'team-2';

    describe('when team is present', () => {
      it('returns value of the flag', async () => {
        config.mockReturnValue({
          flagVariant1: variantFlag({ expiry: '2020-20-20', target: 'team' }),
        });
        const flagData = featureFlagsFactory({
          teams: {
            [teamId1]: [variantFlagFactory('flagVariant1', 'T1')],
          },
        });
        server.use(getApiFeatureFlagsMockHandler(() => flagData));
        const wrapper = createContextWrapper({ userId: 'user-123', teamId: teamId1 });

        const { result } = renderHook(() => useFeatureFlag<any>('flagVariant1'), { wrapper });

        await waitFor(() => expect(result.current).toEqual({ value: 'T1', attachment: '' }));
      });
    });

    describe('when user switches teams', () => {
      it('returns value of the flag for the right team', async () => {
        config.mockReturnValue({
          flagVariant1: variantFlag({ expiry: '2020-20-20', target: 'team' }),
        });
        const flagData = featureFlagsFactory({
          teams: {
            [teamId1]: [variantFlagFactory('flagVariant1', 'T1')],
            [teamId2]: [variantFlagFactory('flagVariant1', 'T2')],
          },
        });
        server.use(getApiFeatureFlagsMockHandler(() => flagData));
        let currentTeamId = teamId1;
        const { result, rerender } = renderHook(() => useFeatureFlag<any>('flagVariant1'), {
          wrapper: (props) => {
            const Wrapper = createContextWrapper({ userId: '123', teamId: currentTeamId });
            return <Wrapper {...props} />;
          },
        });
        await waitFor(() => expect(result.current).toEqual({ value: 'T1', attachment: '' }));

        currentTeamId = teamId2;
        rerender();

        await waitFor(() => expect(result.current).toEqual({ value: 'T2', attachment: '' }));
      });
    });

    describe('when team is not present', () => {
      it('returns empty string (variant default)', async () => {
        config.mockReturnValue({
          flagVariant1: variantFlag({ expiry: '2020-20-20', target: 'team' }),
        });
        const flagData = featureFlagsFactory({
          teams: {
            [teamId1]: [variantFlagFactory('flagVariant1', 'T2')],
          },
        });
        server.use(getApiFeatureFlagsMockHandler(() => flagData));
        const wrapper = createContextWrapper({ userId: '123' });

        const { result } = renderHook(() => useFeatureFlag<any>('flagVariant1'), { wrapper });

        await waitFor(() => expect(result.current).toEqual({ value: '' }));
      });

      describe('when default value is provided', () => {
        it('returns provided default', async () => {
          config.mockReturnValue({
            flagVariant1: variantFlag({ expiry: '2020-20-20', target: 'team', default: 'X' }),
          });
          const flagData = featureFlagsFactory({
            teams: {
              [teamId1]: [variantFlagFactory('flagVariant1', 'T2')],
            },
          });
          server.use(getApiFeatureFlagsMockHandler(() => flagData));
          const wrapper = createContextWrapper({ userId: '123' });

          const { result } = renderHook(() => useFeatureFlag<any>('flagVariant1'), { wrapper });

          await waitFor(() => expect(result.current).toEqual({ value: 'X' }));
        });
      });
    });

    describe('when flags for team is not present', () => {
      it('returns empty string (variant default)', () => {
        config.mockReturnValue({
          flagVariant1: variantFlag({ expiry: '2020-20-20', target: 'team' }),
        });
        server.use(getApiFeatureFlagsMockHandler(() => featureFlagsFactory()));
        const wrapper = createContextWrapper({ userId: '123' });

        const { result } = renderHook(() => useFeatureFlag<any>('flagVariant1'), { wrapper });

        expect(result.current).toEqual({ value: '' });
      });

      describe('when allowSessionFallback is set to true', () => {
        it('returns the session flag', async () => {
          config.mockReturnValue({
            flagVariant1: variantFlag({
              expiry: '2020-20-20',
              target: 'team',
              allowSessionFallback: true,
            }),
          });
          const flagData = featureFlagsFactory({
            teams: {},
            session: [variantFlagFactory('flagVariant1', 'A')],
          });
          server.use(getApiFeatureFlagsMockHandler(() => flagData));
          const wrapper = createContextWrapper({ userId: '123' });

          const { result } = renderHook(() => useFeatureFlag<any>('flagVariant1'), { wrapper });

          await waitFor(() => expect(result.current).toEqual({ value: 'A', attachment: '' }));
        });
      });
    });
  });

  describe('when using flag with target session', () => {
    describe('when flag is present', () => {
      it('returns value of the flag', async () => {
        config.mockReturnValue({
          flagVariant1: variantFlag({ expiry: '2020-20-20', target: 'session' }),
        });
        const flagData = featureFlagsFactory({
          session: [variantFlagFactory('flagVariant1', 'A', '{"s": 1}')],
        });
        server.use(getApiFeatureFlagsMockHandler(() => flagData));
        const wrapper = createContextWrapper({ userId: '123' });

        const { result } = renderHook(() => useFeatureFlag<any>('flagVariant1'), { wrapper });

        await waitFor(() => expect(result.current).toEqual({ value: 'A', attachment: '{"s": 1}' }));
      });
    });
  });
});
