import React, { type FC, useState } from 'react';
import type { Context } from '../../../../src';
import style from './ColorPanel.module.css';
import ColorPicker from './ColorPicker/ColorPicker';

type Color = string;
interface ColorPanelProps {
  canvas: NonNullable<Context['canvas']>;
  colors: Color[];
}

const ColorPanel: FC<ColorPanelProps> = (props) => {
  const [state, setState] = useState({
    currentColor: 'black',
    isOpened: false,
  });

  const togglePanel = () => {
    setState((prev) => ({
      ...prev,
      isOpened: !prev.isOpened,
    }));
  };

  const updateColor = (color: Color) => {
    setState((prev) => ({ ...prev, currentColor: color }));
  };

  const colors = () => {
    if (state.isOpened && props.colors) {
      return (
        <div className={style.colorPicker}>
          {props.colors.map((color, i) => (
            <ColorPicker color={color} updateColor={updateColor} key={color} canvas={props.canvas} />
          ))}
        </div>
      );
    }
  };

  return (
    <div className={style.colorPanel} onMouseEnter={() => togglePanel()} onMouseLeave={() => togglePanel()}>
      <div className={style.currentColor} style={state.currentColor ? { backgroundColor: state.currentColor } : {}} />
      {colors()}
    </div>
  );
};
export default ColorPanel;
