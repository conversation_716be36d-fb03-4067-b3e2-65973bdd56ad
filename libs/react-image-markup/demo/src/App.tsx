import React, { useContext, useEffect } from 'react';
import { Editor, EditorContext, EditorContextProvider } from '../../src';
import backgroundAsset from '../assets/horizontal.png';
import HorizontalToolbar from './components/HorizontalToolbar/HorizontalToolbar';
import VerticalToolbar from './components/VerticalToolbar/VerticalToolbar';

import '@fortawesome/fontawesome-free/css/all.css';
import './App.css';

const App = () => {
  const { canvas } = useContext(EditorContext);

  useEffect(() => {
    if (!canvas) return;

    const handleResize = () => {
      canvas.handleResize();
    };

    canvas.tools.setBackgroundImage(backgroundAsset);
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [canvas]);

  return (
    <div className="editor">
      <HorizontalToolbar />
      <div className="container">
        <VerticalToolbar />
        <Editor />
      </div>
    </div>
  );
};

const Root = () => (
  <EditorContextProvider>
    <App />
  </EditorContextProvider>
);

export default Root;
