import React from 'react';
import { render } from '@testing-library/react';
import { Canvas } from './assets/js/methods';
import { Editor } from './Editor';
import { type Context, EditorContext, initialState } from './EditorContext';

describe('Editor', () => {
  it('initializes canvas', () => {
    const spyOnSetCanvas = jest.fn();
    const contextValues: Context = {
      ...initialState,
      setCanvas: spyOnSetCanvas,
    };

    render(
      <EditorContext.Provider value={contextValues}>
        <Editor />
      </EditorContext.Provider>
    );

    expect(spyOnSetCanvas).toBeCalledTimes(1);
  });

  it('resets editor state, history and canvas reference on unmount', () => {
    const canvas = new Canvas(document.createElement('canvas'));
    const spyOnReset = jest.fn();
    const spyOnSetCanvas = jest.fn();

    canvas.tools.reset = spyOnReset;

    const contextValues: Context = {
      ...initialState,
      canvas,
      setCanvas: spyOnSetCanvas,
    };

    const { unmount } = render(
      <EditorContext.Provider value={contextValues}>
        <Editor />
      </EditorContext.Provider>
    );

    unmount();

    expect(spyOnReset).toBeCalledTimes(1);
    expect(spyOnSetCanvas).toBeCalledTimes(1);
    expect(spyOnSetCanvas).toBeCalledWith(undefined);
  });
});
