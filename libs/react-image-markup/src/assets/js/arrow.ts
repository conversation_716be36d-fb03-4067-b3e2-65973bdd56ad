import { fabric } from 'fabric';
import type { <PERSON><PERSON>, IEvent } from 'fabric/fabric-impl';
import type { LineOptions } from '../../types';
import CanvasHistory, { type HistoryInstance } from './canvasHistory';

// @ts-expect-error ts-migrate(2339) FIXME: Property 'LineArrow' does not exist on type 'typeo... Remove this comment to see the full error message
fabric.LineArrow = fabric.util.createClass(fabric.Line, {
  type: 'lineArrow',

  initialize: function (element: any, options = {}) {
    this.callSuper('initialize', element, options);
  },

  toObject: function () {
    // @ts-expect-error ts-migrate(2554) FIXME: Expected 2 arguments, but got 1.
    return fabric.util.object.extend(this.callSuper('toObject'));
  },

  _render: function (ctx: any) {
    this.ctx = ctx;
    this.callSuper('_render', ctx);
    const p = this.calcLinePoints();
    const xDiff = this.x2 - this.x1;
    const yDiff = this.y2 - this.y1;
    const angle = Math.atan2(yDiff, xDiff);
    this.drawArrow(angle, p.x2, p.y2);
  },

  drawArrow: function (angle: any, xPos: any, yPos: any) {
    this.ctx.save();

    this.ctx.translate(xPos, yPos);
    this.ctx.rotate(angle);
    this.ctx.beginPath();
    this.ctx.moveTo(10, 0);
    this.ctx.lineTo(-15, 15);
    this.ctx.lineTo(-15, -15);
    this.ctx.closePath();

    this.ctx.fillStyle = this.stroke;
    this.ctx.fill();
    this.ctx.restore();
  },
});

// @ts-expect-error ts-migrate(2339) FIXME: Property 'LineArrow' does not exist on type 'typeo... Remove this comment to see the full error message
fabric.LineArrow.fromObject = (object: any, callback: any) => {
  if (callback)
    // @ts-expect-error ts-migrate(2339) FIXME: Property 'LineArrow' does not exist on type 'typeo... Remove this comment to see the full error message
    callback(new fabric.LineArrow([object.x1, object.y1, object.x2, object.y2], object));
};
// @ts-expect-error ts-migrate(2339) FIXME: Property 'LineArrow' does not exist on type 'typeo... Remove this comment to see the full error message
fabric.LineArrow.async = true;

type ClassDefinition = {
  new (canvas: Canvas, draggable: boolean, params?: LineOptions): Instance;
};

type Instance = {
  canvas: Canvas;
  canvasHistory: HistoryInstance;
  className: string;
  isDrawing: boolean;
  bindEvents: () => void;
};

const LineArrow = (() => {
  let drag: boolean;
  let color: LineOptions['stroke'];
  let lineWidth: LineOptions['strokeWidth'];
  let properties: LineOptions | undefined;

  function Arrow(this: Instance, canvas: Canvas, draggable = false, params?: LineOptions) {
    if (!draggable) {
      drag = false;
      return Arrow;
    }

    if (params && color && color !== params.stroke) {
      color = params.stroke;

      new LineArrow(canvas, draggable, params);

      return Arrow;
    }

    properties = params;

    if (properties && params) {
      color = params.stroke;
      lineWidth = params.strokeWidth;
    }
    this.canvas = canvas;
    this.canvasHistory = new CanvasHistory(canvas);
    this.className = 'Arrow';
    this.isDrawing = false;
    this.bindEvents();
    drag = draggable;
  }

  Arrow.prototype.bindEvents = function () {
    document.onkeydown = (e) => {
      // @ts-expect-error ts-migrate(2551) FIXME: Property 'keycode' does not exist on type 'Keyboar... Remove this comment to see the full error message
      if (e.which === 46 || e.keycode === 46) {
        this.canvas.getActiveObjects().forEach((obj: any) => {
          this.canvas.remove(obj);
        });
      }
      this.canvas.renderAll();
    };
    this.selectable = true;

    this.canvas.off('mouse:down');
    this.canvas.on('mouse:down', (o: IEvent<MouseEvent>) => {
      this.onMouseDown(o);
    });
    this.canvas.on('mouse:move', (o: IEvent<MouseEvent>) => {
      this.onMouseMove(o);
    });
    this.canvas.on('mouse:up', (o: IEvent<MouseEvent>) => {
      this.onMouseUp(o);
    });
    this.canvas.on('mouse:over', () => {
      this.onMouseOver();
    });
    this.canvas.on('object:moving', () => {
      this.disable();
    });
  };

  Arrow.prototype.onMouseUp = function () {
    if (!this.isEnable()) {
      return;
    }
    if (drag) {
      this.line.set({
        dirty: true,
        objectCaching: true,
      });
      if (this.canvas.getActiveObject()) {
        this.canvas.getActiveObject().hasControls = false;
        this.canvas.getActiveObject().hasBorders = false;
        this.canvas.getActiveObject().lockMovementX = true;
        this.canvas.getActiveObject().lockMovementY = true;
        this.canvas.getActiveObject().lockUniScaling = true;
      }
      this.canvas.renderAll();

      this.canvasHistory.add();
    }
    this.disable();
  };

  Arrow.prototype.onMouseMove = function (o: IEvent<MouseEvent>) {
    this.canvas.selection = false;
    if (!this.isEnable()) {
      return;
    }
    const pointer = this.canvas.getPointer(o.e);
    const activeObj = this.canvas.getActiveObject();
    activeObj.set({
      x2: pointer.x,
      y2: pointer.y,
    });
    activeObj.setCoords();
    this.canvas.renderAll();
  };

  Arrow.prototype.onMouseDown = function (o: IEvent<MouseEvent>) {
    if (!drag) {
      if (this.canvas.getActiveObject()) {
        this.canvas.getActiveObject().hasControls = true;
        this.canvas.getActiveObject().hasBorders = true;
        this.canvas.getActiveObject().lockMovementX = false;
        this.canvas.getActiveObject().lockMovementY = false;
        this.canvas.getActiveObject().lockUniScaling = false;
        this.canvas.renderAll();
      }
      this.disable();
      return;
    }
    this.enable();
    if (this.canvas.getActiveObject()) {
      this.canvas.getActiveObject().hasControls = false;
      this.canvas.getActiveObject().hasBorders = false;
      this.canvas.getActiveObject().lockMovementX = true;
      this.canvas.getActiveObject().lockMovementY = true;
      this.canvas.getActiveObject().lockUniScaling = true;
      this.canvas.renderAll();
    }
    const pointer = this.canvas.getPointer(o.e);
    const points = [pointer.x, pointer.y, pointer.x, pointer.y];
    // @ts-expect-error ts-migrate(2339) FIXME: Property 'LineArrow' does not exist on type 'typeo... Remove this comment to see the full error message
    this.line = new fabric.LineArrow(points, {
      strokeWidth: lineWidth,
      fill: color,
      stroke: color,
      originX: 'center',
      originY: 'center',
      hasBorders: false,
      hasControls: false,
      objectCaching: false,
      perPixelTargetFind: true,
    });

    this.canvas.add(this.line).setActiveObject(this.line);
  };

  Arrow.prototype.onMouseOver = function () {
    if (this.canvas.getActiveObject()) {
      this.canvas.getActiveObject().hoverCursor = 'default';
      this.canvas.renderAll();
    }
  };

  Arrow.prototype.isEnable = function () {
    return this.isDrawing;
  };

  Arrow.prototype.enable = function () {
    this.isDrawing = true;
  };

  Arrow.prototype.disable = function () {
    this.isDrawing = false;
  };

  return Arrow;
})() as unknown as ClassDefinition;

export default LineArrow;
