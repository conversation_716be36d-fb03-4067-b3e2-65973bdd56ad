import React, { useCallback, useContext, useEffect, useRef } from 'react';
import type { ICanvasOptions } from 'fabric/fabric-impl';
import { Canvas } from './assets/js/methods';
import { EditorContext } from './EditorContext';

export type EditorProps = React.HTMLAttributes<HTMLDivElement> & {
  options?: ICanvasOptions;
};

export const Editor = ({ options, style }: EditorProps) => {
  const { canvas, setCanvas } = useContext(EditorContext);
  const canvasEl = useRef<HTMLCanvasElement>(null);

  const initCanvas = useCallback(() => {
    const element = canvasEl.current!;

    if (element.width && element.height) {
      // When used inside an element that have height initially
      setCanvas(new Canvas(element!, options).init());
      return;
    }

    // When used inside an element that does not have height initially
    const containerSizeObserver = new ResizeObserver(([{ contentRect }], observer) => {
      const { width, height } = contentRect;
      if (!width || !height) return;

      setCanvas(new Canvas(element!, options).init());
      observer.disconnect();
    });

    containerSizeObserver.observe(element);
  }, [options, setCanvas]);

  useEffect(() => {
    if (canvas) return;
    if (!canvasEl.current) return;

    initCanvas();
  }, [canvas, initCanvas, canvasEl, setCanvas]);

  /**
   * On unmounting editor component, it will dispose canvas (clearing attached events),
   * clears history and state and clears canvas reference from context
   */
  useEffect(
    () => () => {
      if (!canvas) return;

      canvas.tools.reset();
      setCanvas(undefined);
    },
    [canvas, setCanvas]
  );

  return (
    <div style={{ height: '100%', width: '100%', ...style }}>
      <canvas role="img" aria-label="image editor" ref={canvasEl} />
    </div>
  );
};
